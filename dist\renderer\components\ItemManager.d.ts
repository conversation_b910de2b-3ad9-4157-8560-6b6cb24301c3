/**
 * Item Manager Component - Manages imported clothing items
 */
import React from 'react';
interface ClothingItem {
    id: string;
    name: string;
    type: 'male' | 'female' | 'prop';
    slot: string;
    drawable: number;
    texture: number;
    size: number;
    triangles: number;
    quality: 'good' | 'warning' | 'error';
    hasConflicts: boolean;
    imported: Date;
}
interface ItemManagerProps {
    items: ClothingItem[];
    onItemSelect?: (item: ClothingItem) => void;
    onItemDelete?: (itemId: string) => void;
    onItemEdit?: (item: ClothingItem) => void;
}
export declare const ItemManager: React.FC<ItemManagerProps>;
export {};
