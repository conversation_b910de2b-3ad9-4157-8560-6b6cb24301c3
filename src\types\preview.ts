/**
 * 3D Preview and rendering system types
 */

import { Gender, Component, Prop, AnimationPose } from './core';
import { Vector3, Matrix4 } from './assets';

export interface PreviewScene {
  id: string;
  camera: CameraSettings;
  lighting: LightingSetup;
  environment: EnvironmentSettings;
  models: PreviewModel[];
  animations: AnimationState[];
}

export interface CameraSettings {
  position: Vector3;
  target: Vector3;
  fov: number;
  near: number;
  far: number;
  type: 'perspective' | 'orthographic';
}

export interface LightingSetup {
  ambient: LightSource;
  directional: LightSource[];
  point: LightSource[];
  spot: LightSource[];
  environment?: string; // HDR environment map path
}

export interface LightSource {
  type: 'ambient' | 'directional' | 'point' | 'spot';
  color: Color;
  intensity: number;
  position?: Vector3;
  direction?: Vector3;
  distance?: number;
  decay?: number;
  angle?: number;
  penumbra?: number;
  castShadow: boolean;
}

export interface Color {
  r: number;
  g: number;
  b: number;
  a?: number;
}

export interface EnvironmentSettings {
  background: 'color' | 'gradient' | 'skybox' | 'hdri';
  backgroundValue: Color | string;
  fog?: FogSettings;
  postProcessing: PostProcessingSettings;
}

export interface FogSettings {
  enabled: boolean;
  color: Color;
  near: number;
  far: number;
  density?: number;
}

export interface PostProcessingSettings {
  enabled: boolean;
  bloom: boolean;
  ssao: boolean;
  antialiasing: 'none' | 'fxaa' | 'smaa' | 'msaa';
  toneMappingExposure: number;
}

export interface PreviewModel {
  id: string;
  type: 'base' | 'clothing' | 'prop';
  gender: Gender;
  slot?: Component | Prop;
  meshPath: string;
  texturePaths: string[];
  transform: Matrix4;
  visible: boolean;
  opacity: number;
  material: MaterialSettings;
}

export interface MaterialSettings {
  type: 'standard' | 'physical' | 'toon' | 'wireframe';
  albedo: Color;
  metallic: number;
  roughness: number;
  normal?: string;
  emission?: Color;
  emissionIntensity: number;
  transparent: boolean;
  alphaTest: number;
  doubleSided: boolean;
}

export interface AnimationState {
  modelId: string;
  pose: AnimationPose;
  frame: number;
  duration: number;
  loop: boolean;
  speed: number;
  blendWeight: number;
}

export interface OutfitConfiguration {
  id: string;
  name: string;
  gender: Gender;
  components: Record<Component, ComponentSelection>;
  props: Record<Prop, PropSelection>;
  baseModel: string;
}

export interface ComponentSelection {
  itemId?: string;
  drawable: number;
  texture: number;
  enabled: boolean;
}

export interface PropSelection {
  itemId?: string;
  drawable: number;
  texture: number;
  enabled: boolean;
}

export interface RenderSettings {
  width: number;
  height: number;
  pixelRatio: number;
  antialias: boolean;
  shadows: boolean;
  shadowMapSize: number;
  physicallyCorrectLights: boolean;
  toneMapping: 'none' | 'linear' | 'reinhard' | 'cineon' | 'aces';
  outputEncoding: 'linear' | 'srgb';
}

export interface ViewportControls {
  enabled: boolean;
  enableRotate: boolean;
  enableZoom: boolean;
  enablePan: boolean;
  autoRotate: boolean;
  autoRotateSpeed: number;
  minDistance: number;
  maxDistance: number;
  minPolarAngle: number;
  maxPolarAngle: number;
}

export interface ScreenshotOptions {
  width: number;
  height: number;
  format: 'png' | 'jpg' | 'webp';
  quality: number;
  transparent: boolean;
  multiplier: number; // For high-res screenshots
}

export interface PreviewPreset {
  id: string;
  name: string;
  description: string;
  camera: CameraSettings;
  lighting: LightingSetup;
  environment: EnvironmentSettings;
  pose: AnimationPose;
  thumbnail?: string;
}

export interface ModelLoadingState {
  modelId: string;
  status: 'loading' | 'loaded' | 'error';
  progress: number;
  error?: string;
}

export interface PreviewStats {
  triangles: number;
  vertices: number;
  drawCalls: number;
  textureMemory: number;
  fps: number;
  frameTime: number;
}

export interface ClippingDetectionResult {
  hasClipping: boolean;
  clippingPairs: ClippingPair[];
  severity: 'none' | 'minor' | 'moderate' | 'severe';
  score: number; // 0-100, higher is worse
}

export interface ClippingPair {
  model1: string;
  model2: string;
  intersectionVolume: number;
  intersectionPoints: Vector3[];
  severity: 'minor' | 'moderate' | 'severe';
}

export interface LODLevel {
  level: number;
  distance: number;
  triangleReduction: number;
  meshPath: string;
  enabled: boolean;
}

export interface PreviewEvent {
  type: 'model_loaded' | 'model_error' | 'outfit_changed' | 'pose_changed' | 'screenshot_taken';
  timestamp: Date;
  data: any;
}

// Default presets
export const DEFAULT_CAMERA_PRESETS: Record<string, CameraSettings> = {
  front: {
    position: { x: 0, y: 1.6, z: 3 },
    target: { x: 0, y: 1.6, z: 0 },
    fov: 50,
    near: 0.1,
    far: 1000,
    type: 'perspective'
  },
  side: {
    position: { x: 3, y: 1.6, z: 0 },
    target: { x: 0, y: 1.6, z: 0 },
    fov: 50,
    near: 0.1,
    far: 1000,
    type: 'perspective'
  },
  back: {
    position: { x: 0, y: 1.6, z: -3 },
    target: { x: 0, y: 1.6, z: 0 },
    fov: 50,
    near: 0.1,
    far: 1000,
    type: 'perspective'
  },
  threequarter: {
    position: { x: 2, y: 2, z: 2 },
    target: { x: 0, y: 1.6, z: 0 },
    fov: 50,
    near: 0.1,
    far: 1000,
    type: 'perspective'
  }
};

export const DEFAULT_LIGHTING_PRESETS: Record<string, LightingSetup> = {
  studio: {
    ambient: {
      type: 'ambient',
      color: { r: 0.4, g: 0.4, b: 0.4 },
      intensity: 0.6,
      castShadow: false
    },
    directional: [
      {
        type: 'directional',
        color: { r: 1, g: 1, b: 1 },
        intensity: 1,
        position: { x: 5, y: 10, z: 5 },
        direction: { x: -1, y: -1, z: -1 },
        castShadow: true
      }
    ],
    point: [],
    spot: []
  },
  natural: {
    ambient: {
      type: 'ambient',
      color: { r: 0.5, g: 0.6, b: 0.7 },
      intensity: 0.4,
      castShadow: false
    },
    directional: [
      {
        type: 'directional',
        color: { r: 1, g: 0.95, b: 0.8 },
        intensity: 1.2,
        position: { x: 10, y: 20, z: 5 },
        direction: { x: -0.5, y: -1, z: -0.3 },
        castShadow: true
      }
    ],
    point: [],
    spot: []
  }
};
