/**
 * Asset Ingest Service - Handles importing and processing of GTA V clothing assets
 */
import { ImportResult } from '../../types/assets';
export declare class AssetIngestService {
    private tempDir;
    constructor(tempDir?: string);
    /**
     * Ingest assets from file paths or directories
     */
    ingestAssets(paths: string[], projectId: string): Promise<ImportResult>;
    /**
     * Process a directory recursively
     */
    private processDirectory;
    /**
     * Process an archive file
     */
    private processArchive;
    /**
     * Extract ZIP file
     */
    private extractZip;
    /**
     * Process a single file
     */
    private processFile;
    /**
     * Detect asset type from filename
     */
    private detectAssetType;
    /**
     * Check if slot string represents a component
     */
    private isComponentSlot;
    /**
     * Map slot string to Component
     */
    private mapToComponent;
    /**
     * Map slot string to Prop
     */
    private mapToProp;
    /**
     * Create an Item from file and detection result
     */
    private createItem;
    /**
     * Check for conflicts with existing items
     */
    private checkForConflicts;
    /**
     * Generate unique ID
     */
    private generateId;
}
