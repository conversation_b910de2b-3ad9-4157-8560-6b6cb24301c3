/**
 * Component exports - Simple placeholder components
 */
import React from 'react';
export declare const Sidebar: React.FC;
export declare const MainContent: React.FC<{
    activeTab?: string;
}>;
export declare const StatusBar: React.FC;
export declare const ConsolePanel: React.FC;
export declare const MenuHandler: React.FC;
export declare const PreferencesModal: React.FC<{
    isOpen: boolean;
    onClose: () => void;
}>;
export declare const AboutModal: React.FC<{
    isOpen: boolean;
    onClose: () => void;
}>;
export declare const ProgressModal: React.FC;
