/**
 * Export Service - Handles project export to various formats
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import { Item } from '@/types/core';
import { 
  ExportConfiguration, 
  ExportResult, 
  QBClothingConfig, 
  ESXConfig, 
  FiveMResource,
  FiveMManifest,
  DEFAULT_FIVEM_MANIFEST,
  DEFAULT_QB_CONFIG,
  DEFAULT_ESX_CONFIG
} from '@/types/export';

export class ExportService {
  private tempDir: string;

  constructor(tempDir?: string) {
    this.tempDir = tempDir || path.join(require('os').tmpdir(), 'dripforge-pro', 'exports');
  }

  /**
   * Export project with given configuration
   */
  async exportProject(config: ExportConfiguration, items: Item[]): Promise<ExportResult[]> {
    const results: ExportResult[] = [];

    // Ensure export directory exists
    await fs.ensureDir(config.outputPath);

    // Process each export format
    for (const format of config.formats) {
      if (!format.enabled) continue;

      try {
        const result = await this.exportFormat(format.type, config, items);
        results.push(result);
      } catch (error) {
        results.push({
          format: format.type,
          outputPath: config.outputPath,
          fileCount: 0,
          totalSize: 0,
          success: false,
          errors: [`Export failed: ${error.message}`],
          warnings: []
        });
      }
    }

    return results;
  }

  /**
   * Export to a specific format
   */
  private async exportFormat(
    formatType: string, 
    config: ExportConfiguration, 
    items: Item[]
  ): Promise<ExportResult> {
    switch (formatType) {
      case 'fivem-resource':
        return this.exportFiveMResource(config, items);
      case 'qb-clothing':
        return this.exportQBClothing(config, items);
      case 'esx':
        return this.exportESX(config, items);
      case 'icons':
        return this.exportIcons(config, items);
      case 'marketing':
        return this.exportMarketing(config, items);
      case 'escrow':
        return this.exportEscrow(config, items);
      default:
        throw new Error(`Unsupported export format: ${formatType}`);
    }
  }

  /**
   * Export as FiveM resource
   */
  private async exportFiveMResource(config: ExportConfiguration, items: Item[]): Promise<ExportResult> {
    const resourcePath = path.join(config.outputPath, 'fivem-resource');
    await fs.ensureDir(resourcePath);

    let fileCount = 0;
    let totalSize = 0;
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Create manifest
      const manifest: FiveMManifest = {
        ...DEFAULT_FIVEM_MANIFEST,
        author: config.metadata.author,
        description: config.metadata.description,
        version: config.metadata.version
      };

      // Create stream directory and copy files
      const streamPath = path.join(resourcePath, 'stream');
      await fs.ensureDir(streamPath);

      const streamFiles: string[] = [];

      for (const item of items) {
        // Copy YDD file
        if (item.files.ydd && await fs.pathExists(item.files.ydd)) {
          const fileName = this.generateFileName(item, 'ydd');
          const destPath = path.join(streamPath, fileName);
          await fs.copy(item.files.ydd, destPath);
          streamFiles.push(`stream/${fileName}`);
          
          const stats = await fs.stat(destPath);
          totalSize += stats.size;
          fileCount++;
        }

        // Copy YTD files
        for (const ytdPath of item.files.ytds) {
          if (await fs.pathExists(ytdPath)) {
            const fileName = this.generateFileName(item, 'ytd');
            const destPath = path.join(streamPath, fileName);
            await fs.copy(ytdPath, destPath);
            streamFiles.push(`stream/${fileName}`);
            
            const stats = await fs.stat(destPath);
            totalSize += stats.size;
            fileCount++;
          }
        }
      }

      manifest.files = streamFiles;

      // Write manifest
      const manifestPath = path.join(resourcePath, 'fxmanifest.lua');
      await fs.writeFile(manifestPath, this.generateManifestLua(manifest));
      fileCount++;

      // Create documentation
      if (config.options.includeDocumentation) {
        await this.createDocumentation(resourcePath, items, config);
        fileCount += 3; // README, CHANGELOG, INSTALLATION
      }

    } catch (error) {
      errors.push(`FiveM resource export failed: ${error.message}`);
    }

    return {
      format: 'fivem-resource',
      outputPath: resourcePath,
      fileCount,
      totalSize,
      success: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Export QB-Clothing configuration
   */
  private async exportQBClothing(config: ExportConfiguration, items: Item[]): Promise<ExportResult> {
    const outputPath = path.join(config.outputPath, 'qb-clothing.json');
    
    const qbConfig: QBClothingConfig = {
      male: {},
      female: {}
    };

    // Group items by gender and slot
    const maleItems = items.filter(item => item.gender === 'male');
    const femaleItems = items.filter(item => item.gender === 'female');

    this.populateQBConfig(qbConfig.male, maleItems);
    this.populateQBConfig(qbConfig.female, femaleItems);

    await fs.writeFile(outputPath, JSON.stringify(qbConfig, null, 2));

    const stats = await fs.stat(outputPath);

    return {
      format: 'qb-clothing',
      outputPath,
      fileCount: 1,
      totalSize: stats.size,
      success: true,
      errors: [],
      warnings: []
    };
  }

  /**
   * Export ESX configuration
   */
  private async exportESX(config: ExportConfiguration, items: Item[]): Promise<ExportResult> {
    const outputPath = path.join(config.outputPath, 'esx-config.lua');
    
    const esxConfig = { ...DEFAULT_ESX_CONFIG };
    
    // Create a basic shop configuration
    esxConfig.Shops = [{
      name: 'Clothing Store',
      coords: { x: 72.3, y: -1399.1, z: 29.4 },
      clothes: {
        male: {},
        female: {}
      }
    }];

    // Populate clothing data (simplified)
    const maleItems = items.filter(item => item.gender === 'male');
    const femaleItems = items.filter(item => item.gender === 'female');

    // Convert to ESX format (this would need proper mapping)
    // For now, create a basic structure

    const luaContent = this.generateESXLua(esxConfig);
    await fs.writeFile(outputPath, luaContent);

    const stats = await fs.stat(outputPath);

    return {
      format: 'esx',
      outputPath,
      fileCount: 1,
      totalSize: stats.size,
      success: true,
      errors: [],
      warnings: []
    };
  }

  /**
   * Export icons/thumbnails
   */
  private async exportIcons(config: ExportConfiguration, items: Item[]): Promise<ExportResult> {
    const iconsPath = path.join(config.outputPath, 'icons');
    await fs.ensureDir(iconsPath);

    // This would generate thumbnails using the 3D preview system
    // For now, create placeholder files
    let fileCount = 0;
    let totalSize = 0;

    for (const item of items) {
      const iconName = `${item.gender}_${item.slot}_${item.drawable}_${item.textures[0] || 0}.png`;
      const iconPath = path.join(iconsPath, iconName);
      
      // Create placeholder icon (1x1 transparent PNG)
      const placeholderPng = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
        0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);

      await fs.writeFile(iconPath, placeholderPng);
      totalSize += placeholderPng.length;
      fileCount++;
    }

    return {
      format: 'icons',
      outputPath: iconsPath,
      fileCount,
      totalSize,
      success: true,
      errors: [],
      warnings: ['Icons are placeholder - 3D rendering not yet implemented']
    };
  }

  /**
   * Export marketing materials
   */
  private async exportMarketing(config: ExportConfiguration, items: Item[]): Promise<ExportResult> {
    const marketingPath = path.join(config.outputPath, 'marketing');
    await fs.ensureDir(marketingPath);

    // This would generate high-quality marketing images
    // For now, create placeholder structure
    await fs.ensureDir(path.join(marketingPath, 'individual'));
    await fs.ensureDir(path.join(marketingPath, 'collections'));
    await fs.ensureDir(path.join(marketingPath, 'banners'));

    return {
      format: 'marketing',
      outputPath: marketingPath,
      fileCount: 0,
      totalSize: 0,
      success: true,
      errors: [],
      warnings: ['Marketing materials generation not yet implemented']
    };
  }

  /**
   * Export for escrow
   */
  private async exportEscrow(config: ExportConfiguration, items: Item[]): Promise<ExportResult> {
    const escrowPath = path.join(config.outputPath, 'escrow');
    await fs.ensureDir(escrowPath);

    // Create clean version without source files
    const cleanPath = path.join(escrowPath, 'clean');
    await fs.ensureDir(cleanPath);

    // Copy only necessary files for escrow
    // This would implement proper escrow packaging

    return {
      format: 'escrow',
      outputPath: escrowPath,
      fileCount: 0,
      totalSize: 0,
      success: true,
      errors: [],
      warnings: ['Escrow packaging not yet implemented']
    };
  }

  /**
   * Generate standardized filename for an item
   */
  private generateFileName(item: Item, extension: 'ydd' | 'ytd'): string {
    const prefix = item.kind === 'prop' ? 'p_' : '';
    return `mp_${item.gender === 'male' ? 'm' : 'f'}_freemode_01_${prefix}${item.drawable}_${item.textures[0] || 0}.${extension}`;
  }

  /**
   * Generate FiveM manifest Lua content
   */
  private generateManifestLua(manifest: FiveMManifest): string {
    return `fx_version '${manifest.fx_version}'
game '${manifest.game}'
${manifest.lua54 ? `lua54 '${manifest.lua54}'` : ''}

author '${manifest.author}'
description '${manifest.description}'
version '${manifest.version}'

files {
${manifest.files?.map(file => `    '${file}'`).join(',\n') || ''}
}`;
  }

  /**
   * Populate QB-Clothing configuration
   */
  private populateQBConfig(config: any, items: Item[]): void {
    for (const item of items) {
      const slotKey = this.mapSlotToQB(item.slot);
      if (!slotKey) continue;

      if (!config[slotKey]) {
        config[slotKey] = [];
      }

      config[slotKey].push({
        drawable: item.drawable,
        texture: item.textures[0] || 0,
        label: item.labels?.[0] || `${item.slot} ${item.drawable}`
      });
    }
  }

  /**
   * Map internal slot names to QB-Clothing slot names
   */
  private mapSlotToQB(slot: string): string | null {
    const mapping: Record<string, string> = {
      'face': 'face',
      'mask': 'mask',
      'hair': 'hair',
      'torso': 'torso',
      'legs': 'legs',
      'bag': 'bag',
      'feet': 'shoes',
      'accs': 'accessory',
      'undershirt': 'undershirt',
      'armor': 'kevlar',
      'decals': 'badge',
      'tops': 'torso2'
    };

    return mapping[slot] || null;
  }

  /**
   * Generate ESX Lua configuration
   */
  private generateESXLua(config: any): string {
    return `Config = {}
Config.Locale = '${config.Config.Locale}'
Config.Price = ${config.Config.Price}
Config.DrawDistance = ${config.Config.DrawDistance}

-- Shops configuration would go here
-- This is a simplified version
`;
  }

  /**
   * Create documentation files
   */
  private async createDocumentation(outputPath: string, items: Item[], config: ExportConfiguration): Promise<void> {
    const docsPath = path.join(outputPath, 'docs');
    await fs.ensureDir(docsPath);

    // README
    const readme = `# ${config.metadata.description}

Generated by DripForge Pro

## Installation
1. Place the resource in your resources folder
2. Add \`ensure ${path.basename(outputPath)}\` to your server.cfg
3. Restart your server

## Items Included
- ${items.length} clothing items
- ${items.filter(i => i.gender === 'male').length} male items
- ${items.filter(i => i.gender === 'female').length} female items

## Support
Visit: ${config.metadata.website || 'N/A'}
Discord: ${config.metadata.discord || 'N/A'}
`;

    await fs.writeFile(path.join(docsPath, 'README.md'), readme);

    // Slot mapping
    const slotMapping = items.map(item => 
      `${item.gender}_${item.slot}_${item.drawable}_${item.textures[0] || 0}`
    ).join('\n');

    await fs.writeFile(path.join(docsPath, 'slot-mapping.txt'), slotMapping);

    // Installation guide
    const installation = `# Installation Guide

## Requirements
- FiveM Server
- Compatible clothing framework (QB-Core, ESX, etc.)

## Steps
1. Download and extract the resource
2. Place in your resources folder
3. Add to server.cfg
4. Configure clothing shop (if applicable)
5. Restart server

## Troubleshooting
- Ensure all files are properly streamed
- Check for slot conflicts with existing clothing
- Verify framework compatibility
`;

    await fs.writeFile(path.join(docsPath, 'INSTALLATION.md'), installation);
  }
}
