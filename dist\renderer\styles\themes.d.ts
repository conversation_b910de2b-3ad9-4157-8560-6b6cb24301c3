/**
 * Theme definitions for styled-components
 */
import 'styled-components';
declare module 'styled-components' {
    interface DefaultTheme extends Theme {
    }
}
export interface Theme {
    colors: {
        primary: string;
        primaryHover: string;
        primaryActive: string;
        background: string;
        backgroundSecondary: string;
        backgroundTertiary: string;
        surface: string;
        surfaceHover: string;
        surfaceActive: string;
        text: string;
        textSecondary: string;
        textMuted: string;
        textInverse: string;
        border: string;
        borderHover: string;
        borderFocus: string;
        success: string;
        warning: string;
        error: string;
        info: string;
        accent: string;
        accentHover: string;
        sidebarBackground: string;
        sidebarBorder: string;
        sidebarItemHover: string;
        sidebarItemActive: string;
        previewBackground: string;
        previewBorder: string;
        consoleBackground: string;
        consoleBorder: string;
        consoleText: string;
        dropZone: string;
        dropZoneActive: string;
        dropZoneBorder: string;
    };
    spacing: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        xxl: string;
    };
    typography: {
        fontFamily: string;
        fontFamilyMono: string;
        fontSize: {
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            xxl: string;
        };
        fontWeight: {
            normal: number;
            medium: number;
            semibold: number;
            bold: number;
        };
        lineHeight: {
            tight: number;
            normal: number;
            relaxed: number;
        };
    };
    borderRadius: {
        sm: string;
        md: string;
        lg: string;
        full: string;
    };
    shadows: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
    };
    transitions: {
        fast: string;
        normal: string;
        slow: string;
    };
    zIndex: {
        dropdown: number;
        modal: number;
        tooltip: number;
        notification: number;
    };
}
export declare const darkTheme: Theme;
export declare const lightTheme: Theme;
