/**
 * IPC handlers for communication between main and renderer processes
 */

import { ipcMain, dialog, shell, BrowserWindow } from 'electron';
import Store from 'electron-store';
import * as fs from 'fs-extra';
import * as path from 'path';
import { AppSettings, Project } from '../types/core';
import { AssetIngestService } from './services/asset-ingest';
import { SlotRouterService } from './services/slot-router';
import { QualityAssuranceService } from './services/quality-assurance';
import { ExportService } from './services/export';

export function setupIpcHandlers(store: Store<AppSettings>): void {
  // File system operations
  ipcMain.handle('fs:read-file', async (_, filePath: string) => {
    try {
      return await fs.readFile(filePath, 'utf8');
    } catch (error) {
      throw new Error(`Failed to read file: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  ipcMain.handle('fs:write-file', async (_, filePath: string, content: string) => {
    try {
      await fs.ensureDir(path.dirname(filePath));
      await fs.writeFile(filePath, content, 'utf8');
      return true;
    } catch (error) {
      throw new Error(`Failed to write file: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  ipcMain.handle('fs:exists', async (_, filePath: string) => {
    return fs.pathExists(filePath);
  });

  ipcMain.handle('fs:read-dir', async (_, dirPath: string) => {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      return items.map(item => ({
        name: item.name,
        isDirectory: item.isDirectory(),
        isFile: item.isFile(),
        path: path.join(dirPath, item.name)
      }));
    } catch (error) {
      throw new Error(`Failed to read directory: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  // Dialog operations
  ipcMain.handle('dialog:open-file', async (_, options: Electron.OpenDialogOptions) => {
    const result = await dialog.showOpenDialog(options);
    return result;
  });

  ipcMain.handle('dialog:save-file', async (_, options: Electron.SaveDialogOptions) => {
    const result = await dialog.showSaveDialog(options);
    return result;
  });

  ipcMain.handle('dialog:show-message', async (_, options: Electron.MessageBoxOptions) => {
    const result = await dialog.showMessageBox(options);
    return result;
  });

  ipcMain.handle('dialog:show-error', async (_, title: string, content: string) => {
    dialog.showErrorBox(title, content);
  });

  // Shell operations
  ipcMain.handle('shell:open-external', async (_, url: string) => {
    await shell.openExternal(url);
  });

  ipcMain.handle('shell:show-item-in-folder', async (_, fullPath: string) => {
    shell.showItemInFolder(fullPath);
  });

  ipcMain.handle('shell:open-path', async (_, fullPath: string) => {
    return shell.openPath(fullPath);
  });

  // App settings
  ipcMain.handle('settings:get', (_, key?: string) => {
    return key ? store.get(key) : store.store;
  });

  ipcMain.handle('settings:set', (_, key: string, value: any) => {
    store.set(key, value);
    return true;
  });

  ipcMain.handle('settings:reset', () => {
    store.clear();
    return true;
  });

  // Project operations
  ipcMain.handle('project:create', async (_, projectData: Partial<Project>) => {
    try {
      const project: Project = {
        id: generateId(),
        name: projectData.name || 'Untitled Project',
        description: projectData.description || '',
        created: new Date(),
        modified: new Date(),
        items: [],
        slotMappings: [],
        settings: {
          reservedRanges: [],
          qaSettings: {
            enableClippingDetection: true,
            clippingTolerance: 0.01,
            requireLODs: true,
            maxTriangles: 50000,
            deepScanMode: false
          },
          exportSettings: {
            includeQBClothing: true,
            includeESX: false,
            includeIcons: true,
            includeMarketing: false,
            escrowMode: false,
            watermarkImages: false
          },
          previewSettings: {
            defaultPose: 'idle',
            renderQuality: 'medium',
            enablePBR: true,
            showWireframe: false
          }
        },
        exportHistory: []
      };

      return project;
    } catch (error) {
      throw new Error(`Failed to create project: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  ipcMain.handle('project:save', async (_, project: Project, filePath?: string) => {
    try {
      if (!filePath) {
        const result = await dialog.showSaveDialog({
          title: 'Save Project',
          defaultPath: `${project.name}.dfp`,
          filters: [
            { name: 'DripForge Project', extensions: ['dfp'] }
          ]
        });

        if (result.canceled || !result.filePath) {
          return null;
        }
        filePath = result.filePath;
      }

      project.modified = new Date();
      await fs.writeFile(filePath, JSON.stringify(project, null, 2));
      
      // Add to recent projects
      const recentProjects = store.get('recentProjects', []) as string[];
      const updatedRecent = [filePath, ...recentProjects.filter(p => p !== filePath)]
        .slice(0, store.get('maxRecentProjects', 10));
      store.set('recentProjects', updatedRecent);

      return filePath;
    } catch (error) {
      throw new Error(`Failed to save project: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  ipcMain.handle('project:load', async (_, filePath?: string) => {
    try {
      if (!filePath) {
        const result = await dialog.showOpenDialog({
          title: 'Open Project',
          filters: [
            { name: 'DripForge Project', extensions: ['dfp'] }
          ],
          properties: ['openFile']
        });

        if (result.canceled || !result.filePaths.length) {
          return null;
        }
        filePath = result.filePaths[0];
      }

      const projectData = await fs.readFile(filePath, 'utf8');
      const project: Project = JSON.parse(projectData);

      // Add to recent projects
      const recentProjects = store.get('recentProjects', []) as string[];
      const updatedRecent = [filePath, ...recentProjects.filter(p => p !== filePath)]
        .slice(0, store.get('maxRecentProjects', 10));
      store.set('recentProjects', updatedRecent);

      return { project, filePath };
    } catch (error) {
      throw new Error(`Failed to load project: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  // Asset operations
  ipcMain.handle('assets:ingest', async (_, paths: string[], projectId: string) => {
    try {
      const assetIngest = new AssetIngestService();
      return await assetIngest.ingestAssets(paths, projectId);
    } catch (error) {
      throw new Error(`Failed to ingest assets: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  ipcMain.handle('assets:validate', async (_, itemIds: string[]) => {
    try {
      const qa = new QualityAssuranceService();
      return await qa.validateAssets(itemIds);
    } catch (error) {
      throw new Error(`Failed to validate assets: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  // Slot routing operations
  ipcMain.handle('slots:route', async (_, items: any[], settings: any) => {
    try {
      const slotRouter = new SlotRouterService();
      return await slotRouter.routeSlots(items, settings);
    } catch (error) {
      throw new Error(`Failed to route slots: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  ipcMain.handle('slots:check-conflicts', async (_, items: any[]) => {
    try {
      const slotRouter = new SlotRouterService();
      return await slotRouter.checkConflicts(items);
    } catch (error) {
      throw new Error(`Failed to check conflicts: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  // Export operations
  ipcMain.handle('export:generate', async (_, config: any, items: any[]) => {
    try {
      const exportService = new ExportService();
      return await exportService.exportProject(config, items);
    } catch (error) {
      throw new Error(`Failed to export project: ${error instanceof Error ? error.message : String(error)}`);
    }
  });

  // Window operations
  ipcMain.handle('window:minimize', () => {
    const window = BrowserWindow.getFocusedWindow();
    window?.minimize();
  });

  ipcMain.handle('window:maximize', () => {
    const window = BrowserWindow.getFocusedWindow();
    if (window?.isMaximized()) {
      window.unmaximize();
    } else {
      window?.maximize();
    }
  });

  ipcMain.handle('window:close', () => {
    const window = BrowserWindow.getFocusedWindow();
    window?.close();
  });

  // Utility functions
  ipcMain.handle('app:get-version', () => {
    return require('../../package.json').version;
  });

  ipcMain.handle('app:get-platform', () => {
    return process.platform;
  });

  ipcMain.handle('app:restart', () => {
    const { app } = require('electron');
    app.relaunch();
    app.exit();
  });
}

function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}
