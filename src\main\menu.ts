/**
 * Application menu configuration
 */

import { Menu, MenuItemConstructorOptions, BrowserWindow, app, shell } from 'electron';
import Store from 'electron-store';
import { AppSettings } from '@/types/core';

export function createMenu(mainWindow: BrowserWindow | null, store: Store<AppSettings>): Menu {
  const isMac = process.platform === 'darwin';
  const isDev = process.env.NODE_ENV === 'development';

  const template: MenuItemConstructorOptions[] = [
    // App Menu (macOS only)
    ...(isMac ? [{
      label: app.getName(),
      submenu: [
        { role: 'about' as const },
        { type: 'separator' as const },
        {
          label: 'Preferences...',
          accelerator: 'Cmd+,',
          click: () => mainWindow?.webContents.send('menu:preferences')
        },
        { type: 'separator' as const },
        { role: 'services' as const },
        { type: 'separator' as const },
        { role: 'hide' as const },
        { role: 'hideOthers' as const },
        { role: 'unhide' as const },
        { type: 'separator' as const },
        { role: 'quit' as const }
      ]
    }] : []),

    // File Menu
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N',
          click: () => mainWindow?.webContents.send('menu:new-project')
        },
        {
          label: 'Open Project...',
          accelerator: 'CmdOrCtrl+O',
          click: () => mainWindow?.webContents.send('menu:open-project')
        },
        {
          label: 'Open Recent',
          submenu: createRecentProjectsMenu(store, mainWindow)
        },
        { type: 'separator' },
        {
          label: 'Save Project',
          accelerator: 'CmdOrCtrl+S',
          click: () => mainWindow?.webContents.send('menu:save-project')
        },
        {
          label: 'Save Project As...',
          accelerator: 'CmdOrCtrl+Shift+S',
          click: () => mainWindow?.webContents.send('menu:save-project-as')
        },
        { type: 'separator' },
        {
          label: 'Import Assets...',
          accelerator: 'CmdOrCtrl+I',
          click: () => mainWindow?.webContents.send('menu:import-assets')
        },
        {
          label: 'Export Project...',
          accelerator: 'CmdOrCtrl+E',
          click: () => mainWindow?.webContents.send('menu:export-project')
        },
        { type: 'separator' },
        ...(isMac ? [] : [
          {
            label: 'Preferences...',
            accelerator: 'Ctrl+,',
            click: () => mainWindow?.webContents.send('menu:preferences')
          },
          { type: 'separator' as const }
        ]),
        isMac ? { role: 'close' as const } : { role: 'quit' as const }
      ]
    },

    // Edit Menu
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' },
        { type: 'separator' },
        {
          label: 'Find',
          accelerator: 'CmdOrCtrl+F',
          click: () => mainWindow?.webContents.send('menu:find')
        },
        {
          label: 'Find Next',
          accelerator: 'CmdOrCtrl+G',
          click: () => mainWindow?.webContents.send('menu:find-next')
        }
      ]
    },

    // View Menu
    {
      label: 'View',
      submenu: [
        {
          label: 'Tree View',
          accelerator: 'CmdOrCtrl+1',
          click: () => mainWindow?.webContents.send('menu:view-tree')
        },
        {
          label: 'Grid View',
          accelerator: 'CmdOrCtrl+2',
          click: () => mainWindow?.webContents.send('menu:view-grid')
        },
        {
          label: 'List View',
          accelerator: 'CmdOrCtrl+3',
          click: () => mainWindow?.webContents.send('menu:view-list')
        },
        { type: 'separator' },
        {
          label: 'Show Preview Panel',
          accelerator: 'CmdOrCtrl+P',
          click: () => mainWindow?.webContents.send('menu:toggle-preview')
        },
        {
          label: 'Show Properties Panel',
          accelerator: 'CmdOrCtrl+Shift+P',
          click: () => mainWindow?.webContents.send('menu:toggle-properties')
        },
        {
          label: 'Show Console',
          accelerator: 'CmdOrCtrl+Shift+C',
          click: () => mainWindow?.webContents.send('menu:toggle-console')
        },
        { type: 'separator' },
        {
          label: 'Zoom In',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => mainWindow?.webContents.send('menu:zoom-in')
        },
        {
          label: 'Zoom Out',
          accelerator: 'CmdOrCtrl+-',
          click: () => mainWindow?.webContents.send('menu:zoom-out')
        },
        {
          label: 'Reset Zoom',
          accelerator: 'CmdOrCtrl+0',
          click: () => mainWindow?.webContents.send('menu:zoom-reset')
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },

    // Tools Menu
    {
      label: 'Tools',
      submenu: [
        {
          label: 'Route Slots',
          accelerator: 'CmdOrCtrl+R',
          click: () => mainWindow?.webContents.send('menu:route-slots')
        },
        {
          label: 'Quality Assurance Scan',
          accelerator: 'CmdOrCtrl+Q',
          click: () => mainWindow?.webContents.send('menu:qa-scan')
        },
        {
          label: 'Generate Thumbnails',
          accelerator: 'CmdOrCtrl+T',
          click: () => mainWindow?.webContents.send('menu:generate-thumbnails')
        },
        {
          label: 'Optimize Assets',
          click: () => mainWindow?.webContents.send('menu:optimize-assets')
        },
        { type: 'separator' },
        {
          label: 'Batch Operations',
          submenu: [
            {
              label: 'Batch Import',
              click: () => mainWindow?.webContents.send('menu:batch-import')
            },
            {
              label: 'Batch Export',
              click: () => mainWindow?.webContents.send('menu:batch-export')
            },
            {
              label: 'Batch Rename',
              click: () => mainWindow?.webContents.send('menu:batch-rename')
            }
          ]
        },
        { type: 'separator' },
        {
          label: 'Open Blender',
          click: () => mainWindow?.webContents.send('menu:open-blender')
        },
        {
          label: 'Open Temp Folder',
          click: () => {
            const tempDir = store.get('tempDirectory');
            shell.openPath(tempDir);
          }
        }
      ]
    },

    // Window Menu
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'zoom' },
        ...(isMac ? [
          { type: 'separator' as const },
          { role: 'front' as const },
          { type: 'separator' as const },
          { role: 'window' as const }
        ] : [
          { role: 'close' as const }
        ])
      ]
    },

    // Help Menu
    {
      label: 'Help',
      submenu: [
        {
          label: 'Getting Started',
          click: () => shell.openExternal('https://dripforge.pro/docs/getting-started')
        },
        {
          label: 'Documentation',
          click: () => shell.openExternal('https://dripforge.pro/docs')
        },
        {
          label: 'Video Tutorials',
          click: () => shell.openExternal('https://dripforge.pro/tutorials')
        },
        {
          label: 'Community Discord',
          click: () => shell.openExternal('https://discord.gg/dripforge')
        },
        { type: 'separator' },
        {
          label: 'Report Bug',
          click: () => shell.openExternal('https://github.com/dripforge/pro/issues')
        },
        {
          label: 'Feature Request',
          click: () => shell.openExternal('https://github.com/dripforge/pro/discussions')
        },
        { type: 'separator' },
        {
          label: 'Check for Updates',
          click: () => mainWindow?.webContents.send('menu:check-updates')
        },
        ...(!isMac ? [
          { type: 'separator' as const },
          {
            label: 'About DripForge Pro',
            click: () => mainWindow?.webContents.send('menu:about')
          }
        ] : [])
      ]
    }
  ];

  return Menu.buildFromTemplate(template);
}

function createRecentProjectsMenu(store: Store<AppSettings>, mainWindow: BrowserWindow | null): MenuItemConstructorOptions[] {
  const recentProjects = store.get('recentProjects', []) as string[];
  
  if (recentProjects.length === 0) {
    return [{ label: 'No Recent Projects', enabled: false }];
  }

  const recentItems: MenuItemConstructorOptions[] = recentProjects.map((projectPath, index) => ({
    label: `${index + 1}. ${require('path').basename(projectPath)}`,
    accelerator: index < 9 ? `CmdOrCtrl+${index + 1}` : undefined,
    click: () => mainWindow?.webContents.send('menu:open-recent', projectPath)
  }));

  recentItems.push(
    { type: 'separator' },
    {
      label: 'Clear Recent Projects',
      click: () => {
        store.set('recentProjects', []);
        // Rebuild menu
        const menu = createMenu(mainWindow, store);
        Menu.setApplicationMenu(menu);
      }
    }
  );

  return recentItems;
}
