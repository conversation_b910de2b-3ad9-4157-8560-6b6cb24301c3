/**
 * Error Boundary Component - Catches and displays React errors
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import styled from 'styled-components';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: ${props => props.theme.spacing.xl};
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
`;

const ErrorTitle = styled.h1`
  font-size: ${props => props.theme.typography.fontSize.xxl};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  margin-bottom: ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.error};
`;

const ErrorMessage = styled.p`
  font-size: ${props => props.theme.typography.fontSize.lg};
  margin-bottom: ${props => props.theme.spacing.lg};
  text-align: center;
  max-width: 600px;
  line-height: ${props => props.theme.typography.lineHeight.relaxed};
`;

const ErrorDetails = styled.details`
  margin-top: ${props => props.theme.spacing.lg};
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.backgroundSecondary};
  border-radius: ${props => props.theme.borderRadius.md};
  border: 1px solid ${props => props.theme.colors.border};
  max-width: 800px;
  width: 100%;
`;

const ErrorSummary = styled.summary`
  cursor: pointer;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  margin-bottom: ${props => props.theme.spacing.sm};
  
  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const ErrorStack = styled.pre`
  font-family: ${props => props.theme.typography.fontFamilyMono};
  font-size: ${props => props.theme.typography.fontSize.sm};
  background: ${props => props.theme.colors.backgroundTertiary};
  padding: ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.sm};
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.lg};
`;

const Button = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.textInverse};
  border: none;
  border-radius: ${props => props.theme.borderRadius.md};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  
  &:hover {
    background: ${props => props.theme.colors.primaryHover};
  }
  
  &:active {
    background: ${props => props.theme.colors.primaryActive};
  }
`;

const SecondaryButton = styled(Button)`
  background: ${props => props.theme.colors.surface};
  color: ${props => props.theme.colors.text};
  border: 1px solid ${props => props.theme.colors.border};
  
  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
    border-color: ${props => props.theme.colors.borderHover};
  }
`;

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Report error to main process
    if (window.electronAPI?.dialog?.showError) {
      window.electronAPI.dialog.showError(
        'Application Error',
        `An unexpected error occurred: ${error.message}`
      );
    }
  }

  handleReload = () => {
    window.location.reload();
  };

  handleRestart = async () => {
    if (window.electronAPI?.app?.restart) {
      await window.electronAPI.app.restart();
    } else {
      window.location.reload();
    }
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorContainer>
          <ErrorTitle>Something went wrong</ErrorTitle>
          
          <ErrorMessage>
            DripForge Pro encountered an unexpected error and needs to be restarted.
            Your work should be automatically saved.
          </ErrorMessage>

          <ActionButtons>
            <Button onClick={this.handleReload}>
              Reload Application
            </Button>
            <SecondaryButton onClick={this.handleRestart}>
              Restart Application
            </SecondaryButton>
            <SecondaryButton onClick={this.handleReset}>
              Try Again
            </SecondaryButton>
          </ActionButtons>

          {this.state.error && (
            <ErrorDetails>
              <ErrorSummary>Technical Details</ErrorSummary>
              <div>
                <strong>Error:</strong> {this.state.error.message}
              </div>
              {this.state.error.stack && (
                <ErrorStack>{this.state.error.stack}</ErrorStack>
              )}
              {this.state.errorInfo && this.state.errorInfo.componentStack && (
                <>
                  <div style={{ marginTop: '16px' }}>
                    <strong>Component Stack:</strong>
                  </div>
                  <ErrorStack>{this.state.errorInfo.componentStack}</ErrorStack>
                </>
              )}
            </ErrorDetails>
          )}
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}
