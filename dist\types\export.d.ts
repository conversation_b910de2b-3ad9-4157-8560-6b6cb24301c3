/**
 * Export and configuration generation types
 */
import { Item } from './core';
export interface ExportConfiguration {
    outputPath: string;
    formats: ExportFormat[];
    options: ExportOptions;
    metadata: ExportMetadata;
}
export interface ExportFormat {
    type: 'qb-clothing' | 'esx' | 'fivem-resource' | 'icons' | 'marketing' | 'escrow';
    enabled: boolean;
    options: any;
}
export interface ExportOptions {
    includeDocumentation: boolean;
    generateThumbnails: boolean;
    optimizeAssets: boolean;
    validateOutput: boolean;
    createBackup: boolean;
    compressionLevel: number;
    watermarkImages: boolean;
    escrowMode: boolean;
}
export interface ExportMetadata {
    author: string;
    version: string;
    description: string;
    license: string;
    website?: string;
    discord?: string;
    tebexStore?: string;
}
export interface QBClothingConfig {
    male: QBClothingGender;
    female: QBClothingGender;
}
export interface QBClothingGender {
    face?: QBClothingItem[];
    mask?: QBClothingItem[];
    hair?: QBClothingItem[];
    torso?: QBClothingItem[];
    legs?: QBClothingItem[];
    bag?: QBClothingItem[];
    shoes?: QBClothingItem[];
    accessory?: QBClothingItem[];
    undershirt?: QBClothingItem[];
    kevlar?: QBClothingItem[];
    badge?: QBClothingItem[];
    torso2?: QBClothingItem[];
}
export interface QBClothingItem {
    drawable: number;
    texture: number;
    label?: string;
    price?: number;
    category?: string;
}
export interface ESXConfig {
    Config: {
        Locale: string;
        Price: number;
        DrawDistance: number;
        MarkerSize: {
            x: number;
            y: number;
            z: number;
        };
        MarkerColor: {
            r: number;
            g: number;
            b: number;
        };
        MarkerType: number;
    };
    Shops: ESXShop[];
}
export interface ESXShop {
    name: string;
    coords: {
        x: number;
        y: number;
        z: number;
    };
    clothes: {
        male: ESXClothingCategory;
        female: ESXClothingCategory;
    };
}
export interface ESXClothingCategory {
    tshirt_1?: ESXClothingItem[];
    tshirt_2?: ESXClothingItem[];
    torso_1?: ESXClothingItem[];
    torso_2?: ESXClothingItem[];
    decals_1?: ESXClothingItem[];
    decals_2?: ESXClothingItem[];
    arms?: ESXClothingItem[];
    pants_1?: ESXClothingItem[];
    pants_2?: ESXClothingItem[];
    shoes_1?: ESXClothingItem[];
    shoes_2?: ESXClothingItem[];
    mask_1?: ESXClothingItem[];
    mask_2?: ESXClothingItem[];
    bproof_1?: ESXClothingItem[];
    bproof_2?: ESXClothingItem[];
    chain_1?: ESXClothingItem[];
    chain_2?: ESXClothingItem[];
    helmet_1?: ESXClothingItem[];
    helmet_2?: ESXClothingItem[];
    glasses_1?: ESXClothingItem[];
    glasses_2?: ESXClothingItem[];
    watches_1?: ESXClothingItem[];
    watches_2?: ESXClothingItem[];
    bracelets_1?: ESXClothingItem[];
    bracelets_2?: ESXClothingItem[];
}
export interface ESXClothingItem {
    label: string;
    value: number;
    price: number;
}
export interface FiveMResource {
    manifest: FiveMManifest;
    stream: string[];
    config: any;
    documentation: ResourceDocumentation;
}
export interface FiveMManifest {
    fx_version: string;
    game: string;
    lua54?: string;
    author: string;
    description: string;
    version: string;
    files?: string[];
    data_file?: DataFileEntry[];
    dependencies?: string[];
    server_scripts?: string[];
    client_scripts?: string[];
    shared_scripts?: string[];
}
export interface DataFileEntry {
    type: string;
    file: string;
}
export interface ResourceDocumentation {
    readme: string;
    changelog: string;
    installation: string;
    slotMapping: SlotMappingDoc;
    qaReport: QAReportDoc;
}
export interface SlotMappingDoc {
    male: Record<string, SlotRange>;
    female: Record<string, SlotRange>;
    conflicts: ConflictReport[];
    reserved: ReservedSlotDoc[];
}
export interface SlotRange {
    start: number;
    end: number;
    count: number;
    items: string[];
}
export interface ConflictReport {
    slot: string;
    drawable: number;
    conflictsWith: string[];
    resolution: string;
}
export interface ReservedSlotDoc {
    slot: string;
    range: string;
    reason: string;
}
export interface QAReportDoc {
    summary: QASummary;
    items: QAItemReport[];
    recommendations: string[];
    timestamp: string;
}
export interface QASummary {
    totalItems: number;
    passed: number;
    failed: number;
    warnings: number;
    averageScore: number;
}
export interface QAItemReport {
    itemId: string;
    name: string;
    slot: string;
    drawable: number;
    status: 'passed' | 'failed' | 'warning';
    score: number;
    issues: QAIssue[];
}
export interface QAIssue {
    type: 'clipping' | 'lod' | 'texture' | 'mesh';
    severity: 'low' | 'medium' | 'high';
    description: string;
    suggestion?: string;
}
export interface IconGenerationOptions {
    sizes: number[];
    format: 'png' | 'jpg' | 'webp';
    background: 'transparent' | 'white' | 'black' | string;
    pose: string;
    lighting: string;
    padding: number;
    quality: number;
}
export interface MarketingImageOptions {
    sizes: {
        width: number;
        height: number;
        name: string;
    }[];
    format: 'png' | 'jpg';
    quality: number;
    watermark: boolean;
    watermarkText?: string;
    watermarkOpacity: number;
    backgrounds: string[];
    poses: string[];
    compositions: MarketingComposition[];
}
export interface MarketingComposition {
    name: string;
    layout: 'single' | 'grid' | 'collage';
    itemCount: number;
    showLabels: boolean;
    theme: 'dark' | 'light' | 'brand';
}
export interface EscrowPackage {
    cleanFiles: string[];
    encryptedFiles: string[];
    manifest: EscrowManifest;
    documentation: string[];
}
export interface EscrowManifest {
    name: string;
    version: string;
    author: string;
    description: string;
    files: EscrowFileEntry[];
    dependencies: string[];
    permissions: string[];
}
export interface EscrowFileEntry {
    path: string;
    encrypted: boolean;
    checksum: string;
    size: number;
}
export interface TebexIntegration {
    enabled: boolean;
    apiKey?: string;
    storeId?: string;
    categoryId?: string;
    autoUpload: boolean;
    packageTemplate: TebexPackageTemplate;
}
export interface TebexPackageTemplate {
    name: string;
    description: string;
    price: number;
    currency: string;
    category: string;
    tags: string[];
    images: string[];
    downloadable: boolean;
    instantDelivery: boolean;
}
export interface ExportJob {
    id: string;
    configuration: ExportConfiguration;
    items: Item[];
    status: 'pending' | 'running' | 'completed' | 'failed';
    progress: ExportProgress;
    results: ExportResult[];
    errors: string[];
    warnings: string[];
    startTime: Date;
    endTime?: Date;
}
export interface ExportProgress {
    currentStep: string;
    currentItem: number;
    totalItems: number;
    percentage: number;
    estimatedTimeRemaining?: number;
}
export interface ExportResult {
    format: string;
    outputPath: string;
    fileCount: number;
    totalSize: number;
    success: boolean;
    errors: string[];
    warnings: string[];
}
export declare const DEFAULT_FIVEM_MANIFEST: Partial<FiveMManifest>;
export declare const DEFAULT_QB_CONFIG: Partial<QBClothingConfig>;
export declare const DEFAULT_ESX_CONFIG: Partial<ESXConfig>;
