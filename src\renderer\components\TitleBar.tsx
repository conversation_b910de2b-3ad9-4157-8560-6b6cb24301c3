/**
 * Custom Title Bar Component - For Windows/Linux
 */

import React from 'react';
import styled from 'styled-components';
import { useProjectStore } from '@/stores/projectStore';

const TitleBarContainer = styled.div`
  display: flex;
  align-items: center;
  height: 32px;
  background: ${props => props.theme.colors.backgroundSecondary};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  -webkit-app-region: drag;
  user-select: none;
`;

const TitleBarContent = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  padding: 0 ${props => props.theme.spacing.md};
`;

const AppIcon = styled.div`
  width: 16px;
  height: 16px;
  margin-right: ${props => props.theme.spacing.sm};
  font-size: 12px;
`;

const AppTitle = styled.div`
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text};
`;

const WindowControls = styled.div`
  display: flex;
  -webkit-app-region: no-drag;
`;

const WindowButton = styled.button`
  width: 46px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: ${props => props.theme.colors.text};
  font-size: 10px;
  transition: ${props => props.theme.transitions.fast};
  
  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
  }
  
  &.close:hover {
    background: #e81123;
    color: white;
  }
`;

export const TitleBar: React.FC = () => {
  const { currentProject, currentProjectPath, hasUnsavedChanges } = useProjectStore();

  const getTitle = () => {
    let title = 'DripForge Pro';
    
    if (currentProject) {
      title = currentProject.name;
      if (currentProjectPath) {
        title += ` - ${currentProjectPath}`;
      }
      if (hasUnsavedChanges) {
        title += ' •';
      }
    }
    
    return title;
  };

  const handleMinimize = () => {
    window.electronAPI.window.minimize();
  };

  const handleMaximize = () => {
    window.electronAPI.window.maximize();
  };

  const handleClose = () => {
    window.electronAPI.window.close();
  };

  return (
    <TitleBarContainer>
      <TitleBarContent>
        <AppIcon>🎨</AppIcon>
        <AppTitle>{getTitle()}</AppTitle>
      </TitleBarContent>
      
      <WindowControls>
        <WindowButton onClick={handleMinimize}>
          ─
        </WindowButton>
        <WindowButton onClick={handleMaximize}>
          □
        </WindowButton>
        <WindowButton className="close" onClick={handleClose}>
          ×
        </WindowButton>
      </WindowControls>
    </TitleBarContainer>
  );
};
