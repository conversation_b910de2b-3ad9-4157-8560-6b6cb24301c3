/**
 * TypeScript declarations for Electron API
 */

interface ElectronAPI {
  // Project operations
  project: {
    create: (projectData: any) => Promise<any>;
    save: (project: any, filePath?: string) => Promise<string | null>;
    load: (filePath?: string) => Promise<{ project: any; filePath: string } | null>;
  };

  // Asset operations
  assets: {
    ingest: (paths: string[], projectId: string) => Promise<any>;
    validate: (itemIds: string[]) => Promise<any>;
  };

  // Dialog operations
  dialog: {
    openFile: (options: any) => Promise<any>;
    saveFile: (options: any) => Promise<any>;
    showMessage: (options: any) => Promise<any>;
    showError: (title: string, content: string) => Promise<void>;
  };

  // Other operations
  slots: {
    route: (items: any[], settings: any) => Promise<any>;
    checkConflicts: (items: any[]) => Promise<any>;
  };

  export: {
    generate: (config: any, items: any[]) => Promise<any>;
  };

  settings: {
    get: (key?: string) => Promise<any>;
    set: (key: string, value: any) => Promise<boolean>;
    reset: () => Promise<boolean>;
  };

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  once: (channel: string, callback: (...args: any[]) => void) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
