/**
 * Asset processing and file handling types
 */
import { Gender, Component, Prop } from './core';
export interface AssetFile {
    path: string;
    name: string;
    extension: string;
    size: number;
    lastModified: Date;
    checksum: string;
}
export interface YDDFile extends AssetFile {
    extension: '.ydd';
    meshData?: MeshData;
    lodLevels: number;
    triangleCount: number;
    vertexCount: number;
}
export interface YTDFile extends AssetFile {
    extension: '.ytd';
    textures: TextureInfo[];
    totalSize: number;
}
export interface TextureInfo {
    name: string;
    width: number;
    height: number;
    format: string;
    mipLevels: number;
    size: number;
    hasAlpha: boolean;
}
export interface MeshData {
    vertices: Float32Array;
    normals: Float32Array;
    uvs: Float32Array;
    indices: Uint32Array;
    weights?: Float32Array;
    bones?: BoneData[];
    boundingBox: BoundingBox;
}
export interface BoneData {
    name: string;
    index: number;
    parentIndex: number;
    transform: Matrix4;
}
export interface BoundingBox {
    min: Vector3;
    max: Vector3;
    center: Vector3;
    size: Vector3;
}
export interface Vector3 {
    x: number;
    y: number;
    z: number;
}
export interface Matrix4 {
    elements: number[];
}
export interface AssetDetectionResult {
    gender: Gender;
    slot: Component | Prop;
    kind: 'component' | 'prop';
    drawable: number;
    texture: number;
    confidence: number;
    source: 'filename' | 'metadata' | 'heuristic';
}
export interface ImportResult {
    success: boolean;
    itemsImported: number;
    itemsSkipped: number;
    errors: ImportError[];
    warnings: string[];
    conflicts: ConflictInfo[];
    duration: number;
}
export interface ImportError {
    file: string;
    error: string;
    severity: 'error' | 'warning';
    suggestion?: string;
}
export interface ConflictInfo {
    file: string;
    existingItem: string;
    conflictType: 'slot' | 'filename' | 'checksum';
    resolution: 'skip' | 'overwrite' | 'rename';
}
export interface TextureProcessingOptions {
    format: 'DXT1' | 'DXT5' | 'BC7';
    generateMipmaps: boolean;
    maxSize: number;
    quality: number;
    preserveAlpha: boolean;
}
export interface MeshOptimizationOptions {
    generateLODs: boolean;
    lodReductions: number[];
    maxWeightsPerVertex: number;
    removeLooseVertices: boolean;
    optimizeTopology: boolean;
    preserveUVSeams: boolean;
}
export interface AssetValidationResult {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationWarning[];
    metrics: AssetMetrics;
}
export interface ValidationError {
    type: 'missing_file' | 'corrupt_data' | 'invalid_format' | 'size_limit';
    message: string;
    file?: string;
    severity: 'critical' | 'major' | 'minor';
}
export interface ValidationWarning {
    type: 'performance' | 'quality' | 'compatibility';
    message: string;
    file?: string;
    suggestion?: string;
}
export interface AssetMetrics {
    totalFiles: number;
    totalSize: number;
    triangleCount: number;
    textureMemory: number;
    lodCoverage: number;
    averageQuality: number;
}
export interface FileNamingConvention {
    pattern: RegExp;
    genderGroup: number;
    slotGroup: number;
    drawableGroup: number;
    textureGroup: number;
    description: string;
}
export declare const NAMING_CONVENTIONS: FileNamingConvention[];
export interface AssetCache {
    thumbnails: Map<string, string>;
    meshData: Map<string, MeshData>;
    textureData: Map<string, ImageData>;
    lastAccessed: Map<string, Date>;
    maxSize: number;
    currentSize: number;
}
export interface ThumbnailOptions {
    width: number;
    height: number;
    format: 'png' | 'jpg' | 'webp';
    quality: number;
    background: 'transparent' | 'white' | 'black' | string;
    pose: string;
    lighting: 'studio' | 'natural' | 'dramatic';
}
export interface BatchProcessingJob {
    id: string;
    type: 'import' | 'optimize' | 'thumbnail' | 'export';
    files: string[];
    options: any;
    progress: {
        current: number;
        total: number;
        status: string;
    };
    results: any[];
    errors: string[];
    startTime: Date;
    estimatedCompletion?: Date;
}
export type AssetEventType = 'import_started' | 'import_progress' | 'import_completed' | 'import_failed' | 'optimization_started' | 'optimization_completed' | 'thumbnail_generated' | 'validation_completed';
export interface AssetEvent {
    type: AssetEventType;
    timestamp: Date;
    data: any;
    itemId?: string;
    jobId?: string;
}
