/**
 * Item Manager Component - Manages imported clothing items
 */

import React, { useState, useMemo } from 'react';
import styled from 'styled-components';

const ManagerContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const Toolbar = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 8px 12px;
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #007acc;
  }

  &::placeholder {
    color: #888888;
  }
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #007acc;
  }

  option {
    background: #1a1a1a;
    color: #ffffff;
  }
`;

const ViewToggle = styled.div`
  display: flex;
  background: #1a1a1a;
  border-radius: 4px;
  overflow: hidden;
`;

const ViewButton = styled.button<{ active: boolean }>`
  padding: 8px 12px;
  background: ${props => props.active ? '#007acc' : 'transparent'};
  color: ${props => props.active ? '#ffffff' : '#888888'};
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.active ? '#007acc' : '#404040'};
    color: #ffffff;
  }
`;

const ItemsContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
`;

const GridView = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
`;

const ListView = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ItemCard = styled.div<{ selected: boolean }>`
  background: #2d2d2d;
  border: 2px solid ${props => props.selected ? '#007acc' : '#404040'};
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007acc;
    background: #353535;
  }
`;

const ItemHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
`;

const ItemIcon = styled.div<{ type: string }>`
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  background: ${props => {
    switch (props.type) {
      case 'male': return '#4a90e2';
      case 'female': return '#e24a90';
      case 'prop': return '#50c878';
      default: return '#888888';
    }
  }};
`;

const ItemInfo = styled.div`
  flex: 1;
`;

const ItemName = styled.div`
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
`;

const ItemDetails = styled.div`
  font-size: 12px;
  color: #888888;
`;

const ItemStats = styled.div`
  display: flex;
  gap: 16px;
  margin-top: 12px;
`;

const StatItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
`;

const StatLabel = styled.div`
  font-size: 10px;
  color: #888888;
  text-transform: uppercase;
`;

const StatValue = styled.div<{ status?: 'good' | 'warning' | 'error' }>`
  font-size: 14px;
  font-weight: 600;
  color: ${props => {
    switch (props.status) {
      case 'good': return '#28a745';
      case 'warning': return '#ffc107';
      case 'error': return '#dc3545';
      default: return '#ffffff';
    }
  }};
`;

const ListItem = styled.div<{ selected: boolean }>`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: #2d2d2d;
  border: 1px solid ${props => props.selected ? '#007acc' : '#404040'};
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007acc;
    background: #353535;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
  margin-left: auto;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;

  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background: #007acc;
          color: white;
          &:hover { background: #1a8cdd; }
        `;
      case 'danger':
        return `
          background: #dc3545;
          color: white;
          &:hover { background: #c82333; }
        `;
      default:
        return `
          background: #404040;
          color: white;
          &:hover { background: #505050; }
        `;
    }
  }}
`;

interface ClothingItem {
  id: string;
  name: string;
  type: 'male' | 'female' | 'prop';
  slot: string;
  drawable: number;
  texture: number;
  size: number;
  triangles: number;
  quality: 'good' | 'warning' | 'error';
  hasConflicts: boolean;
  imported: Date;
}

interface ItemManagerProps {
  items: ClothingItem[];
  onItemSelect?: (item: ClothingItem) => void;
  onItemDelete?: (itemId: string) => void;
  onItemEdit?: (item: ClothingItem) => void;
}

export const ItemManager: React.FC<ItemManagerProps> = ({
  items = [],
  onItemSelect,
  onItemDelete,
  onItemEdit
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const filteredItems = useMemo(() => {
    return items.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.slot.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter = filterType === 'all' || item.type === filterType;
      
      return matchesSearch && matchesFilter;
    });
  }, [items, searchQuery, filterType]);

  const handleItemClick = (item: ClothingItem) => {
    setSelectedItems(prev => 
      prev.includes(item.id) 
        ? prev.filter(id => id !== item.id)
        : [...prev, item.id]
    );
    
    if (onItemSelect) {
      onItemSelect(item);
    }
  };

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'male': return '👨';
      case 'female': return '👩';
      case 'prop': return '🎩';
      default: return '📄';
    }
  };

  const formatFileSize = (bytes: number) => {
    return (bytes / 1024 / 1024).toFixed(2) + ' MB';
  };

  const formatTriangles = (count: number) => {
    return count.toLocaleString();
  };

  if (items.length === 0) {
    return (
      <ManagerContainer>
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          color: '#888888'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📦</div>
          <div style={{ fontSize: '18px', marginBottom: '8px' }}>No items imported yet</div>
          <div style={{ fontSize: '14px' }}>Import some clothing files to get started</div>
        </div>
      </ManagerContainer>
    );
  }

  return (
    <ManagerContainer>
      <Toolbar>
        <SearchInput
          type="text"
          placeholder="Search items..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        
        <FilterSelect value={filterType} onChange={(e) => setFilterType(e.target.value)}>
          <option value="all">All Types</option>
          <option value="male">Male</option>
          <option value="female">Female</option>
          <option value="prop">Props</option>
        </FilterSelect>

        <ViewToggle>
          <ViewButton 
            active={viewMode === 'grid'} 
            onClick={() => setViewMode('grid')}
          >
            Grid
          </ViewButton>
          <ViewButton 
            active={viewMode === 'list'} 
            onClick={() => setViewMode('list')}
          >
            List
          </ViewButton>
        </ViewToggle>
      </Toolbar>

      <ItemsContainer>
        {viewMode === 'grid' ? (
          <GridView>
            {filteredItems.map(item => (
              <ItemCard
                key={item.id}
                selected={selectedItems.includes(item.id)}
                onClick={() => handleItemClick(item)}
              >
                <ItemHeader>
                  <ItemIcon type={item.type}>
                    {getItemIcon(item.type)}
                  </ItemIcon>
                  <ItemInfo>
                    <ItemName>{item.name}</ItemName>
                    <ItemDetails>
                      {item.type} • {item.slot} • Slot {item.drawable}
                    </ItemDetails>
                  </ItemInfo>
                </ItemHeader>

                <ItemStats>
                  <StatItem>
                    <StatLabel>Size</StatLabel>
                    <StatValue>{formatFileSize(item.size)}</StatValue>
                  </StatItem>
                  <StatItem>
                    <StatLabel>Triangles</StatLabel>
                    <StatValue>{formatTriangles(item.triangles)}</StatValue>
                  </StatItem>
                  <StatItem>
                    <StatLabel>Quality</StatLabel>
                    <StatValue status={item.quality}>
                      {item.quality === 'good' ? '✓' : item.quality === 'warning' ? '⚠' : '✗'}
                    </StatValue>
                  </StatItem>
                </ItemStats>

                <ActionButtons>
                  <ActionButton onClick={(e) => { e.stopPropagation(); onItemEdit?.(item); }}>
                    Edit
                  </ActionButton>
                  <ActionButton 
                    variant="danger" 
                    onClick={(e) => { e.stopPropagation(); onItemDelete?.(item.id); }}
                  >
                    Delete
                  </ActionButton>
                </ActionButtons>
              </ItemCard>
            ))}
          </GridView>
        ) : (
          <ListView>
            {filteredItems.map(item => (
              <ListItem
                key={item.id}
                selected={selectedItems.includes(item.id)}
                onClick={() => handleItemClick(item)}
              >
                <ItemIcon type={item.type}>
                  {getItemIcon(item.type)}
                </ItemIcon>
                <ItemInfo>
                  <ItemName>{item.name}</ItemName>
                  <ItemDetails>
                    {item.type} • {item.slot} • Slot {item.drawable} • {formatFileSize(item.size)}
                  </ItemDetails>
                </ItemInfo>
                <StatValue status={item.quality}>
                  {item.quality === 'good' ? '✓' : item.quality === 'warning' ? '⚠' : '✗'}
                </StatValue>
                <ActionButtons>
                  <ActionButton onClick={(e) => { e.stopPropagation(); onItemEdit?.(item); }}>
                    Edit
                  </ActionButton>
                  <ActionButton 
                    variant="danger" 
                    onClick={(e) => { e.stopPropagation(); onItemDelete?.(item.id); }}
                  >
                    Delete
                  </ActionButton>
                </ActionButtons>
              </ListItem>
            ))}
          </ListView>
        )}
      </ItemsContainer>
    </ManagerContainer>
  );
};
