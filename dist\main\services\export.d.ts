/**
 * Export Service - Handles project export to various formats
 */
import { Item } from '../../types/core';
import { ExportConfiguration, ExportResult } from '../../types/export';
export declare class ExportService {
    private tempDir;
    constructor(tempDir?: string);
    /**
     * Export project with given configuration
     */
    exportProject(config: ExportConfiguration, items: Item[]): Promise<ExportResult[]>;
    /**
     * Export to a specific format
     */
    private exportFormat;
    /**
     * Export as FiveM resource
     */
    private exportFiveMResource;
    /**
     * Export QB-Clothing configuration
     */
    private exportQBClothing;
    /**
     * Export ESX configuration
     */
    private exportESX;
    /**
     * Export icons/thumbnails
     */
    private exportIcons;
    /**
     * Export marketing materials
     */
    private exportMarketing;
    /**
     * Export for escrow
     */
    private exportEscrow;
    /**
     * Generate standardized filename for an item
     */
    private generateFileName;
    /**
     * Generate FiveM manifest Lua content
     */
    private generateManifestLua;
    /**
     * Populate QB-Clothing configuration
     */
    private populateQBConfig;
    /**
     * Map internal slot names to QB-Clothing slot names
     */
    private mapSlotToQB;
    /**
     * Generate ESX Lua configuration
     */
    private generateESXLua;
    /**
     * Create documentation files
     */
    private createDocumentation;
}
