/**
 * Asset Ingest Service - Handles importing and processing of GTA V clothing assets
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import { yauzl } from 'yauzl';
import { Gender, Component, Prop, Item, ItemKind } from '@/types/core';
import { 
  AssetDetectionResult, 
  ImportResult, 
  ImportError, 
  ConflictInfo,
  NAMING_CONVENTIONS 
} from '@/types/assets';

export class AssetIngestService {
  private tempDir: string;

  constructor(tempDir?: string) {
    this.tempDir = tempDir || path.join(require('os').tmpdir(), 'dripforge-pro', 'imports');
  }

  /**
   * Ingest assets from file paths or directories
   */
  async ingestAssets(paths: string[], projectId: string): Promise<ImportResult> {
    const startTime = Date.now();
    const result: ImportResult = {
      success: true,
      itemsImported: 0,
      itemsSkipped: 0,
      errors: [],
      warnings: [],
      conflicts: [],
      duration: 0
    };

    try {
      // Ensure temp directory exists
      await fs.ensureDir(this.tempDir);

      // Process each path
      for (const inputPath of paths) {
        const stats = await fs.stat(inputPath);
        
        if (stats.isDirectory()) {
          await this.processDirectory(inputPath, projectId, result);
        } else if (stats.isFile()) {
          const ext = path.extname(inputPath).toLowerCase();
          
          if (ext === '.zip' || ext === '.rar') {
            await this.processArchive(inputPath, projectId, result);
          } else if (ext === '.ydd' || ext === '.ytd') {
            await this.processFile(inputPath, projectId, result);
          } else {
            result.warnings.push(`Unsupported file type: ${inputPath}`);
          }
        }
      }

      result.duration = Date.now() - startTime;
      result.success = result.errors.length === 0;

    } catch (error) {
      result.success = false;
      result.errors.push({
        file: 'general',
        error: `Ingest failed: ${error.message}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Process a directory recursively
   */
  private async processDirectory(dirPath: string, projectId: string, result: ImportResult): Promise<void> {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });

      for (const item of items) {
        const fullPath = path.join(dirPath, item.name);

        if (item.isDirectory()) {
          await this.processDirectory(fullPath, projectId, result);
        } else if (item.isFile()) {
          const ext = path.extname(item.name).toLowerCase();
          
          if (ext === '.ydd' || ext === '.ytd') {
            await this.processFile(fullPath, projectId, result);
          } else if (ext === '.zip' || ext === '.rar') {
            await this.processArchive(fullPath, projectId, result);
          }
        }
      }
    } catch (error) {
      result.errors.push({
        file: dirPath,
        error: `Failed to process directory: ${error.message}`,
        severity: 'error'
      });
    }
  }

  /**
   * Process an archive file
   */
  private async processArchive(archivePath: string, projectId: string, result: ImportResult): Promise<void> {
    const extractDir = path.join(this.tempDir, `extract_${Date.now()}`);
    
    try {
      await fs.ensureDir(extractDir);
      
      // Extract archive (simplified - would need proper zip/rar handling)
      if (path.extname(archivePath).toLowerCase() === '.zip') {
        await this.extractZip(archivePath, extractDir);
        await this.processDirectory(extractDir, projectId, result);
      } else {
        result.warnings.push(`RAR archives not yet supported: ${archivePath}`);
      }
    } catch (error) {
      result.errors.push({
        file: archivePath,
        error: `Failed to process archive: ${error.message}`,
        severity: 'error'
      });
    } finally {
      // Clean up extracted files
      await fs.remove(extractDir).catch(() => {});
    }
  }

  /**
   * Extract ZIP file
   */
  private async extractZip(zipPath: string, extractDir: string): Promise<void> {
    return new Promise((resolve, reject) => {
      yauzl.open(zipPath, { lazyEntries: true }, (err, zipfile) => {
        if (err) return reject(err);

        zipfile.readEntry();
        zipfile.on('entry', (entry) => {
          if (/\/$/.test(entry.fileName)) {
            // Directory entry
            zipfile.readEntry();
          } else {
            // File entry
            const outputPath = path.join(extractDir, entry.fileName);
            fs.ensureDir(path.dirname(outputPath)).then(() => {
              zipfile.openReadStream(entry, (err, readStream) => {
                if (err) return reject(err);
                
                const writeStream = fs.createWriteStream(outputPath);
                readStream.pipe(writeStream);
                writeStream.on('close', () => zipfile.readEntry());
              });
            });
          }
        });

        zipfile.on('end', () => resolve());
        zipfile.on('error', reject);
      });
    });
  }

  /**
   * Process a single file
   */
  private async processFile(filePath: string, projectId: string, result: ImportResult): Promise<void> {
    try {
      const fileName = path.basename(filePath);
      const detection = this.detectAssetType(fileName);

      if (!detection) {
        result.warnings.push(`Could not detect asset type for: ${fileName}`);
        result.itemsSkipped++;
        return;
      }

      // Create item
      const item = await this.createItem(filePath, detection, projectId);
      
      if (item) {
        // Check for conflicts (simplified)
        const hasConflict = await this.checkForConflicts(item);
        
        if (hasConflict) {
          result.conflicts.push({
            file: fileName,
            existingItem: 'existing-item-id', // Would be actual conflict
            conflictType: 'slot',
            resolution: 'rename'
          });
        }

        result.itemsImported++;
      } else {
        result.itemsSkipped++;
      }

    } catch (error) {
      result.errors.push({
        file: filePath,
        error: `Failed to process file: ${error.message}`,
        severity: 'error'
      });
    }
  }

  /**
   * Detect asset type from filename
   */
  private detectAssetType(fileName: string): AssetDetectionResult | null {
    for (const convention of NAMING_CONVENTIONS) {
      const match = fileName.match(convention.pattern);
      
      if (match) {
        const genderStr = match[convention.genderGroup];
        const slotStr = match[convention.slotGroup];
        const drawable = parseInt(match[convention.drawableGroup]);
        const texture = parseInt(match[convention.textureGroup]);

        const gender: Gender = genderStr === 'm' ? 'male' : 'female';
        
        // Determine if it's a component or prop
        const isComponent = this.isComponentSlot(slotStr);
        const kind: ItemKind = isComponent ? 'component' : 'prop';
        
        const slot = isComponent ? 
          this.mapToComponent(slotStr) : 
          this.mapToProp(slotStr);

        if (slot) {
          return {
            gender,
            slot,
            kind,
            drawable,
            texture,
            confidence: 0.9,
            source: 'filename'
          };
        }
      }
    }

    return null;
  }

  /**
   * Check if slot string represents a component
   */
  private isComponentSlot(slotStr: string): boolean {
    const componentSlots = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'];
    const namedSlots = ['face', 'mask', 'hair', 'torso', 'legs', 'bag', 'feet', 'accs', 'undershirt', 'armor', 'decals', 'tops'];
    
    return componentSlots.includes(slotStr) || namedSlots.includes(slotStr.toLowerCase());
  }

  /**
   * Map slot string to Component
   */
  private mapToComponent(slotStr: string): Component | null {
    const mapping: Record<string, Component> = {
      '0': 'face', 'face': 'face',
      '1': 'mask', 'mask': 'mask',
      '2': 'hair', 'hair': 'hair',
      '3': 'torso', 'torso': 'torso',
      '4': 'legs', 'legs': 'legs',
      '5': 'bag', 'bag': 'bag',
      '6': 'feet', 'feet': 'feet',
      '7': 'accs', 'accs': 'accs',
      '8': 'undershirt', 'undershirt': 'undershirt',
      '9': 'armor', 'armor': 'armor',
      '10': 'decals', 'decals': 'decals',
      '11': 'tops', 'tops': 'tops'
    };

    return mapping[slotStr.toLowerCase()] || null;
  }

  /**
   * Map slot string to Prop
   */
  private mapToProp(slotStr: string): Prop | null {
    const mapping: Record<string, Prop> = {
      '0': 'hat', 'hat': 'hat',
      '1': 'glasses', 'glasses': 'glasses',
      '2': 'ears', 'ears': 'ears',
      '6': 'watch', 'watch': 'watch',
      '7': 'bracelet', 'bracelet': 'bracelet'
    };

    return mapping[slotStr.toLowerCase()] || null;
  }

  /**
   * Create an Item from file and detection result
   */
  private async createItem(filePath: string, detection: AssetDetectionResult, projectId: string): Promise<Item | null> {
    try {
      const stats = await fs.stat(filePath);
      const content = await fs.readFile(filePath);
      const checksum = crypto.createHash('md5').update(content).digest('hex');

      const item: Item = {
        id: this.generateId(),
        gender: detection.gender,
        kind: detection.kind,
        slot: detection.slot,
        drawable: detection.drawable,
        textures: [detection.texture],
        files: {
          ydd: path.extname(filePath) === '.ydd' ? filePath : '',
          ytds: path.extname(filePath) === '.ytd' ? [filePath] : [],
          originalPath: filePath,
          size: stats.size,
          checksum
        },
        metadata: {
          imported: new Date(),
          modified: new Date(),
          source: projectId,
          version: '1.0.0'
        }
      };

      return item;
    } catch (error) {
      console.error('Failed to create item:', error);
      return null;
    }
  }

  /**
   * Check for conflicts with existing items
   */
  private async checkForConflicts(item: Item): Promise<boolean> {
    // This would check against existing project items
    // For now, return false (no conflicts)
    return false;
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }
}
