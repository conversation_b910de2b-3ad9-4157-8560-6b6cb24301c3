/**
 * UI Store - Manages application UI state and preferences
 */
interface UIStoreState {
    sidebarWidth: number;
    sidebarCollapsed: boolean;
    previewPanelWidth: number;
    previewPanelCollapsed: boolean;
    consolePanelHeight: number;
    activeView: 'tree' | 'grid' | 'list';
    activeTab: 'items' | 'preview' | 'qa' | 'export';
    filters: {
        gender?: 'male' | 'female';
        slot?: string;
        hasConflicts?: boolean;
        qaStatus?: 'passed' | 'failed' | 'pending';
        searchQuery?: string;
    };
    sortBy: 'name' | 'slot' | 'drawable' | 'modified';
    sortOrder: 'asc' | 'desc';
    showPreferences: boolean;
    showAbout: boolean;
    showProgress: boolean;
    showConsole: boolean;
    showItemDetails: boolean;
    showExportDialog: boolean;
    showImportDialog: boolean;
    progressOperations: Array<{
        id: string;
        title: string;
        progress: number;
        status: string;
        cancellable: boolean;
    }>;
    notifications: Array<{
        id: string;
        type: 'info' | 'success' | 'warning' | 'error';
        title: string;
        message: string;
        timestamp: Date;
        dismissed: boolean;
    }>;
    theme: 'light' | 'dark' | 'auto';
    fontSize: 'small' | 'medium' | 'large';
    compactMode: boolean;
    previewSettings: {
        renderQuality: 'low' | 'medium' | 'high';
        showWireframe: boolean;
        showBounds: boolean;
        autoRotate: boolean;
        currentPose: string;
        lighting: string;
        background: string;
    };
    consoleMessages: Array<{
        id: string;
        timestamp: Date;
        level: 'debug' | 'info' | 'warn' | 'error';
        message: string;
        source?: string;
    }>;
    consoleFilter: 'all' | 'debug' | 'info' | 'warn' | 'error';
}
interface UIStoreActions {
    setSidebarWidth: (width: number) => void;
    toggleSidebar: () => void;
    setPreviewPanelWidth: (width: number) => void;
    togglePreviewPanel: () => void;
    setConsolePanelHeight: (height: number) => void;
    setActiveView: (view: 'tree' | 'grid' | 'list') => void;
    setActiveTab: (tab: 'items' | 'preview' | 'qa' | 'export') => void;
    setFilters: (filters: Partial<UIStoreState['filters']>) => void;
    clearFilters: () => void;
    setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
    setShowPreferences: (show: boolean) => void;
    setShowAbout: (show: boolean) => void;
    setShowProgress: (show: boolean) => void;
    setShowConsole: (show: boolean) => void;
    setShowItemDetails: (show: boolean) => void;
    setShowExportDialog: (show: boolean) => void;
    setShowImportDialog: (show: boolean) => void;
    addProgressOperation: (operation: Omit<UIStoreState['progressOperations'][0], 'id'>) => string;
    updateProgressOperation: (id: string, updates: Partial<UIStoreState['progressOperations'][0]>) => void;
    removeProgressOperation: (id: string) => void;
    addNotification: (notification: Omit<UIStoreState['notifications'][0], 'id' | 'timestamp' | 'dismissed'>) => string;
    dismissNotification: (id: string) => void;
    clearNotifications: () => void;
    setTheme: (theme: 'light' | 'dark' | 'auto') => void;
    setFontSize: (size: 'small' | 'medium' | 'large') => void;
    setCompactMode: (compact: boolean) => void;
    setPreviewSettings: (settings: Partial<UIStoreState['previewSettings']>) => void;
    addConsoleMessage: (message: Omit<UIStoreState['consoleMessages'][0], 'id' | 'timestamp'>) => void;
    clearConsoleMessages: () => void;
    setConsoleFilter: (filter: UIStoreState['consoleFilter']) => void;
    loadSettings: () => Promise<void>;
    saveSettings: () => Promise<void>;
}
type UIStore = UIStoreState & UIStoreActions;
export declare const useUIStore: import("zustand").UseBoundStore<Omit<Omit<import("zustand").StoreApi<UIStore>, "subscribe"> & {
    subscribe: {
        (listener: (selectedState: UIStore, previousSelectedState: UIStore) => void): () => void;
        <U>(selector: (state: UIStore) => U, listener: (selectedState: U, previousSelectedState: U) => void, options?: {
            equalityFn?: ((a: U, b: U) => boolean) | undefined;
            fireImmediately?: boolean;
        } | undefined): () => void;
    };
}, "setState"> & {
    setState(nextStateOrUpdater: UIStore | Partial<UIStore> | ((state: import("immer").WritableDraft<UIStore>) => void), shouldReplace?: boolean | undefined): void;
}>;
export {};
