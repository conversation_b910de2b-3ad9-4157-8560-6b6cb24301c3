/**
 * Main Electron process for DripForge Pro
 */
import { BrowserWindow } from 'electron';
import Store from 'electron-store';
import { AppSettings } from '../types/core';
declare class DripForgeApp {
    private mainWindow;
    private store;
    private isDev;
    constructor();
    private initializeApp;
    private createMainWindow;
    private getAppIcon;
    private setupAppMenu;
    private initializeServices;
    private setupAutoUpdater;
    private showUnsavedChangesDialog;
    getMainWindow(): BrowserWindow | null;
    getStore(): Store<AppSettings>;
}
declare const dripForgeApp: DripForgeApp;
export { dripForgeApp };
