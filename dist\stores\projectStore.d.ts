/**
 * Project Store - Manages project state and operations
 */
import { Project, Item, SlotMapping, BatchOperation } from '../types/core';
import { ImportResult } from '../types/assets';
interface ProjectState {
    currentProject: Project | null;
    currentProjectPath: string | null;
    hasUnsavedChanges: boolean;
    items: Item[];
    selectedItems: string[];
    filteredItems: Item[];
    slotMappings: SlotMapping[];
    activeOperations: BatchOperation[];
    recentProjects: string[];
    isLoading: boolean;
    loadingMessage: string;
}
interface ProjectActions {
    createProject: (name: string, description?: string) => Promise<void>;
    loadProject: (filePath?: string) => Promise<void>;
    saveProject: (filePath?: string) => Promise<string | null>;
    closeProject: () => void;
    setUnsavedChanges: (hasChanges: boolean) => void;
    addItems: (items: Item[]) => void;
    removeItems: (itemIds: string[]) => void;
    updateItem: (itemId: string, updates: Partial<Item>) => void;
    selectItems: (itemIds: string[]) => void;
    clearSelection: () => void;
    filterItems: (filters: any) => void;
    importAssets: (paths: string[]) => Promise<ImportResult>;
    routeSlots: () => Promise<void>;
    runQualityAssurance: (itemIds?: string[]) => Promise<void>;
    exportProject: (config: any) => Promise<void>;
    startOperation: (operation: BatchOperation) => void;
    updateOperation: (operationId: string, updates: Partial<BatchOperation>) => void;
    completeOperation: (operationId: string) => void;
    cancelOperation: (operationId: string) => void;
    setLoading: (loading: boolean, message?: string) => void;
}
type ProjectStore = ProjectState & ProjectActions;
export declare const useProjectStore: import("zustand").UseBoundStore<Omit<Omit<import("zustand").StoreApi<ProjectStore>, "subscribe"> & {
    subscribe: {
        (listener: (selectedState: ProjectStore, previousSelectedState: ProjectStore) => void): () => void;
        <U>(selector: (state: ProjectStore) => U, listener: (selectedState: U, previousSelectedState: U) => void, options?: {
            equalityFn?: ((a: U, b: U) => boolean) | undefined;
            fireImmediately?: boolean;
        } | undefined): () => void;
    };
}, "setState"> & {
    setState(nextStateOrUpdater: ProjectStore | Partial<ProjectStore> | ((state: import("immer").WritableDraft<ProjectStore>) => void), shouldReplace?: boolean | undefined): void;
}>;
export {};
