/**
 * Main Electron process for DripForge Pro
 */

import { app, BrowserWindow, Menu, ipcMain, dialog, shell } from 'electron';
import { autoUpdater } from 'electron-updater';
import Store from 'electron-store';
import * as path from 'path';
import * as fs from 'fs-extra';
import { AppSettings } from '../types/core';
import { setupIpcHandlers } from './ipc-handlers';
import { createMenu } from './menu';
import { initializeServices } from './services';

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
  try {
    require('electron-reload')(__dirname, {
      electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
      hardResetMethod: 'exit'
    });
  } catch (error) {
    console.log('Electron reload not available in production build');
  }
}

class DripForgeApp {
  private mainWindow: BrowserWindow | null = null;
  private store: Store<AppSettings>;
  private isDev = process.env.NODE_ENV === 'development';

  constructor() {
    this.store = new Store<AppSettings>({
      defaults: {
        theme: 'dark',
        language: 'en',
        autoSave: true,
        autoSaveInterval: 300000, // 5 minutes
        maxRecentProjects: 10,
        tempDirectory: path.join(app.getPath('temp'), 'dripforge-pro'),
        logLevel: 'info'
      }
    });

    this.initializeApp();
  }

  private async initializeApp(): Promise<void> {
    // Set app user model ID for Windows
    if (process.platform === 'win32') {
      app.setAppUserModelId('com.dripforge.pro');
    }

    // Handle app events
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupAppMenu();
      this.initializeServices();
      this.setupAutoUpdater();
      
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('before-quit', async (event) => {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        event.preventDefault();
        const response = await this.showUnsavedChangesDialog();
        if (response === 'save' || response === 'discard') {
          app.quit();
        }
      }
    });

    // Setup IPC handlers
    setupIpcHandlers(this.store);
  }

  private createMainWindow(): void {
    const windowState = this.store.get('windowState', {
      width: 1400,
      height: 900,
      x: undefined,
      y: undefined,
      maximized: false
    });

    this.mainWindow = new BrowserWindow({
      width: windowState.width,
      height: windowState.height,
      x: windowState.x,
      y: windowState.y,
      minWidth: 1200,
      minHeight: 800,
      show: false,
      icon: this.getAppIcon(),
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: !this.isDev
      }
    });

    // Load the app
    if (this.isDev) {
      this.mainWindow.loadURL('http://localhost:4000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Window event handlers
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        if (windowState.maximized) {
          this.mainWindow.maximize();
        }
        this.mainWindow.show();
        
        // Focus on the window
        if (this.isDev) {
          this.mainWindow.focus();
        }
      }
    });

    this.mainWindow.on('close', (event) => {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        const bounds = this.mainWindow.getBounds();
        const maximized = this.mainWindow.isMaximized();
        
        this.store.set('windowState', {
          ...bounds,
          maximized
        });
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  private getAppIcon(): string {
    const iconName = process.platform === 'win32' ? 'icon.ico' : 'icon.png';
    return path.join(__dirname, '../assets/icons', iconName);
  }

  private setupAppMenu(): void {
    const menu = createMenu(this.mainWindow, this.store);
    Menu.setApplicationMenu(menu);
  }

  private async initializeServices(): Promise<void> {
    try {
      await initializeServices(this.store);
      console.log('Services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      dialog.showErrorBox(
        'Initialization Error',
        'Failed to initialize application services. Some features may not work correctly.'
      );
    }
  }

  private setupAutoUpdater(): void {
    if (this.isDev) return;

    autoUpdater.checkForUpdatesAndNotify();

    autoUpdater.on('update-available', () => {
      dialog.showMessageBox(this.mainWindow!, {
        type: 'info',
        title: 'Update Available',
        message: 'A new version of DripForge Pro is available. It will be downloaded in the background.',
        buttons: ['OK']
      });
    });

    autoUpdater.on('update-downloaded', () => {
      dialog.showMessageBox(this.mainWindow!, {
        type: 'info',
        title: 'Update Ready',
        message: 'Update downloaded. The application will restart to apply the update.',
        buttons: ['Restart Now', 'Later']
      }).then((result) => {
        if (result.response === 0) {
          autoUpdater.quitAndInstall();
        }
      });
    });
  }

  private async showUnsavedChangesDialog(): Promise<'save' | 'discard' | 'cancel'> {
    const result = await dialog.showMessageBox(this.mainWindow!, {
      type: 'warning',
      title: 'Unsaved Changes',
      message: 'You have unsaved changes. What would you like to do?',
      buttons: ['Save', 'Discard', 'Cancel'],
      defaultId: 0,
      cancelId: 2
    });

    switch (result.response) {
      case 0: return 'save';
      case 1: return 'discard';
      default: return 'cancel';
    }
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  public getStore(): Store<AppSettings> {
    return this.store;
  }
}

// Create app instance
const dripForgeApp = new DripForgeApp();

// Handle protocol for deep linking (future feature)
app.setAsDefaultProtocolClient('dripforge');

// Handle command line arguments
if (process.argv.length >= 2) {
  const filePath = process.argv[process.argv.length - 1];
  if (filePath && filePath.endsWith('.dfp')) {
    // Handle opening .dfp project files
    app.whenReady().then(() => {
      dripForgeApp.getMainWindow()?.webContents.send('open-project', filePath);
    });
  }
}

// Export for testing
export { dripForgeApp };
