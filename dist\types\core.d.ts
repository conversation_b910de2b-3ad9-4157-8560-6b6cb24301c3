/**
 * Core data types for DripForge Pro
 */
export type Gender = 'male' | 'female';
export type Component = 'face' | 'mask' | 'hair' | 'torso' | 'legs' | 'bag' | 'feet' | 'accs' | 'undershirt' | 'armor' | 'decals' | 'tops';
export type Prop = 'hat' | 'glasses' | 'ears' | 'watch' | 'bracelet';
export type ItemKind = 'component' | 'prop';
export interface QualityAssessment {
    clippingScore: number;
    lods: number;
    tris: number;
    notes: string[];
    passed: boolean;
    lastScanned: Date;
}
export interface FileReference {
    ydd: string;
    ytds: string[];
    originalPath: string;
    size: number;
    checksum: string;
}
export interface Item {
    id: string;
    gender: Gender;
    kind: ItemKind;
    slot: Component | Prop;
    drawable: number;
    textures: number[];
    files: FileReference;
    labels?: string[];
    qa?: QualityAssessment;
    metadata: {
        imported: Date;
        modified: Date;
        source: string;
        version: string;
    };
}
export interface SlotMapping {
    gender: Gender;
    slot: Component | Prop;
    drawable: number;
    texture: number;
    reserved: boolean;
    assignedTo?: string;
}
export interface Project {
    id: string;
    name: string;
    description: string;
    created: Date;
    modified: Date;
    items: Item[];
    slotMappings: SlotMapping[];
    settings: ProjectSettings;
    exportHistory: ExportRecord[];
}
export interface ProjectSettings {
    reservedRanges: ReservedRange[];
    qaSettings: QASettings;
    exportSettings: ExportSettings;
    previewSettings: PreviewSettings;
}
export interface ReservedRange {
    gender: Gender;
    slot: Component | Prop;
    startDrawable: number;
    endDrawable: number;
    description: string;
}
export interface QASettings {
    enableClippingDetection: boolean;
    clippingTolerance: number;
    requireLODs: boolean;
    maxTriangles: number;
    deepScanMode: boolean;
}
export interface ExportSettings {
    includeQBClothing: boolean;
    includeESX: boolean;
    includeIcons: boolean;
    includeMarketing: boolean;
    escrowMode: boolean;
    watermarkImages: boolean;
}
export interface PreviewSettings {
    defaultPose: AnimationPose;
    renderQuality: 'low' | 'medium' | 'high';
    enablePBR: boolean;
    showWireframe: boolean;
}
export type AnimationPose = 'idle' | 'run' | 'aim' | 'sit' | 'crouch';
export interface ExportRecord {
    id: string;
    timestamp: Date;
    format: ExportFormat[];
    itemCount: number;
    outputPath: string;
    success: boolean;
    errors?: string[];
}
export type ExportFormat = 'qb-clothing' | 'esx' | 'fivem-resource' | 'icons' | 'marketing';
export interface ConflictResolution {
    itemId: string;
    originalSlot: SlotMapping;
    newSlot: SlotMapping;
    reason: string;
    autoResolved: boolean;
}
export interface BatchOperation {
    id: string;
    type: 'import' | 'export' | 'qa' | 'route' | 'optimize';
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    total: number;
    startTime: Date;
    endTime?: Date;
    errors: string[];
    warnings: string[];
}
export interface AppSettings {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    autoSave: boolean;
    autoSaveInterval: number;
    maxRecentProjects: number;
    blenderPath?: string;
    tempDirectory: string;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
}
export interface UIState {
    selectedItems: string[];
    activeView: 'tree' | 'grid' | 'list';
    filters: {
        gender?: Gender;
        slot?: Component | Prop;
        hasConflicts?: boolean;
        qaStatus?: 'passed' | 'failed' | 'pending';
    };
    sortBy: 'name' | 'slot' | 'drawable' | 'modified';
    sortOrder: 'asc' | 'desc';
}
export declare const COMPONENT_SLOTS: Record<Component, number>;
export declare const PROP_SLOTS: Record<Prop, number>;
export declare const SLOT_TO_COMPONENT: Record<number, Component>;
export declare const SLOT_TO_PROP: Record<number, Prop>;
