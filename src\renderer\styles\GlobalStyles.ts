/**
 * Global styles for the application
 */

import { createGlobalStyle } from 'styled-components';
import { Theme } from './themes';

export const GlobalStyles = createGlobalStyle<{ theme: Theme }>`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html, body {
    height: 100%;
    font-family: ${props => props.theme.typography.fontFamily};
    font-size: ${props => props.theme.typography.fontSize.md};
    line-height: ${props => props.theme.typography.lineHeight.normal};
    color: ${props => props.theme.colors.text};
    background-color: ${props => props.theme.colors.background};
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Scrollbars */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.backgroundSecondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.border};
    border-radius: ${props => props.theme.borderRadius.sm};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.colors.borderHover};
  }

  ::-webkit-scrollbar-corner {
    background: ${props => props.theme.colors.backgroundSecondary};
  }

  /* Selection */
  ::selection {
    background: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.textInverse};
  }

  /* Focus styles */
  *:focus {
    outline: 2px solid ${props => props.theme.colors.borderFocus};
    outline-offset: 2px;
  }

  *:focus:not(:focus-visible) {
    outline: none;
  }

  /* Button reset */
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
  }

  /* Input reset */
  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    background: ${props => props.theme.colors.surface};
    border: 1px solid ${props => props.theme.colors.border};
    border-radius: ${props => props.theme.borderRadius.md};
    padding: ${props => props.theme.spacing.sm};
    transition: ${props => props.theme.transitions.fast};
  }

  input:hover, textarea:hover, select:hover {
    border-color: ${props => props.theme.colors.borderHover};
  }

  input:focus, textarea:focus, select:focus {
    border-color: ${props => props.theme.colors.borderFocus};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }

  /* Link styles */
  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    transition: ${props => props.theme.transitions.fast};
  }

  a:hover {
    color: ${props => props.theme.colors.primaryHover};
    text-decoration: underline;
  }

  /* Drag and drop styles */
  .drag-over {
    background: ${props => props.theme.colors.dropZoneActive} !important;
    border: 2px dashed ${props => props.theme.colors.dropZoneBorder} !important;
  }

  .dragging {
    opacity: 0.5;
  }

  /* Utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .text-xs { font-size: ${props => props.theme.typography.fontSize.xs}; }
  .text-sm { font-size: ${props => props.theme.typography.fontSize.sm}; }
  .text-md { font-size: ${props => props.theme.typography.fontSize.md}; }
  .text-lg { font-size: ${props => props.theme.typography.fontSize.lg}; }
  .text-xl { font-size: ${props => props.theme.typography.fontSize.xl}; }

  .font-normal { font-weight: ${props => props.theme.typography.fontWeight.normal}; }
  .font-medium { font-weight: ${props => props.theme.typography.fontWeight.medium}; }
  .font-semibold { font-weight: ${props => props.theme.typography.fontWeight.semibold}; }
  .font-bold { font-weight: ${props => props.theme.typography.fontWeight.bold}; }

  .text-primary { color: ${props => props.theme.colors.text}; }
  .text-secondary { color: ${props => props.theme.colors.textSecondary}; }
  .text-muted { color: ${props => props.theme.colors.textMuted}; }

  /* Animation keyframes */
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  @keyframes slideInUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideInDown {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Loading animations */
  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid ${props => props.theme.colors.border};
    border-top: 2px solid ${props => props.theme.colors.primary};
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Responsive breakpoints */
  @media (max-width: 768px) {
    .hide-mobile {
      display: none !important;
    }
  }

  @media (max-width: 1024px) {
    .hide-tablet {
      display: none !important;
    }
  }

  /* Print styles */
  @media print {
    * {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }

    a, a:visited {
      text-decoration: underline;
    }

    .no-print {
      display: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    * {
      border-color: currentColor;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
`;
