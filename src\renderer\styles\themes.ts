/**
 * Theme definitions for styled-components
 */

import 'styled-components';

// Extend the DefaultTheme interface
declare module 'styled-components' {
  export interface DefaultTheme extends Theme {}
}

export interface Theme {
  colors: {
    // Primary colors
    primary: string;
    primaryHover: string;
    primaryActive: string;
    
    // Background colors
    background: string;
    backgroundSecondary: string;
    backgroundTertiary: string;
    
    // Surface colors
    surface: string;
    surfaceHover: string;
    surfaceActive: string;
    
    // Text colors
    text: string;
    textSecondary: string;
    textMuted: string;
    textInverse: string;
    
    // Border colors
    border: string;
    borderHover: string;
    borderFocus: string;
    
    // Status colors
    success: string;
    warning: string;
    error: string;
    info: string;
    
    // Accent colors
    accent: string;
    accentHover: string;
    
    // Sidebar
    sidebarBackground: string;
    sidebarBorder: string;
    sidebarItemHover: string;
    sidebarItemActive: string;
    
    // Preview panel
    previewBackground: string;
    previewBorder: string;
    
    // Console
    consoleBackground: string;
    consoleBorder: string;
    consoleText: string;
    
    // Drag and drop
    dropZone: string;
    dropZoneActive: string;
    dropZoneBorder: string;
  };
  
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  
  typography: {
    fontFamily: string;
    fontFamilyMono: string;
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      xxl: string;
    };
    fontWeight: {
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    full: string;
  };
  
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  
  transitions: {
    fast: string;
    normal: string;
    slow: string;
  };
  
  zIndex: {
    dropdown: number;
    modal: number;
    tooltip: number;
    notification: number;
  };
}

export const darkTheme: Theme = {
  colors: {
    primary: '#007acc',
    primaryHover: '#1a8cdd',
    primaryActive: '#0066aa',
    
    background: '#1a1a1a',
    backgroundSecondary: '#2a2a2a',
    backgroundTertiary: '#3a3a3a',
    
    surface: '#2d2d2d',
    surfaceHover: '#3d3d3d',
    surfaceActive: '#4d4d4d',
    
    text: '#ffffff',
    textSecondary: '#cccccc',
    textMuted: '#999999',
    textInverse: '#000000',
    
    border: '#404040',
    borderHover: '#505050',
    borderFocus: '#007acc',
    
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8',
    
    accent: '#ff6b35',
    accentHover: '#ff7f50',
    
    sidebarBackground: '#252526',
    sidebarBorder: '#3e3e42',
    sidebarItemHover: '#2a2d2e',
    sidebarItemActive: '#37373d',
    
    previewBackground: '#1e1e1e',
    previewBorder: '#3e3e42',
    
    consoleBackground: '#1e1e1e',
    consoleBorder: '#3e3e42',
    consoleText: '#cccccc',
    
    dropZone: 'rgba(0, 122, 204, 0.1)',
    dropZoneActive: 'rgba(0, 122, 204, 0.2)',
    dropZoneBorder: '#007acc',
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif',
    fontFamilyMono: '"SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", monospace',
    fontSize: {
      xs: '11px',
      sm: '12px',
      md: '14px',
      lg: '16px',
      xl: '18px',
      xxl: '24px',
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.8,
    },
  },
  
  borderRadius: {
    sm: '2px',
    md: '4px',
    lg: '8px',
    full: '9999px',
  },
  
  shadows: {
    sm: '0 1px 2px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.1)',
  },
  
  transitions: {
    fast: '150ms ease',
    normal: '250ms ease',
    slow: '350ms ease',
  },
  
  zIndex: {
    dropdown: 1000,
    modal: 1050,
    tooltip: 1070,
    notification: 1080,
  },
};

export const lightTheme: Theme = {
  ...darkTheme,
  colors: {
    primary: '#007acc',
    primaryHover: '#1a8cdd',
    primaryActive: '#0066aa',
    
    background: '#ffffff',
    backgroundSecondary: '#f8f9fa',
    backgroundTertiary: '#e9ecef',
    
    surface: '#ffffff',
    surfaceHover: '#f8f9fa',
    surfaceActive: '#e9ecef',
    
    text: '#212529',
    textSecondary: '#6c757d',
    textMuted: '#adb5bd',
    textInverse: '#ffffff',
    
    border: '#dee2e6',
    borderHover: '#adb5bd',
    borderFocus: '#007acc',
    
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8',
    
    accent: '#ff6b35',
    accentHover: '#ff7f50',
    
    sidebarBackground: '#f8f9fa',
    sidebarBorder: '#dee2e6',
    sidebarItemHover: '#e9ecef',
    sidebarItemActive: '#dee2e6',
    
    previewBackground: '#ffffff',
    previewBorder: '#dee2e6',
    
    consoleBackground: '#f8f9fa',
    consoleBorder: '#dee2e6',
    consoleText: '#495057',
    
    dropZone: 'rgba(0, 122, 204, 0.05)',
    dropZoneActive: 'rgba(0, 122, 204, 0.1)',
    dropZoneBorder: '#007acc',
  },
};
