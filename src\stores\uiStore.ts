/**
 * UI Store - Manages application UI state and preferences
 */

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { UIState, AppSettings } from '../types/core';

interface UIStoreState {
  // Layout
  sidebarWidth: number;
  sidebarCollapsed: boolean;
  previewPanelWidth: number;
  previewPanelCollapsed: boolean;
  consolePanelHeight: number;
  
  // View state
  activeView: 'tree' | 'grid' | 'list';
  activeTab: 'items' | 'preview' | 'qa' | 'export';
  
  // Filters and sorting
  filters: {
    gender?: 'male' | 'female';
    slot?: string;
    hasConflicts?: boolean;
    qaStatus?: 'passed' | 'failed' | 'pending';
    searchQuery?: string;
  };
  sortBy: 'name' | 'slot' | 'drawable' | 'modified';
  sortOrder: 'asc' | 'desc';
  
  // Modals and dialogs
  showPreferences: boolean;
  showAbout: boolean;
  showProgress: boolean;
  showConsole: boolean;
  showItemDetails: boolean;
  showExportDialog: boolean;
  showImportDialog: boolean;
  
  // Progress and notifications
  progressOperations: Array<{
    id: string;
    title: string;
    progress: number;
    status: string;
    cancellable: boolean;
  }>;
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: Date;
    dismissed: boolean;
  }>;
  
  // Theme and appearance
  theme: 'light' | 'dark' | 'auto';
  fontSize: 'small' | 'medium' | 'large';
  compactMode: boolean;
  
  // 3D Preview settings
  previewSettings: {
    renderQuality: 'low' | 'medium' | 'high';
    showWireframe: boolean;
    showBounds: boolean;
    autoRotate: boolean;
    currentPose: string;
    lighting: string;
    background: string;
  };
  
  // Console
  consoleMessages: Array<{
    id: string;
    timestamp: Date;
    level: 'debug' | 'info' | 'warn' | 'error';
    message: string;
    source?: string;
  }>;
  consoleFilter: 'all' | 'debug' | 'info' | 'warn' | 'error';
}

interface UIStoreActions {
  // Layout actions
  setSidebarWidth: (width: number) => void;
  toggleSidebar: () => void;
  setPreviewPanelWidth: (width: number) => void;
  togglePreviewPanel: () => void;
  setConsolePanelHeight: (height: number) => void;
  
  // View actions
  setActiveView: (view: 'tree' | 'grid' | 'list') => void;
  setActiveTab: (tab: 'items' | 'preview' | 'qa' | 'export') => void;
  
  // Filter and sort actions
  setFilters: (filters: Partial<UIStoreState['filters']>) => void;
  clearFilters: () => void;
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  
  // Modal actions
  setShowPreferences: (show: boolean) => void;
  setShowAbout: (show: boolean) => void;
  setShowProgress: (show: boolean) => void;
  setShowConsole: (show: boolean) => void;
  setShowItemDetails: (show: boolean) => void;
  setShowExportDialog: (show: boolean) => void;
  setShowImportDialog: (show: boolean) => void;
  
  // Progress actions
  addProgressOperation: (operation: Omit<UIStoreState['progressOperations'][0], 'id'>) => string;
  updateProgressOperation: (id: string, updates: Partial<UIStoreState['progressOperations'][0]>) => void;
  removeProgressOperation: (id: string) => void;
  
  // Notification actions
  addNotification: (notification: Omit<UIStoreState['notifications'][0], 'id' | 'timestamp' | 'dismissed'>) => string;
  dismissNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Theme actions
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  setFontSize: (size: 'small' | 'medium' | 'large') => void;
  setCompactMode: (compact: boolean) => void;
  
  // Preview actions
  setPreviewSettings: (settings: Partial<UIStoreState['previewSettings']>) => void;
  
  // Console actions
  addConsoleMessage: (message: Omit<UIStoreState['consoleMessages'][0], 'id' | 'timestamp'>) => void;
  clearConsoleMessages: () => void;
  setConsoleFilter: (filter: UIStoreState['consoleFilter']) => void;
  
  // Persistence
  loadSettings: () => Promise<void>;
  saveSettings: () => Promise<void>;
}

type UIStore = UIStoreState & UIStoreActions;

export const useUIStore = create<UIStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial state
      sidebarWidth: 300,
      sidebarCollapsed: false,
      previewPanelWidth: 400,
      previewPanelCollapsed: false,
      consolePanelHeight: 200,
      
      activeView: 'tree',
      activeTab: 'items',
      
      filters: {},
      sortBy: 'name',
      sortOrder: 'asc',
      
      showPreferences: false,
      showAbout: false,
      showProgress: false,
      showConsole: false,
      showItemDetails: false,
      showExportDialog: false,
      showImportDialog: false,
      
      progressOperations: [],
      notifications: [],
      
      theme: 'dark',
      fontSize: 'medium',
      compactMode: false,
      
      previewSettings: {
        renderQuality: 'medium',
        showWireframe: false,
        showBounds: false,
        autoRotate: false,
        currentPose: 'idle',
        lighting: 'studio',
        background: 'gradient'
      },
      
      consoleMessages: [],
      consoleFilter: 'all',

      // Layout actions
      setSidebarWidth: (width: number) => {
        set((state) => {
          state.sidebarWidth = Math.max(200, Math.min(600, width));
        });
      },

      toggleSidebar: () => {
        set((state) => {
          state.sidebarCollapsed = !state.sidebarCollapsed;
        });
      },

      setPreviewPanelWidth: (width: number) => {
        set((state) => {
          state.previewPanelWidth = Math.max(300, Math.min(800, width));
        });
      },

      togglePreviewPanel: () => {
        set((state) => {
          state.previewPanelCollapsed = !state.previewPanelCollapsed;
        });
      },

      setConsolePanelHeight: (height: number) => {
        set((state) => {
          state.consolePanelHeight = Math.max(100, Math.min(400, height));
        });
      },

      // View actions
      setActiveView: (view: 'tree' | 'grid' | 'list') => {
        set((state) => {
          state.activeView = view;
        });
      },

      setActiveTab: (tab: 'items' | 'preview' | 'qa' | 'export') => {
        set((state) => {
          state.activeTab = tab;
        });
      },

      // Filter and sort actions
      setFilters: (filters: Partial<UIStoreState['filters']>) => {
        set((state) => {
          Object.assign(state.filters, filters);
        });
      },

      clearFilters: () => {
        set((state) => {
          state.filters = {};
        });
      },

      setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => {
        set((state) => {
          state.sortBy = sortBy as any;
          state.sortOrder = sortOrder;
        });
      },

      // Modal actions
      setShowPreferences: (show: boolean) => {
        set((state) => {
          state.showPreferences = show;
        });
      },

      setShowAbout: (show: boolean) => {
        set((state) => {
          state.showAbout = show;
        });
      },

      setShowProgress: (show: boolean) => {
        set((state) => {
          state.showProgress = show;
        });
      },

      setShowConsole: (show: boolean) => {
        set((state) => {
          state.showConsole = show;
        });
      },

      setShowItemDetails: (show: boolean) => {
        set((state) => {
          state.showItemDetails = show;
        });
      },

      setShowExportDialog: (show: boolean) => {
        set((state) => {
          state.showExportDialog = show;
        });
      },

      setShowImportDialog: (show: boolean) => {
        set((state) => {
          state.showImportDialog = show;
        });
      },

      // Progress actions
      addProgressOperation: (operation) => {
        const id = `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        set((state) => {
          state.progressOperations.push({ ...operation, id });
          state.showProgress = true;
        });
        return id;
      },

      updateProgressOperation: (id: string, updates) => {
        set((state) => {
          const index = state.progressOperations.findIndex(op => op.id === id);
          if (index !== -1) {
            Object.assign(state.progressOperations[index], updates);
          }
        });
      },

      removeProgressOperation: (id: string) => {
        set((state) => {
          state.progressOperations = state.progressOperations.filter(op => op.id !== id);
          if (state.progressOperations.length === 0) {
            state.showProgress = false;
          }
        });
      },

      // Notification actions
      addNotification: (notification) => {
        const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        set((state) => {
          state.notifications.push({
            ...notification,
            id,
            timestamp: new Date(),
            dismissed: false
          });
        });

        // Auto-dismiss info and success notifications after 5 seconds
        if (notification.type === 'info' || notification.type === 'success') {
          setTimeout(() => {
            get().dismissNotification(id);
          }, 5000);
        }

        return id;
      },

      dismissNotification: (id: string) => {
        set((state) => {
          const index = state.notifications.findIndex(n => n.id === id);
          if (index !== -1) {
            state.notifications[index].dismissed = true;
          }
        });
      },

      clearNotifications: () => {
        set((state) => {
          state.notifications = [];
        });
      },

      // Theme actions
      setTheme: (theme: 'light' | 'dark' | 'auto') => {
        set((state) => {
          state.theme = theme;
        });
      },

      setFontSize: (size: 'small' | 'medium' | 'large') => {
        set((state) => {
          state.fontSize = size;
        });
      },

      setCompactMode: (compact: boolean) => {
        set((state) => {
          state.compactMode = compact;
        });
      },

      // Preview actions
      setPreviewSettings: (settings) => {
        set((state) => {
          Object.assign(state.previewSettings, settings);
        });
      },

      // Console actions
      addConsoleMessage: (message) => {
        const id = `console_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        set((state) => {
          state.consoleMessages.push({
            ...message,
            id,
            timestamp: new Date()
          });

          // Keep only last 1000 messages
          if (state.consoleMessages.length > 1000) {
            state.consoleMessages = state.consoleMessages.slice(-1000);
          }
        });
      },

      clearConsoleMessages: () => {
        set((state) => {
          state.consoleMessages = [];
        });
      },

      setConsoleFilter: (filter) => {
        set((state) => {
          state.consoleFilter = filter;
        });
      },

      // Persistence
      loadSettings: async () => {
        try {
          const settings = await window.electronAPI.settings.get();
          
          set((state) => {
            if (settings.theme) state.theme = settings.theme;
            if (settings.fontSize) state.fontSize = settings.fontSize;
            if (settings.compactMode !== undefined) state.compactMode = settings.compactMode;
            // Load other persisted settings
          });
        } catch (error) {
          console.error('Failed to load UI settings:', error);
        }
      },

      saveSettings: async () => {
        try {
          const { theme, fontSize, compactMode, sidebarWidth, previewPanelWidth } = get();
          
          await window.electronAPI.settings.set('uiSettings', {
            theme,
            fontSize,
            compactMode,
            sidebarWidth,
            previewPanelWidth
          });
        } catch (error) {
          console.error('Failed to save UI settings:', error);
        }
      }
    }))
  )
);

// Auto-save settings when they change
useUIStore.subscribe(
  (state) => ({
    theme: state.theme,
    fontSize: state.fontSize,
    compactMode: state.compactMode,
    sidebarWidth: state.sidebarWidth,
    previewPanelWidth: state.previewPanelWidth
  }),
  () => {
    // Debounce the save operation
    const timeoutId = setTimeout(() => {
      useUIStore.getState().saveSettings();
    }, 1000);

    return () => clearTimeout(timeoutId);
  },
  { equalityFn: (a, b) => JSON.stringify(a) === JSON.stringify(b) }
);
