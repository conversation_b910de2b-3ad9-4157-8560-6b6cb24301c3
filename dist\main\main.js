/*! For license information please see main.js.LICENSE.txt */
(()=>{var e={9:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DifferentialDownloader=void 0;const n=r(6551),s=r(4652),o=r(9896),a=r(2652),i=r(7016),c=r(1305),l=r(4405),u=r(9516);function d(e,t=" KB"){return new Intl.NumberFormat("en").format((e/1024).toFixed(2))+t}t.DifferentialDownloader=class{constructor(e,t,r){this.blockAwareFileInfo=e,this.httpExecutor=t,this.options=r,this.fileMetadataBuffer=null,this.logger=r.logger}createRequestOptions(){const e={headers:{...this.options.requestHeaders,accept:"*/*"}};return(0,n.configureRequestUrl)(this.options.newUrl,e),(0,n.configureRequestOptions)(e),e}doDownload(e,t){if(e.version!==t.version)throw new Error(`version is different (${e.version} - ${t.version}), full download is required`);const r=this.logger,n=(0,c.computeOperations)(e,t,r);null!=r.debug&&r.debug(JSON.stringify(n,null,2));let s=0,o=0;for(const e of n){const t=e.end-e.start;e.kind===c.OperationKind.DOWNLOAD?s+=t:o+=t}const a=this.blockAwareFileInfo.size;if(s+o+(null==this.fileMetadataBuffer?0:this.fileMetadataBuffer.length)!==a)throw new Error(`Internal error, size mismatch: downloadSize: ${s}, copySize: ${o}, newSize: ${a}`);return r.info(`Full: ${d(a)}, To download: ${d(s)} (${Math.round(s/(a/100))}%)`),this.downloadFile(n)}downloadFile(e){const t=[],r=()=>Promise.all(t.map(e=>(0,s.close)(e.descriptor).catch(t=>{this.logger.error(`cannot close file "${e.path}": ${t}`)})));return this.doDownloadFile(e,t).then(r).catch(e=>r().catch(t=>{try{this.logger.error(`cannot close files: ${t}`)}catch(e){try{console.error(e)}catch(e){}}throw e}).then(()=>{throw e}))}async doDownloadFile(e,t){const r=await(0,s.open)(this.options.oldFile,"r");t.push({descriptor:r,path:this.options.oldFile});const d=await(0,s.open)(this.options.newFile,"w");t.push({descriptor:d,path:this.options.newFile});const p=(0,o.createWriteStream)(this.options.newFile,{fd:d});await new Promise((s,o)=>{const d=[];let h;if(!this.options.isUseMultipleRangeRequest&&this.options.onProgress){const t=[];let r=0;for(const n of e)n.kind===c.OperationKind.DOWNLOAD&&(t.push(n.end-n.start),r+=n.end-n.start);const n={expectedByteCounts:t,grandTotal:r};h=new u.ProgressDifferentialDownloadCallbackTransform(n,this.options.cancellationToken,this.options.onProgress),d.push(h)}const f=new n.DigestTransform(this.blockAwareFileInfo.sha512);f.isValidateOnEnd=!1,d.push(f),p.on("finish",()=>{p.close(()=>{t.splice(1,1);try{f.validate()}catch(e){return void o(e)}s(void 0)})}),d.push(p);let m=null;for(const e of d)e.on("error",o),m=null==m?e:m.pipe(e);const g=d[0];let y;if(this.options.isUseMultipleRangeRequest)return y=(0,l.executeTasksUsingMultipleRangeRequests)(this,e,g,r,o),void y(0);let v=0,w=null;this.logger.info(`Differential download: ${this.options.newUrl}`);const _=this.createRequestOptions();_.redirect="manual",y=t=>{var s,l;if(t>=e.length)return null!=this.fileMetadataBuffer&&g.write(this.fileMetadataBuffer),void g.end();const u=e[t++];if(u.kind===c.OperationKind.COPY)return h&&h.beginFileCopy(),void(0,a.copyData)(u,g,r,o,()=>y(t));const d=`bytes=${u.start}-${u.end-1}`;_.headers.range=d,null===(l=null===(s=this.logger)||void 0===s?void 0:s.debug)||void 0===l||l.call(s,`download range: ${d}`),h&&h.beginRangeDownload();const p=this.httpExecutor.createRequest(_,e=>{e.on("error",o),e.on("aborted",()=>{o(new Error("response has been aborted by the server"))}),e.statusCode>=400&&o((0,n.createHttpError)(e)),e.pipe(g,{end:!1}),e.once("end",()=>{h&&h.endRangeDownload(),100===++v?(v=0,setTimeout(()=>y(t),1e3)):y(t)})});p.on("redirect",(e,t,r)=>{this.logger.info(`Redirect to ${function(e){const t=e.indexOf("?");return t<0?e:e.substring(0,t)}(r)}`),w=r,(0,n.configureRequestUrl)(new i.URL(w),_),p.followRedirect()}),this.httpExecutor.addErrorAndTimeoutHandlers(p,o),p.end()},y(0)})}async readRemoteBytes(e,t){const r=Buffer.allocUnsafe(t+1-e),n=this.createRequestOptions();n.headers.range=`bytes=${e}-${t}`;let s=0;if(await this.request(n,e=>{e.copy(r,s),s+=e.length}),s!==r.length)throw new Error(`Received data length ${s} is not equal to expected ${r.length}`);return r}request(e,t){return new Promise((r,n)=>{const s=this.httpExecutor.createRequest(e,e=>{(0,l.checkIsRangesSupported)(e,n)&&(e.on("error",n),e.on("aborted",()=>{n(new Error("response has been aborted by the server"))}),e.on("data",t),e.on("end",()=>r()))});this.httpExecutor.addErrorAndTimeoutHandlers(s,n),s.end()})}}},111:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;const n=r(3186),s=r(903);var o=r(3186);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return o._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return o.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return o.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return o.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return o.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return o.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return o.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return o.Name}});var a=r(903);Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return a.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return a.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return a.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return a.varKinds}}),t.operators={GT:new n._Code(">"),GTE:new n._Code(">="),LT:new n._Code("<"),LTE:new n._Code("<="),EQ:new n._Code("==="),NEQ:new n._Code("!=="),NOT:new n._Code("!"),OR:new n._Code("||"),AND:new n._Code("&&"),ADD:new n._Code("+")};class i{optimizeNodes(){return this}optimizeNames(e,t){return this}}class c extends i{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){const r=e?s.varKinds.var:this.varKind,n=void 0===this.rhs?"":` = ${this.rhs}`;return`${r} ${this.name}${n};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=A(this.rhs,e,t)),this}get names(){return this.rhs instanceof n._CodeOrName?this.rhs.names:{}}}class l extends i{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof n.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=A(this.rhs,e,t),this}get names(){return T(this.lhs instanceof n.Name?{}:{...this.lhs.names},this.rhs)}}class u extends l{constructor(e,t,r,n){super(e,r,n),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class d extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class p extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`break${this.label?` ${this.label}`:""};`+e}}class h extends i{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class f extends i{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=A(this.code,e,t),this}get names(){return this.code instanceof n._CodeOrName?this.code.names:{}}}class m extends i{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce((t,r)=>t+r.render(e),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;for(;t--;){const r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){const{nodes:r}=this;let n=r.length;for(;n--;){const s=r[n];s.optimizeNames(e,t)||(k(e,s.names),r.splice(n,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce((e,t)=>I(e,t.names),{})}}class g extends m{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class y extends m{}class v extends g{}v.kind="else";class w extends g{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new v(e):e}return t?!1===e?t instanceof w?t:t.nodes:this.nodes.length?this:new w(R(e),t instanceof w?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null===(r=this.else)||void 0===r?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=A(this.condition,e,t),this}get names(){const e=super.names;return T(e,this.condition),this.else&&I(e,this.else.names),e}}w.kind="if";class _ extends g{}_.kind="for";class b extends _{constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=A(this.iteration,e,t),this}get names(){return I(super.names,this.iteration.names)}}class $ extends _{constructor(e,t,r,n){super(),this.varKind=e,this.name=t,this.from=r,this.to=n}render(e){const t=e.es5?s.varKinds.var:this.varKind,{name:r,from:n,to:o}=this;return`for(${t} ${r}=${n}; ${r}<${o}; ${r}++)`+super.render(e)}get names(){const e=T(super.names,this.from);return T(e,this.to)}}class E extends _{constructor(e,t,r,n){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=n}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=A(this.iterable,e,t),this}get names(){return I(super.names,this.iterable.names)}}class S extends g{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(e)}}S.kind="func";class P extends m{render(e){return"return "+super.render(e)}}P.kind="return";class O extends g{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null===(e=this.catch)||void 0===e||e.optimizeNodes(),null===(t=this.finally)||void 0===t||t.optimizeNodes(),this}optimizeNames(e,t){var r,n;return super.optimizeNames(e,t),null===(r=this.catch)||void 0===r||r.optimizeNames(e,t),null===(n=this.finally)||void 0===n||n.optimizeNames(e,t),this}get names(){const e=super.names;return this.catch&&I(e,this.catch.names),this.finally&&I(e,this.finally.names),e}}class C extends g{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}C.kind="catch";class N extends g{render(e){return"finally"+super.render(e)}}function I(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function T(e,t){return t instanceof n._CodeOrName?I(e,t.names):e}function A(e,t,r){return e instanceof n.Name?o(e):(s=e)instanceof n._Code&&s._items.some(e=>e instanceof n.Name&&1===t[e.str]&&void 0!==r[e.str])?new n._Code(e._items.reduce((e,t)=>(t instanceof n.Name&&(t=o(t)),t instanceof n._Code?e.push(...t._items):e.push(t),e),[])):e;var s;function o(e){const n=r[e.str];return void 0===n||1!==t[e.str]?e:(delete t[e.str],n)}}function k(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function R(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:n._`!${M(e)}`}N.kind="finally",t.CodeGen=class{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new s.Scope({parent:e}),this._nodes=[new y]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,n){const s=this._scope.toName(t);return void 0!==r&&n&&(this._constants[s.str]=r),this._leafNode(new c(e,s,r)),s}const(e,t,r){return this._def(s.varKinds.const,e,t,r)}let(e,t,r){return this._def(s.varKinds.let,e,t,r)}var(e,t,r){return this._def(s.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new l(e,t,r))}add(e,r){return this._leafNode(new u(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==n.nil&&this._leafNode(new f(e)),this}object(...e){const t=["{"];for(const[r,s]of e)t.length>1&&t.push(","),t.push(r),(r!==s||this.opts.es5)&&(t.push(":"),(0,n.addCodeArg)(t,s));return t.push("}"),new n._Code(t)}if(e,t,r){if(this._blockNode(new w(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new w(e))}else(){return this._elseNode(new v)}endIf(){return this._endBlockNode(w,v)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new b(e),t)}forRange(e,t,r,n,o=(this.opts.es5?s.varKinds.var:s.varKinds.let)){const a=this._scope.toName(e);return this._for(new $(o,a,t,r),()=>n(a))}forOf(e,t,r,o=s.varKinds.const){const a=this._scope.toName(e);if(this.opts.es5){const e=t instanceof n.Name?t:this.var("_arr",t);return this.forRange("_i",0,n._`${e}.length`,t=>{this.var(a,n._`${e}[${t}]`),r(a)})}return this._for(new E("of",o,a,t),()=>r(a))}forIn(e,t,r,o=(this.opts.es5?s.varKinds.var:s.varKinds.const)){if(this.opts.ownProperties)return this.forOf(e,n._`Object.keys(${t})`,r);const a=this._scope.toName(e);return this._for(new E("in",o,a,t),()=>r(a))}endFor(){return this._endBlockNode(_)}label(e){return this._leafNode(new d(e))}break(e){return this._leafNode(new p(e))}return(e){const t=new P;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(P)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const n=new O;if(this._blockNode(n),this.code(e),t){const e=this.name("e");this._currNode=n.catch=new C(e),t(e)}return r&&(this._currNode=n.finally=new N,this.code(r)),this._endBlockNode(C,N)}throw(e){return this._leafNode(new h(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){const t=this._blockStarts.pop();if(void 0===t)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=n.nil,r,s){return this._blockNode(new S(e,t,r)),s&&this.code(s).endFunc(),this}endFunc(){return this._endBlockNode(S)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof w))throw new Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}},t.not=R;const x=j(t.operators.AND);t.and=function(...e){return e.reduce(x)};const D=j(t.operators.OR);function j(e){return(t,r)=>t===n.nil?r:r===n.nil?t:n._`${M(t)} ${e} ${M(r)}`}function M(e){return e instanceof n.Name?e:n._`(${e})`}t.or=function(...e){return e.reduce(D)}},127:(e,t,r)=>{"use strict";var n=r(5388),s=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),o=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");e.exports=new n("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:function(e){return null!==e&&(null!==s.exec(e)||null!==o.exec(e))},construct:function(e){var t,r,n,a,i,c,l,u,d=0,p=null;if(null===(t=s.exec(e))&&(t=o.exec(e)),null===t)throw new Error("Date resolve error");if(r=+t[1],n=+t[2]-1,a=+t[3],!t[4])return new Date(Date.UTC(r,n,a));if(i=+t[4],c=+t[5],l=+t[6],t[7]){for(d=t[7].slice(0,3);d.length<3;)d+="0";d=+d}return t[9]&&(p=6e4*(60*+t[10]+ +(t[11]||0)),"-"===t[9]&&(p=-p)),u=new Date(Date.UTC(r,n,a,i,c,l,d)),p&&u.setTime(u.getTime()-p),u},instanceOf:Date,represent:function(e){return e.toISOString()}})},144:(e,t,r)=>{"use strict";const n=r(3908);e.exports=(e,t,r=!1)=>{if(e instanceof n)return e;try{return new n(e,t)}catch(e){if(!r)return null;throw e}}},270:(e,t,r)=>{"use strict";const n=r(3908),s=r(8311);e.exports=(e,t,r)=>{let o=null,a=null,i=null;try{i=new s(t,r)}catch(e){return null}return e.forEach(e=>{i.test(e)&&(o&&1!==a.compare(e)||(o=e,a=new n(o,r)))}),o}},302:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1085),s=r(3789),o={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>s.str`must match pattern "${e}"`,params:({schemaCode:e})=>s._`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:o,schemaCode:a,it:i}=e,c=i.opts.unicodeRegExp?"u":"",l=r?s._`(new RegExp(${a}, ${c}))`:(0,n.usePattern)(e,o);e.fail$data(s._`!${l}.test(${t})`)}};t.default=o},303:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3043);class s extends Error{constructor(e,t,r,s){super(s||`can't resolve reference ${r} from id ${t}`),this.missingRef=(0,n.resolveUrl)(e,t,r),this.missingSchema=(0,n.normalizeId)((0,n.getFullPath)(e,this.missingRef))}}t.default=s},329:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>n.str`must match format "${e}"`,params:({schemaCode:e})=>n._`{format: ${e}}`},code(e,t){const{gen:r,data:s,$data:o,schema:a,schemaCode:i,it:c}=e,{opts:l,errSchemaPath:u,schemaEnv:d,self:p}=c;l.validateFormats&&(o?function(){const o=r.scopeValue("formats",{ref:p.formats,code:l.code.formats}),a=r.const("fDef",n._`${o}[${i}]`),c=r.let("fType"),u=r.let("format");r.if(n._`typeof ${a} == "object" && !(${a} instanceof RegExp)`,()=>r.assign(c,n._`${a}.type || "string"`).assign(u,n._`${a}.validate`),()=>r.assign(c,n._`"string"`).assign(u,a)),e.fail$data((0,n.or)(!1===l.strictSchema?n.nil:n._`${i} && !${u}`,function(){const e=d.$async?n._`(${a}.async ? await ${u}(${s}) : ${u}(${s}))`:n._`${u}(${s})`,r=n._`(typeof ${u} == "function" ? ${e} : ${u}.test(${s}))`;return n._`${u} && ${u} !== true && ${c} === ${t} && !${r}`}()))}():function(){const o=p.formats[a];if(!o)return void function(){if(!1!==l.strictSchema)throw new Error(e());function e(){return`unknown format "${a}" ignored in schema at path "${u}"`}p.logger.warn(e())}();if(!0===o)return;const[i,c,h]=function(e){const t=e instanceof RegExp?(0,n.regexpCode)(e):l.code.formats?n._`${l.code.formats}${(0,n.getProperty)(a)}`:void 0,s=r.scopeValue("formats",{key:a,ref:e,code:t});return"object"!=typeof e||e instanceof RegExp?["string",e,s]:[e.type||"string",e.validate,n._`${s}.validate`]}(o);i===t&&e.pass(function(){if("object"==typeof o&&!(o instanceof RegExp)&&o.async){if(!d.$async)throw new Error("async format in sync schema");return n._`await ${h}(${s})`}return"function"==typeof c?n._`${h}(${s})`:n._`${h}.test(${s})`}())}())}};t.default=s},343:(e,t,r)=>{"use strict";const{isUUID:n}=r(4834),s=/([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu,o=["http","https","ws","wss","urn","urn:uuid"];function a(e){return!0===e.secure||!1!==e.secure&&!!e.scheme&&!(3!==e.scheme.length||"w"!==e.scheme[0]&&"W"!==e.scheme[0]||"s"!==e.scheme[1]&&"S"!==e.scheme[1]||"s"!==e.scheme[2]&&"S"!==e.scheme[2])}function i(e){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e}function c(e){const t="https"===String(e.scheme).toLowerCase();return e.port!==(t?443:80)&&""!==e.port||(e.port=void 0),e.path||(e.path="/"),e}const l={scheme:"http",domainHost:!0,parse:i,serialize:c},u={scheme:"ws",domainHost:!0,parse:function(e){return e.secure=a(e),e.resourceName=(e.path||"/")+(e.query?"?"+e.query:""),e.path=void 0,e.query=void 0,e},serialize:function(e){if(e.port!==(a(e)?443:80)&&""!==e.port||(e.port=void 0),"boolean"==typeof e.secure&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){const[t,r]=e.resourceName.split("?");e.path=t&&"/"!==t?t:void 0,e.query=r,e.resourceName=void 0}return e.fragment=void 0,e}},d={http:l,https:{scheme:"https",domainHost:l.domainHost,parse:i,serialize:c},ws:u,wss:{scheme:"wss",domainHost:u.domainHost,parse:u.parse,serialize:u.serialize},urn:{scheme:"urn",parse:function(e,t){if(!e.path)return e.error="URN can not be parsed",e;const r=e.path.match(s);if(r){const n=t.scheme||e.scheme||"urn";e.nid=r[1].toLowerCase(),e.nss=r[2];const s=p(`${n}:${t.nid||e.nid}`);e.path=void 0,s&&(e=s.parse(e,t))}else e.error=e.error||"URN can not be parsed.";return e},serialize:function(e,t){if(void 0===e.nid)throw new Error("URN without nid cannot be serialized");const r=t.scheme||e.scheme||"urn",n=e.nid.toLowerCase(),s=p(`${r}:${t.nid||n}`);s&&(e=s.serialize(e,t));const o=e,a=e.nss;return o.path=`${n||t.nid}:${a}`,t.skipEscape=!0,o},skipNormalize:!0},"urn:uuid":{scheme:"urn:uuid",parse:function(e,t){const r=e;return r.uuid=r.nss,r.nss=void 0,t.tolerant||r.uuid&&n(r.uuid)||(r.error=r.error||"UUID is not valid."),r},serialize:function(e){const t=e;return t.nss=(e.uuid||"").toLowerCase(),t},skipNormalize:!0}};function p(e){return e&&(d[e]||d[e.toLowerCase()])||void 0}Object.setPrototypeOf(d,null),e.exports={wsIsSecure:a,SCHEMES:d,isValidSchemeName:function(e){return-1!==o.indexOf(e)},getSchemeHandler:p}},344:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(7103),s=r(9045),o=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",n.default,s.default];t.default=o},369:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ElectronHttpExecutor=t.NET_SESSION_NAME=void 0,t.getNetSession=s;const n=r(6551);function s(){return r(4157).session.fromPartition(t.NET_SESSION_NAME,{cache:!1})}t.NET_SESSION_NAME="electron-updater";class o extends n.HttpExecutor{constructor(e){super(),this.proxyLoginCallback=e,this.cachedSession=null}async download(e,t,r){return await r.cancellationToken.createPromise((s,o,a)=>{const i={headers:r.headers||void 0,redirect:"manual"};(0,n.configureRequestUrl)(e,i),(0,n.configureRequestOptions)(i),this.doDownload(i,{destination:t,options:r,onCancel:a,callback:e=>{null==e?s(t):o(e)},responseHandler:null},0)})}createRequest(e,t){e.headers&&e.headers.Host&&(e.host=e.headers.Host,delete e.headers.Host),null==this.cachedSession&&(this.cachedSession=s());const n=r(4157).net.request({...e,session:this.cachedSession});return n.on("response",t),null!=this.proxyLoginCallback&&n.on("login",this.proxyLoginCallback),n}addRedirectHandlers(e,t,r,s,o){e.on("redirect",(a,i,c)=>{e.abort(),s>this.maxRedirects?r(this.createMaxRedirectError()):o(n.HttpExecutor.prepareRedirectUrlOptions(c,t))})}}t.ElectronHttpExecutor=o},396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1085),s=r(3789),o=r(6735),a=r(7083),i={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>s._`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:i,data:c,errsCount:l,it:u}=e;if(!l)throw new Error("ajv implementation error");const{allErrors:d,opts:p}=u;if(u.props=!0,"all"!==p.removeAdditional&&(0,a.alwaysValidSchema)(u,r))return;const h=(0,n.allSchemaProperties)(i.properties),f=(0,n.allSchemaProperties)(i.patternProperties);function m(e){t.code(s._`delete ${c}[${e}]`)}function g(n){if("all"===p.removeAdditional||p.removeAdditional&&!1===r)m(n);else{if(!1===r)return e.setParams({additionalProperty:n}),e.error(),void(d||t.break());if("object"==typeof r&&!(0,a.alwaysValidSchema)(u,r)){const r=t.name("valid");"failing"===p.removeAdditional?(y(n,r,!1),t.if((0,s.not)(r),()=>{e.reset(),m(n)})):(y(n,r),d||t.if((0,s.not)(r),()=>t.break()))}}}function y(t,r,n){const s={keyword:"additionalProperties",dataProp:t,dataPropType:a.Type.Str};!1===n&&Object.assign(s,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(s,r)}t.forIn("key",c,r=>{h.length||f.length?t.if(function(r){let o;if(h.length>8){const e=(0,a.schemaRefOrVal)(u,i.properties,"properties");o=(0,n.isOwnProperty)(t,e,r)}else o=h.length?(0,s.or)(...h.map(e=>s._`${r} === ${e}`)):s.nil;return f.length&&(o=(0,s.or)(o,...f.map(t=>s._`${(0,n.usePattern)(e,t)}.test(${r})`))),(0,s.not)(o)}(r),()=>g(r)):g(r)}),e.ok(s._`${l} === ${o.default.errors}`)}};t.default=i},429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const n=r(9270),s=r(111),o=r(7193),a={message:"boolean schema is false"};function i(e,t){const{gen:r,data:s}=e,o={gen:r,keyword:"false schema",data:s,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,n.reportError)(o,a,void 0,t)}t.topBoolOrEmptySchema=function(e){const{gen:t,schema:r,validateName:n}=e;!1===r?i(e,!1):"object"==typeof r&&!0===r.$async?t.return(o.default.data):(t.assign(s._`${n}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){const{gen:r,schema:n}=e;!1===n?(r.var(t,!1),i(e)):r.var(t,!0)}},450:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=n.operators,o={maximum:{okStr:"<=",ok:s.LTE,fail:s.GT},minimum:{okStr:">=",ok:s.GTE,fail:s.LT},exclusiveMaximum:{okStr:"<",ok:s.LT,fail:s.GTE},exclusiveMinimum:{okStr:">",ok:s.GT,fail:s.LTE}},a={message:({keyword:e,schemaCode:t})=>n.str`must be ${o[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>n._`{comparison: ${o[e].okStr}, limit: ${t}}`},i={keyword:Object.keys(o),type:"number",schemaType:"number",$data:!0,error:a,code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(n._`${r} ${o[t].fail} ${s} || isNaN(${r})`)}};t.default=i},560:(e,t,r)=>{"use strict";const n=r(3908);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(2754),s=r(111),o=r(5273),a=r(2780),i={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>s.str`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>s._`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:i,schema:c,parentSchema:l,schemaCode:u,it:d}=e;if(!i&&!c)return;const p=t.let("valid"),h=l.items?(0,n.getSchemaTypes)(l.items):[];function f(o,a){const i=t.name("item"),c=(0,n.checkDataTypes)(h,i,d.opts.strictNumbers,n.DataType.Wrong),l=t.const("indices",s._`{}`);t.for(s._`;${o}--;`,()=>{t.let(i,s._`${r}[${o}]`),t.if(c,s._`continue`),h.length>1&&t.if(s._`typeof ${i} == "string"`,s._`${i} += "_"`),t.if(s._`typeof ${l}[${i}] == "number"`,()=>{t.assign(a,s._`${l}[${i}]`),e.error(),t.assign(p,!1).break()}).code(s._`${l}[${i}] = ${o}`)})}function m(n,i){const c=(0,o.useFunc)(t,a.default),l=t.name("outer");t.label(l).for(s._`;${n}--;`,()=>t.for(s._`${i} = ${n}; ${i}--;`,()=>t.if(s._`${c}(${r}[${n}], ${r}[${i}])`,()=>{e.error(),t.assign(p,!1).break(l)})))}e.block$data(p,function(){const n=t.let("i",s._`${r}.length`),o=t.let("j");e.setParams({i:n,j:o}),t.assign(p,!0),t.if(s._`${n} > 1`,()=>(h.length>0&&!h.some(e=>"object"===e||"array"===e)?f:m)(n,o))},s._`${u} === false`),e.ok(p)}};t.default=i},736:(e,t,r)=>{e.exports=function(e){function t(e){let r,s,o,a=null;function i(...e){if(!i.enabled)return;const n=i,s=Number(new Date),o=s-(r||s);n.diff=o,n.prev=r,n.curr=s,r=s,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,s)=>{if("%%"===r)return"%";a++;const o=t.formatters[s];if("function"==typeof o){const t=e[a];r=o.call(n,t),e.splice(a,1),a--}return r}),t.formatArgs.call(n,e),(n.log||t.log).apply(n,e)}return i.namespace=e,i.useColors=t.useColors(),i.color=t.selectColor(e),i.extend=n,i.destroy=t.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(s!==t.namespaces&&(s=t.namespaces,o=t.enabled(e)),o),set:e=>{a=e}}),"function"==typeof t.init&&t.init(i),i}function n(e,r){const n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function s(e,t){let r=0,n=0,s=-1,o=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(s=n,o=r,n++):(r++,n++);else{if(-1===s)return!1;n=s+1,o++,r=o}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(s(e,r))return!1;for(const r of t.names)if(s(e,r))return!0;return!1},t.humanize=r(6585),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NsisUpdater=void 0;const n=r(6551),s=r(6928),o=r(9322),a=r(1972),i=r(3765),c=r(5776),l=r(4652),u=r(6578),d=r(7016);class p extends o.BaseUpdater{constructor(e,t){super(e,t),this._verifyUpdateCodeSignature=(e,t)=>(0,u.verifySignature)(e,t,this._logger)}get verifyUpdateCodeSignature(){return this._verifyUpdateCodeSignature}set verifyUpdateCodeSignature(e){e&&(this._verifyUpdateCodeSignature=e)}doDownloadUpdate(e){const t=e.updateInfoAndProvider.provider,r=(0,c.findFile)(t.resolveFiles(e.updateInfoAndProvider.info),"exe");return this.executeDownload({fileExtension:"exe",downloadUpdateOptions:e,fileInfo:r,task:async(s,o,a,i)=>{const c=r.packageInfo,u=null!=c&&null!=a;if(u&&e.disableWebInstaller)throw(0,n.newError)(`Unable to download new version ${e.updateInfoAndProvider.info.version}. Web Installers are disabled`,"ERR_UPDATER_WEB_INSTALLER_DISABLED");u||e.disableWebInstaller||this._logger.warn("disableWebInstaller is set to false, you should set it to true if you do not plan on using a web installer. This will default to true in a future version."),(u||e.disableDifferentialDownload||await this.differentialDownloadInstaller(r,e,s,t,n.CURRENT_APP_INSTALLER_FILE_NAME))&&await this.httpExecutor.download(r.url,s,o);const p=await this.verifySignature(s);if(null!=p)throw await i(),(0,n.newError)(`New version ${e.updateInfoAndProvider.info.version} is not signed by the application owner: ${p}`,"ERR_UPDATER_INVALID_SIGNATURE");if(u&&await this.differentialDownloadWebPackage(e,c,a,t))try{await this.httpExecutor.download(new d.URL(c.path),a,{headers:e.requestHeaders,cancellationToken:e.cancellationToken,sha512:c.sha512})}catch(e){try{await(0,l.unlink)(a)}catch(e){}throw e}}})}async verifySignature(e){let t;try{if(t=(await this.configOnDisk.value).publisherName,null==t)return null}catch(e){if("ENOENT"===e.code)return null;throw e}return await this._verifyUpdateCodeSignature(Array.isArray(t)?t:[t],e)}doInstall(e){const t=this.installerPath;if(null==t)return this.dispatchError(new Error("No valid update available, can't quit and install")),!1;const n=["--updated"];e.isSilent&&n.push("/S"),e.isForceRunAfter&&n.push("--force-run"),this.installDirectory&&n.push(`/D=${this.installDirectory}`);const o=null==this.downloadedUpdateHelper?null:this.downloadedUpdateHelper.packageFile;null!=o&&n.push(`--package-file=${o}`);const a=()=>{this.spawnLog(s.join(process.resourcesPath,"elevate.exe"),[t].concat(n)).catch(e=>this.dispatchError(e))};return e.isAdminRightsRequired?(this._logger.info("isAdminRightsRequired is set to true, run installer using elevate.exe"),a(),!0):(this.spawnLog(t,n).catch(e=>{const n=e.code;this._logger.info(`Cannot run installer: error code: ${n}, error message: "${e.message}", will be executed again using elevate if EACCES, and will try to use electron.shell.openItem if ENOENT`),"UNKNOWN"===n||"EACCES"===n?a():"ENOENT"===n?r(4157).shell.openPath(t).catch(e=>this.dispatchError(e)):this.dispatchError(e)}),!0)}async differentialDownloadWebPackage(e,t,r,o){if(null==t.blockMapSize)return!0;try{const c={newUrl:new d.URL(t.path),oldFile:s.join(this.downloadedUpdateHelper.cacheDir,n.CURRENT_APP_PACKAGE_FILE_NAME),logger:this._logger,newFile:r,requestHeaders:this.requestHeaders,isUseMultipleRangeRequest:o.isUseMultipleRangeRequest,cancellationToken:e.cancellationToken};this.listenerCount(i.DOWNLOAD_PROGRESS)>0&&(c.onProgress=e=>this.emit(i.DOWNLOAD_PROGRESS,e)),await new a.FileWithEmbeddedBlockMapDifferentialDownloader(t,this.httpExecutor,c).download()}catch(e){return this._logger.error(`Cannot download differentially, fallback to full download: ${e.stack||e}`),"win32"===process.platform}return!1}}t.NsisUpdater=p},857:e=>{"use strict";e.exports=require("os")},866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(7083),s={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,n.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};t.default=s},903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;const n=r(3186);class s extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}var o;!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(o||(t.UsedValueState=o={})),t.varKinds={const:new n.Name("const"),let:new n.Name("let"),var:new n.Name("var")};class a{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof n.Name?e:this.name(e)}name(e){return new n.Name(this._newName(e))}_newName(e){return`${e}${(this._names[e]||this._nameGroup(e)).index++}`}_nameGroup(e){var t,r;if((null===(r=null===(t=this._parent)||void 0===t?void 0:t._prefixes)||void 0===r?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=a;class i extends n.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=n._`.${new n.Name(t)}[${r}]`}}t.ValueScopeName=i;const c=n._`\n`;t.ValueScope=class extends a{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?c:n.nil}}get(){return this._scope}name(e){return new i(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw new Error("CodeGen: ref must be passed in value");const n=this.toName(e),{prefix:s}=n,o=null!==(r=t.key)&&void 0!==r?r:t.ref;let a=this._values[s];if(a){const e=a.get(o);if(e)return e}else a=this._values[s]=new Map;a.set(o,n);const i=this._scope[s]||(this._scope[s]=[]),c=i.length;return i[c]=t.ref,n.setValue(t,{property:s,itemIndex:c}),n}getValue(e,t){const r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,t=>{if(void 0===t.scopePath)throw new Error(`CodeGen: name "${t}" has no value`);return n._`${e}${t.scopePath}`})}scopeCode(e=this._values,t,r){return this._reduceValues(e,e=>{if(void 0===e.value)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code},t,r)}_reduceValues(e,r,a={},i){let c=n.nil;for(const l in e){const u=e[l];if(!u)continue;const d=a[l]=a[l]||new Map;u.forEach(e=>{if(d.has(e))return;d.set(e,o.Started);let a=r(e);if(a){const r=this.opts.es5?t.varKinds.var:t.varKinds.const;c=n._`${c}${r} ${e} = ${a};${this.opts._n}`}else{if(!(a=null==i?void 0:i(e)))throw new s(e);c=n._`${c}${a}${this.opts._n}`}d.set(e,o.Completed)})}return c}}},906:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.newBaseUrl=function(e){const t=new n.URL(e);return t.pathname.endsWith("/")||(t.pathname+="/"),t},t.newUrlFromBase=o,t.getChannelFilename=function(e){return`${e}.yml`},t.blockmapFiles=function(e,t,r){const n=o(`${e.pathname}.blockmap`,e);return[o(`${e.pathname.replace(new RegExp(s(r),"g"),t)}.blockmap`,e),n]};const n=r(7016),s=r(912);function o(e,t,r=!1){const s=new n.URL(e,t),o=t.search;return null!=o&&0!==o.length?s.search=o:r&&(s.search=`noCache=${Date.now().toString(32)}`),s}},909:(e,t,r)=>{"use strict";const n=r(3908);e.exports=(e,t,r)=>{const s=new n(e,r),o=new n(t,r);return s.compare(o)||s.compareBuild(o)}},910:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ElectronAppAdapter=void 0;const n=r(6928),s=r(6176);t.ElectronAppAdapter=class{constructor(e=r(4157).app){this.app=e}whenReady(){return this.app.whenReady()}get version(){return this.app.getVersion()}get name(){return this.app.getName()}get isPackaged(){return!0===this.app.isPackaged}get appUpdateConfigPath(){return this.isPackaged?n.join(process.resourcesPath,"app-update.yml"):n.join(this.app.getAppPath(),"dev-app-update.yml")}get userDataPath(){return this.app.getPath("userData")}get baseCachePath(){return(0,s.getAppCacheDir)()}quit(){this.app.quit()}relaunch(){this.app.relaunch()}onQuit(e){this.app.once("quit",(t,r)=>e(r))}}},912:e=>{var t=/[\\^$.*+?()[\]{}|]/g,r=RegExp(t.source),n="object"==typeof global&&global&&global.Object===Object&&global,s="object"==typeof self&&self&&self.Object===Object&&self,o=n||s||Function("return this")(),a=Object.prototype.toString,i=o.Symbol,c=i?i.prototype:void 0,l=c?c.toString:void 0;e.exports=function(e){var n;return(e=null==(n=e)?"":function(e){if("string"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==a.call(e)}(e))return l?l.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}(n))&&r.test(e)?e.replace(t,"\\$&"):e}},973:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>n.str`must be multiple of ${e}`,params:({schemaCode:e})=>n._`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:s,it:o}=e,a=o.opts.multipleOfPrecision,i=t.let("res"),c=a?n._`Math.abs(Math.round(${i}) - ${i}) > 1e-${a}`:n._`${i} !== parseInt(${i})`;e.fail$data(n._`(${s} === 0 || (${i} = ${r}/${s}, ${c}))`)}};t.default=s},1005:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PacmanUpdater=void 0;const n=r(9322),s=r(3765),o=r(5776);class a extends n.BaseUpdater{constructor(e,t){super(e,t)}doDownloadUpdate(e){const t=e.updateInfoAndProvider.provider,r=(0,o.findFile)(t.resolveFiles(e.updateInfoAndProvider.info),"pacman",["AppImage","deb","rpm"]);return this.executeDownload({fileExtension:"pacman",fileInfo:r,downloadUpdateOptions:e,task:async(e,t)=>{this.listenerCount(s.DOWNLOAD_PROGRESS)>0&&(t.onProgress=e=>this.emit(s.DOWNLOAD_PROGRESS,e)),await this.httpExecutor.download(r.url,e,t)}})}get installerPath(){var e,t;return null!==(t=null===(e=super.installerPath)||void 0===e?void 0:e.replace(/ /g,"\\ "))&&void 0!==t?t:null}doInstall(e){const t=this.wrapSudo(),r=/pkexec/i.test(t)?"":'"',n=this.installerPath;if(null==n)return this.dispatchError(new Error("No valid update available, can't quit and install")),!1;const s=["pacman","-U","--noconfirm",n];return this.spawnSyncLog(t,[`${r}/bin/bash`,"-c",`'${s.join(" ")}'${r}`]),e.isForceRunAfter&&this.app.relaunch(),!0}}t.PacmanUpdater=a},1039:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const n=r(111),s=r(5273),o=r(7193),a=r(5273);function i(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:n._`Object.prototype.hasOwnProperty`})}function c(e,t,r){return n._`${i(e)}.call(${t}, ${r})`}function l(e,t,r,s){const o=n._`${t}${(0,n.getProperty)(r)} === undefined`;return s?(0,n.or)(o,(0,n.not)(c(e,t,r))):o}function u(e){return e?Object.keys(e).filter(e=>"__proto__"!==e):[]}t.checkReportMissingProp=function(e,t){const{gen:r,data:s,it:o}=e;r.if(l(r,s,t,o.opts.ownProperties),()=>{e.setParams({missingProperty:n._`${t}`},!0),e.error()})},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},s,o){return(0,n.or)(...s.map(s=>(0,n.and)(l(e,t,s,r.ownProperties),n._`${o} = ${s}`)))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=i,t.isOwnProperty=c,t.propertyInData=function(e,t,r,s){const o=n._`${t}${(0,n.getProperty)(r)} !== undefined`;return s?n._`${o} && ${c(e,t,r)}`:o},t.noPropertyInData=l,t.allSchemaProperties=u,t.schemaProperties=function(e,t){return u(t).filter(r=>!(0,s.alwaysValidSchema)(e,t[r]))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:s,schemaPath:a,errorPath:i},it:c},l,u,d){const p=d?n._`${e}, ${t}, ${s}${a}`:t,h=[[o.default.instancePath,(0,n.strConcat)(o.default.instancePath,i)],[o.default.parentData,c.parentData],[o.default.parentDataProperty,c.parentDataProperty],[o.default.rootData,o.default.rootData]];c.opts.dynamicRef&&h.push([o.default.dynamicAnchors,o.default.dynamicAnchors]);const f=n._`${p}, ${r.object(...h)}`;return u!==n.nil?n._`${l}.call(${u}, ${f})`:n._`${l}(${f})`};const d=n._`new RegExp`;t.usePattern=function({gen:e,it:{opts:t}},r){const s=t.unicodeRegExp?"u":"",{regExp:o}=t.code,i=o(r,s);return e.scopeValue("pattern",{key:i.toString(),ref:i,code:n._`${"new RegExp"===o.code?d:(0,a.useFunc)(e,o)}(${r}, ${s})`})},t.validateArray=function(e){const{gen:t,data:r,keyword:o,it:a}=e,i=t.name("valid");if(a.allErrors){const e=t.let("valid",!0);return c(()=>t.assign(e,!1)),e}return t.var(i,!0),c(()=>t.break()),i;function c(a){const c=t.const("len",n._`${r}.length`);t.forRange("i",0,c,r=>{e.subschema({keyword:o,dataProp:r,dataPropType:s.Type.Num},i),t.if((0,n.not)(i),a)})}},t.validateUnion=function(e){const{gen:t,schema:r,keyword:o,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(e=>(0,s.alwaysValidSchema)(a,e))&&!a.opts.unevaluated)return;const i=t.let("valid",!1),c=t.name("_valid");t.block(()=>r.forEach((r,s)=>{const a=e.subschema({keyword:o,schemaProp:s,compositeRule:!0},c);t.assign(i,n._`${i} || ${c}`),e.mergeValidEvaluated(a,c)||t.if((0,n.not)(i))})),e.result(i,()=>e.reset(),()=>e.error(!0))}},1081:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.retry=async function e(t,r,s,o=0,a=0,i){var c;const l=new n.CancellationToken;try{return await t()}catch(n){if((null===(c=null==i?void 0:i(n))||void 0===c||c)&&r>0&&!l.cancelled)return await new Promise(e=>setTimeout(e,s+o*a)),await e(t,r-1,s,o,a+1,i);throw n}};const n=r(3411)},1085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const n=r(3789),s=r(7083),o=r(6735),a=r(7083);function i(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:n._`Object.prototype.hasOwnProperty`})}function c(e,t,r){return n._`${i(e)}.call(${t}, ${r})`}function l(e,t,r,s){const o=n._`${t}${(0,n.getProperty)(r)} === undefined`;return s?(0,n.or)(o,(0,n.not)(c(e,t,r))):o}function u(e){return e?Object.keys(e).filter(e=>"__proto__"!==e):[]}t.checkReportMissingProp=function(e,t){const{gen:r,data:s,it:o}=e;r.if(l(r,s,t,o.opts.ownProperties),()=>{e.setParams({missingProperty:n._`${t}`},!0),e.error()})},t.checkMissingProp=function({gen:e,data:t,it:{opts:r}},s,o){return(0,n.or)(...s.map(s=>(0,n.and)(l(e,t,s,r.ownProperties),n._`${o} = ${s}`)))},t.reportMissingProp=function(e,t){e.setParams({missingProperty:t},!0),e.error()},t.hasPropFunc=i,t.isOwnProperty=c,t.propertyInData=function(e,t,r,s){const o=n._`${t}${(0,n.getProperty)(r)} !== undefined`;return s?n._`${o} && ${c(e,t,r)}`:o},t.noPropertyInData=l,t.allSchemaProperties=u,t.schemaProperties=function(e,t){return u(t).filter(r=>!(0,s.alwaysValidSchema)(e,t[r]))},t.callValidateCode=function({schemaCode:e,data:t,it:{gen:r,topSchemaRef:s,schemaPath:a,errorPath:i},it:c},l,u,d){const p=d?n._`${e}, ${t}, ${s}${a}`:t,h=[[o.default.instancePath,(0,n.strConcat)(o.default.instancePath,i)],[o.default.parentData,c.parentData],[o.default.parentDataProperty,c.parentDataProperty],[o.default.rootData,o.default.rootData]];c.opts.dynamicRef&&h.push([o.default.dynamicAnchors,o.default.dynamicAnchors]);const f=n._`${p}, ${r.object(...h)}`;return u!==n.nil?n._`${l}.call(${u}, ${f})`:n._`${l}(${f})`};const d=n._`new RegExp`;t.usePattern=function({gen:e,it:{opts:t}},r){const s=t.unicodeRegExp?"u":"",{regExp:o}=t.code,i=o(r,s);return e.scopeValue("pattern",{key:i.toString(),ref:i,code:n._`${"new RegExp"===o.code?d:(0,a.useFunc)(e,o)}(${r}, ${s})`})},t.validateArray=function(e){const{gen:t,data:r,keyword:o,it:a}=e,i=t.name("valid");if(a.allErrors){const e=t.let("valid",!0);return c(()=>t.assign(e,!1)),e}return t.var(i,!0),c(()=>t.break()),i;function c(a){const c=t.const("len",n._`${r}.length`);t.forRange("i",0,c,r=>{e.subschema({keyword:o,dataProp:r,dataPropType:s.Type.Num},i),t.if((0,n.not)(i),a)})}},t.validateUnion=function(e){const{gen:t,schema:r,keyword:o,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(e=>(0,s.alwaysValidSchema)(a,e))&&!a.opts.unevaluated)return;const i=t.let("valid",!1),c=t.name("_valid");t.block(()=>r.forEach((r,s)=>{const a=e.subschema({keyword:o,schemaProp:s,compositeRule:!0},c);t.assign(i,n._`${i} || ${c}`),e.mergeValidEvaluated(a,c)||t.if((0,n.not)(i))})),e.result(i,()=>e.reset(),()=>e.error(!0))}},1119:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;const n=r(111),s=r(5273),o=r(1039);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{const s=1===t?"property":"properties";return n.str`must have ${s} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:s}})=>n._`{property: ${e},
    missingProperty: ${s},
    depsCount: ${t},
    deps: ${r}}`};const a={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=function({schema:e}){const t={},r={};for(const n in e)"__proto__"!==n&&((Array.isArray(e[n])?t:r)[n]=e[n]);return[t,r]}(e);i(e,t),c(e,r)}};function i(e,t=e.schema){const{gen:r,data:s,it:a}=e;if(0===Object.keys(t).length)return;const i=r.let("missing");for(const c in t){const l=t[c];if(0===l.length)continue;const u=(0,o.propertyInData)(r,s,c,a.opts.ownProperties);e.setParams({property:c,depsCount:l.length,deps:l.join(", ")}),a.allErrors?r.if(u,()=>{for(const t of l)(0,o.checkReportMissingProp)(e,t)}):(r.if(n._`${u} && (${(0,o.checkMissingProp)(e,l,i)})`),(0,o.reportMissingProp)(e,i),r.else())}}function c(e,t=e.schema){const{gen:r,data:n,keyword:a,it:i}=e,c=r.name("valid");for(const l in t)(0,s.alwaysValidSchema)(i,t[l])||(r.if((0,o.propertyInData)(r,n,l,i.opts.ownProperties),()=>{const t=e.subschema({keyword:a,schemaProp:l},c);e.mergeValidEvaluated(t,c)},()=>r.var(c,!0)),e.ok(c))}t.validatePropertyDeps=i,t.validateSchemaDeps=c,t.default=a},1123:e=>{"use strict";const t=/^[0-9]+$/,r=(e,r)=>{const n=t.test(e),s=t.test(r);return n&&s&&(e=+e,r=+r),e===r?0:n&&!s?-1:s&&!n?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},1124:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.attemptifySync=t.attemptifyAsync=void 0;const n=r(4763);t.attemptifyAsync=(e,t=n.NOOP)=>function(){return e.apply(void 0,arguments).catch(t)},t.attemptifySync=(e,t=n.NOOP)=>function(){try{return e.apply(void 0,arguments)}catch(e){return t(e)}}},1203:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GitHubProvider=t.BaseGitHubProvider=void 0,t.computeReleaseNotes=d;const n=r(6551),s=r(9589),o=r(7016),a=r(906),i=r(5776),c=/\/tag\/([^/]+)$/;class l extends i.Provider{constructor(e,t,r){super({...r,isUseMultipleRangeRequest:!1}),this.options=e,this.baseUrl=(0,a.newBaseUrl)((0,n.githubUrl)(e,t));const s="github.com"===t?"api.github.com":t;this.baseApiUrl=(0,a.newBaseUrl)((0,n.githubUrl)(e,s))}computeGithubBasePath(e){const t=this.options.host;return t&&!["github.com","api.github.com"].includes(t)?`/api/v3${e}`:e}}function u(e){const t=e.elementValueOrEmpty("content");return"No content."===t?"":t}function d(e,t,r,n){if(!t)return u(n);const o=[];for(const t of r.getElements("entry")){const r=/\/tag\/v?([^/]+)$/.exec(t.element("link").attribute("href"))[1];s.lt(e,r)&&o.push({version:r,note:u(t)})}return o.sort((e,t)=>s.rcompare(e.version,t.version))}t.BaseGitHubProvider=l,t.GitHubProvider=class extends l{constructor(e,t,r){super(e,"github.com",r),this.options=e,this.updater=t}get channel(){const e=this.updater.channel||this.options.channel;return null==e?this.getDefaultChannelName():this.getCustomChannelName(e)}async getLatestVersion(){var e,t,r,o,l;const u=new n.CancellationToken,p=await this.httpRequest((0,a.newUrlFromBase)(`${this.basePath}.atom`,this.baseUrl),{accept:"application/xml, application/atom+xml, text/xml, */*"},u),h=(0,n.parseXml)(p);let f,m=h.element("entry",!1,"No published versions on GitHub"),g=null;try{if(this.updater.allowPrerelease){const n=(null===(e=this.updater)||void 0===e?void 0:e.channel)||(null===(t=s.prerelease(this.updater.currentVersion))||void 0===t?void 0:t[0])||null;if(null===n)g=c.exec(m.element("link").attribute("href"))[1];else for(const e of h.getElements("entry")){const t=c.exec(e.element("link").attribute("href"));if(null===t)continue;const o=t[1],a=(null===(r=s.prerelease(o))||void 0===r?void 0:r[0])||null,i=!n||["alpha","beta"].includes(n),l=null!==a&&!["alpha","beta"].includes(String(a));if(i&&!l&&("beta"!==n||"alpha"!==a)){g=o;break}if(a&&a===n){g=o;break}}}else{g=await this.getLatestTagName(u);for(const e of h.getElements("entry"))if(c.exec(e.element("link").attribute("href"))[1]===g){m=e;break}}}catch(e){throw(0,n.newError)(`Cannot parse releases feed: ${e.stack||e.message},\nXML:\n${p}`,"ERR_UPDATER_INVALID_RELEASE_FEED")}if(null==g)throw(0,n.newError)("No published versions on GitHub","ERR_UPDATER_NO_PUBLISHED_VERSIONS");let y="",v="";const w=async e=>{y=(0,a.getChannelFilename)(e),v=(0,a.newUrlFromBase)(this.getBaseDownloadPath(String(g),y),this.baseUrl);const t=this.createRequestOptions(v);try{return await this.executor.request(t,u)}catch(e){if(e instanceof n.HttpError&&404===e.statusCode)throw(0,n.newError)(`Cannot find ${y} in the latest release artifacts (${v}): ${e.stack||e.message}`,"ERR_UPDATER_CHANNEL_FILE_NOT_FOUND");throw e}};try{let e=this.channel;this.updater.allowPrerelease&&(null===(o=s.prerelease(g))||void 0===o?void 0:o[0])&&(e=this.getCustomChannelName(String(null===(l=s.prerelease(g))||void 0===l?void 0:l[0]))),f=await w(e)}catch(e){if(!this.updater.allowPrerelease)throw e;f=await w(this.getDefaultChannelName())}const _=(0,i.parseUpdateInfo)(f,y,v);return null==_.releaseName&&(_.releaseName=m.elementValueOrEmpty("title")),null==_.releaseNotes&&(_.releaseNotes=d(this.updater.currentVersion,this.updater.fullChangelog,h,m)),{tag:g,..._}}async getLatestTagName(e){const t=this.options,r=null==t.host||"github.com"===t.host?(0,a.newUrlFromBase)(`${this.basePath}/latest`,this.baseUrl):new o.URL(`${this.computeGithubBasePath(`/repos/${t.owner}/${t.repo}/releases`)}/latest`,this.baseApiUrl);try{const t=await this.httpRequest(r,{Accept:"application/json"},e);return null==t?null:JSON.parse(t).tag_name}catch(e){throw(0,n.newError)(`Unable to find latest version on GitHub (${r}), please ensure a production release exists: ${e.stack||e.message}`,"ERR_UPDATER_LATEST_VERSION_NOT_FOUND")}}get basePath(){return`/${this.options.owner}/${this.options.repo}/releases`}resolveFiles(e){return(0,i.resolveFiles)(e,this.baseUrl,t=>this.getBaseDownloadPath(e.tag,t.replace(/ /g,"-")))}getBaseDownloadPath(e,t){return`${this.basePath}/download/${e}/${t}`}}},1231:e=>{"use strict";function t(e,t){var r="",n=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+="\n\n"+e.mark.snippet),n+" "+r):n}function r(e,r){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=r,this.message=t(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||""}r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r.prototype.toString=function(e){return this.name+": "+t(this,e)},e.exports=r},1261:(e,t,r)=>{"use strict";const n=r(3908),s=r(8311),o=r(5580);e.exports=(e,t)=>{e=new s(e,t);let r=new n("0.0.0");if(e.test(r))return r;if(r=new n("0.0.0-0"),e.test(r))return r;r=null;for(let t=0;t<e.set.length;++t){const s=e.set[t];let a=null;s.forEach(e=>{const t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":a&&!o(t,a)||(a=t);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}}),!a||r&&!o(r,a)||(r=a)}return r&&e.test(r)?r:null}},1273:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;const n=r(3789),s=r(7083),o=r(1085);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{const s=1===t?"property":"properties";return n.str`must have ${s} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:s}})=>n._`{property: ${e},
    missingProperty: ${s},
    depsCount: ${t},
    deps: ${r}}`};const a={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=function({schema:e}){const t={},r={};for(const n in e)"__proto__"!==n&&((Array.isArray(e[n])?t:r)[n]=e[n]);return[t,r]}(e);i(e,t),c(e,r)}};function i(e,t=e.schema){const{gen:r,data:s,it:a}=e;if(0===Object.keys(t).length)return;const i=r.let("missing");for(const c in t){const l=t[c];if(0===l.length)continue;const u=(0,o.propertyInData)(r,s,c,a.opts.ownProperties);e.setParams({property:c,depsCount:l.length,deps:l.join(", ")}),a.allErrors?r.if(u,()=>{for(const t of l)(0,o.checkReportMissingProp)(e,t)}):(r.if(n._`${u} && (${(0,o.checkMissingProp)(e,l,i)})`),(0,o.reportMissingProp)(e,i),r.else())}}function c(e,t=e.schema){const{gen:r,data:n,keyword:a,it:i}=e,c=r.name("valid");for(const l in t)(0,s.alwaysValidSchema)(i,t[l])||(r.if((0,o.propertyInData)(r,n,l,i.opts.ownProperties),()=>{const t=e.subschema({keyword:a,schemaProp:l},c);e.mergeValidEvaluated(t,c)},()=>r.var(c,!0)),e.ok(c))}t.validatePropertyDeps=i,t.validateSchemaDeps=c,t.default=a},1305:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OperationKind=void 0,t.computeOperations=function(e,t,n){const a=o(e.files),i=o(t.files);let c=null;const l=t.files[0],u=[],d=l.name,p=a.get(d);if(null==p)throw new Error(`no file ${d} in old blockmap`);const h=i.get(d);let f=0;const{checksumToOffset:m,checksumToOldSize:g}=function(e,t,r){const n=new Map,s=new Map;let o=t;for(let t=0;t<e.checksums.length;t++){const a=e.checksums[t],i=e.sizes[t],c=s.get(a);if(void 0===c)n.set(a,o),s.set(a,i);else if(null!=r.debug){const e=c===i?"(same size)":`(size: ${c}, this size: ${i})`;r.debug(`${a} duplicated in blockmap ${e}, it doesn't lead to broken differential downloader, just corresponding block will be skipped)`)}o+=i}return{checksumToOffset:n,checksumToOldSize:s}}(a.get(d),p.offset,n);let y=l.offset;for(let e=0;e<h.checksums.length;y+=h.sizes[e],e++){const t=h.sizes[e],o=h.checksums[e];let a=m.get(o);null!=a&&g.get(o)!==t&&(n.warn(`Checksum ("${o}") matches, but size differs (old: ${g.get(o)}, new: ${t})`),a=void 0),void 0===a?(f++,null!=c&&c.kind===r.DOWNLOAD&&c.end===y?c.end+=t:(c={kind:r.DOWNLOAD,start:y,end:y+t},s(c,u,o,e))):null!=c&&c.kind===r.COPY&&c.end===a?c.end+=t:(c={kind:r.COPY,start:a,end:a+t},s(c,u,o,e))}return f>0&&n.info(`File${"file"===l.name?"":" "+l.name} has ${f} changed blocks`),u},function(e){e[e.COPY=0]="COPY",e[e.DOWNLOAD=1]="DOWNLOAD"}(r||(t.OperationKind=r={}));const n="true"===process.env.DIFFERENTIAL_DOWNLOAD_PLAN_BUILDER_VALIDATE_RANGES;function s(e,t,s,o){if(n&&0!==t.length){const n=t[t.length-1];if(n.kind===e.kind&&e.start<n.end&&e.start>n.start){const t=[n.start,n.end,e.start,e.end].reduce((e,t)=>e<t?e:t);throw new Error(`operation (block index: ${o}, checksum: ${s}, kind: ${r[e.kind]}) overlaps previous operation (checksum: ${s}):\nabs: ${n.start} until ${n.end} and ${e.start} until ${e.end}\nrel: ${n.start-t} until ${n.end-t} and ${e.start-t} until ${e.end-t}`)}}t.push(e)}function o(e){const t=new Map;for(const r of e)t.set(r.name,r);return t}},1325:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BitbucketProvider=void 0;const n=r(6551),s=r(906),o=r(5776);class a extends o.Provider{constructor(e,t,r){super({...r,isUseMultipleRangeRequest:!1}),this.configuration=e,this.updater=t;const{owner:n,slug:o}=e;this.baseUrl=(0,s.newBaseUrl)(`https://api.bitbucket.org/2.0/repositories/${n}/${o}/downloads`)}get channel(){return this.updater.channel||this.configuration.channel||"latest"}async getLatestVersion(){const e=new n.CancellationToken,t=(0,s.getChannelFilename)(this.getCustomChannelName(this.channel)),r=(0,s.newUrlFromBase)(t,this.baseUrl,this.updater.isAddNoCacheQuery);try{const n=await this.httpRequest(r,void 0,e);return(0,o.parseUpdateInfo)(n,t,r)}catch(e){throw(0,n.newError)(`Unable to find latest version on ${this.toString()}, please ensure release exists: ${e.stack||e.message}`,"ERR_UPDATER_LATEST_VERSION_NOT_FOUND")}}resolveFiles(e){return(0,o.resolveFiles)(e,this.baseUrl)}toString(){const{owner:e,slug:t}=this.configuration;return`Bitbucket (owner: ${e}, slug: ${t}, channel: ${this.channel})`}}t.BitbucketProvider=a},1390:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(8343);n.code='require("ajv/dist/runtime/uri").default',t.default=n},1461:(e,t,r)=>{"use strict";var n=r(8433),s=r(5388),o=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),a=/^[-+]?[0-9]+e/;e.exports=new s("tag:yaml.org,2002:float",{kind:"scalar",resolve:function(e){return null!==e&&!(!o.test(e)||"_"===e[e.length-1])},construct:function(e){var t,r;return r="-"===(t=e.replace(/_/g,"").toLowerCase())[0]?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),".inf"===t?1===r?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===t?NaN:r*parseFloat(t,10)},predicate:function(e){return"[object Number]"===Object.prototype.toString.call(e)&&(e%1!=0||n.isNegativeZero(e))},represent:function(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(n.isNegativeZero(e))return"-0.0";return r=e.toString(10),a.test(r)?r.replace("e",".e"):r},defaultStyle:"lowercase"})},1597:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.retryifySync=t.retryifyAsync=void 0;const n=r(4169);t.retryifyAsync=(e,t)=>function(r){return function s(){return n.default.schedule().then(n=>e.apply(void 0,arguments).then(e=>(n(),e),e=>{if(n(),Date.now()>=r)throw e;if(t(e)){const e=Math.round(100+400*Math.random());return new Promise(t=>setTimeout(t,e)).then(()=>s.apply(void 0,arguments))}throw e}))}},t.retryifySync=(e,t)=>function(r){return function n(){try{return e.apply(void 0,arguments)}catch(e){if(Date.now()>r)throw e;if(t(e))return n.apply(void 0,arguments);throw e}}}},1665:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:r(1085).validateUnion,error:{message:"must match a schema in anyOf"}};t.default=n},1729:(e,t,r)=>{"use strict";const n=r(144);e.exports=(e,t)=>{const r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},1763:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t)=>n(e,t,!0)},1769:(e,t,r)=>{"use strict";e.exports=r(6184)},1832:(e,t,r)=>{"use strict";const n=r(144);e.exports=(e,t)=>{const r=n(e,null,!0),s=n(t,null,!0),o=r.compare(s);if(0===o)return null;const a=o>0,i=a?r:s,c=a?s:r,l=!!i.prerelease.length;if(c.prerelease.length&&!l){if(!c.patch&&!c.minor)return"major";if(0===c.compareMain(i))return c.minor&&!c.patch?"minor":"patch"}const u=l?"pre":"";return r.major!==s.major?u+"major":r.minor!==s.minor?u+"minor":r.patch!==s.patch?u+"patch":"prerelease"}},1851:(e,t,r)=>{"use strict";var n=r(5388);e.exports=new n("tag:yaml.org,2002:merge",{kind:"scalar",resolve:function(e){return"<<"===e||null===e}})},1868:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.nil=t.UUID=void 0;const n=r(6982),s=r(4261),o=(0,n.randomBytes)(16);o[0]=1|o[0];const a={},i=[];for(let e=0;e<256;e++){const t=(e+256).toString(16).substr(1);a[t]=e,i[e]=t}class c{constructor(e){this.ascii=null,this.binary=null;const t=c.check(e);if(!t)throw new Error("not a UUID");this.version=t.version,"ascii"===t.format?this.ascii=e:this.binary=e}static v5(e,t){return function(e,t,r,o,a=u.ASCII){const l=(0,n.createHash)(t);if("string"!=typeof e&&!Buffer.isBuffer(e))throw(0,s.newError)("options.name must be either a string or a Buffer","ERR_INVALID_UUID_NAME");l.update(o),l.update(e);const d=l.digest();let p;switch(a){case u.BINARY:d[6]=15&d[6]|r,d[8]=63&d[8]|128,p=d;break;case u.OBJECT:d[6]=15&d[6]|r,d[8]=63&d[8]|128,p=new c(d);break;default:p=i[d[0]]+i[d[1]]+i[d[2]]+i[d[3]]+"-"+i[d[4]]+i[d[5]]+"-"+i[15&d[6]|r]+i[d[7]]+"-"+i[63&d[8]|128]+i[d[9]]+"-"+i[d[10]]+i[d[11]]+i[d[12]]+i[d[13]]+i[d[14]]+i[d[15]]}return p}(e,"sha1",80,t)}toString(){var e;return null==this.ascii&&(this.ascii=(e=this.binary,i[e[0]]+i[e[1]]+i[e[2]]+i[e[3]]+"-"+i[e[4]]+i[e[5]]+"-"+i[e[6]]+i[e[7]]+"-"+i[e[8]]+i[e[9]]+"-"+i[e[10]]+i[e[11]]+i[e[12]]+i[e[13]]+i[e[14]]+i[e[15]])),this.ascii}inspect(){return`UUID v${this.version} ${this.toString()}`}static check(e,t=0){if("string"==typeof e)return e=e.toLowerCase(),!!/^[a-f0-9]{8}(-[a-f0-9]{4}){3}-([a-f0-9]{12})$/.test(e)&&("00000000-0000-0000-0000-000000000000"===e?{version:void 0,variant:"nil",format:"ascii"}:{version:(240&a[e[14]+e[15]])>>4,variant:l((224&a[e[19]+e[20]])>>5),format:"ascii"});if(Buffer.isBuffer(e)){if(e.length<t+16)return!1;let r=0;for(;r<16&&0===e[t+r];r++);return 16===r?{version:void 0,variant:"nil",format:"binary"}:{version:(240&e[t+6])>>4,variant:l((224&e[t+8])>>5),format:"binary"}}throw(0,s.newError)("Unknown type of uuid","ERR_UNKNOWN_UUID_TYPE")}static parse(e){const t=Buffer.allocUnsafe(16);let r=0;for(let n=0;n<16;n++)t[n]=a[e[r++]+e[r++]],3!==n&&5!==n&&7!==n&&9!==n||(r+=1);return t}}function l(e){switch(e){case 0:case 1:case 3:return"ncs";case 4:case 5:return"rfc4122";case 6:return"microsoft";default:return"future"}}var u;t.UUID=c,c.OID=c.parse("6ba7b812-9dad-11d1-80b4-00c04fd430c8"),function(e){e[e.ASCII=0]="ASCII",e[e.BINARY=1]="BINARY",e[e.OBJECT=2]="OBJECT"}(u||(u={})),t.nil=new c("00000000-0000-0000-0000-000000000000")},1969:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o=r(2780),a={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>n._`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:a,schemaCode:i,schema:c}=e;a||c&&"object"==typeof c?e.fail$data(n._`!${(0,s.useFunc)(t,o.default)}(${r}, ${i})`):e.fail(n._`${c} !== ${r}`)}};t.default=a},1972:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FileWithEmbeddedBlockMapDifferentialDownloader=void 0;const n=r(4652),s=r(9),o=r(3106);class a extends s.DifferentialDownloader{async download(){const e=this.blockAwareFileInfo,t=e.size,r=t-(e.blockMapSize+4);this.fileMetadataBuffer=await this.readRemoteBytes(r,t-1);const s=i(this.fileMetadataBuffer.slice(0,this.fileMetadataBuffer.length-4));await this.doDownload(await async function(e){const t=await(0,n.open)(e,"r");try{const e=(await(0,n.fstat)(t)).size,r=Buffer.allocUnsafe(4);await(0,n.read)(t,r,0,r.length,e-r.length);const s=Buffer.allocUnsafe(r.readUInt32BE(0));return await(0,n.read)(t,s,0,s.length,e-r.length-s.length),await(0,n.close)(t),i(s)}catch(e){throw await(0,n.close)(t),e}}(this.options.oldFile),s)}}function i(e){return JSON.parse((0,o.inflateRawSync)(e).toString())}t.FileWithEmbeddedBlockMapDifferentialDownloader=a},2017:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,s,o;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(s=n;0!==s--;)if(!e(t[s],r[s]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(o=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(s=n;0!==s--;)if(!Object.prototype.hasOwnProperty.call(r,o[s]))return!1;for(s=n;0!==s--;){var a=o[s];if(!e(t[a],r[a]))return!1}return!0}return t!=t&&r!=r}},2018:e=>{"use strict";e.exports=require("tty")},2036:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}t.default=r},2040:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxProperties"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxProperties"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`Object.keys(${r}).length ${o} ${s}`)}};t.default=s},2054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;const r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}},2104:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class n extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=n;class s extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const e=this._items[0];return""===e||'""'===e}get str(){var e;return null!==(e=this._str)&&void 0!==e?e:this._str=this._items.reduce((e,t)=>`${e}${t}`,"")}get names(){var e;return null!==(e=this._names)&&void 0!==e?e:this._names=this._items.reduce((e,t)=>(t instanceof n&&(e[t.str]=(e[t.str]||0)+1),e),{})}}function o(e,...t){const r=[e[0]];let n=0;for(;n<t.length;)c(r,t[n]),r.push(e[++n]);return new s(r)}t._Code=s,t.nil=new s(""),t._=o;const a=new s("+");function i(e,...t){const r=[u(e[0])];let n=0;for(;n<t.length;)r.push(a),c(r,t[n]),r.push(a,u(e[++n]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===a){const r=l(e[t-1],e[t+1]);if(void 0!==r){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}(r),new s(r)}function c(e,t){var r;t instanceof s?e.push(...t._items):t instanceof n?e.push(t):e.push("number"==typeof(r=t)||"boolean"==typeof r||null===r?r:u(Array.isArray(r)?r.join(","):r))}function l(e,t){if('""'===t)return e;if('""'===e)return t;if("string"==typeof e){if(t instanceof n||'"'!==e[e.length-1])return;return"string"!=typeof t?`${e.slice(0,-1)}${t}"`:'"'===t[0]?e.slice(0,-1)+t.slice(1):void 0}return"string"!=typeof t||'"'!==t[0]||e instanceof n?void 0:`"${e}${t.slice(1)}`}function u(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=i,t.addCodeArg=c,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:i`${e}${t}`},t.stringify=function(e){return new s(u(e))},t.safeStringify=u,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new s(`.${e}`):o`[${e}]`},t.getEsmExportName=function(e){if("string"==typeof e&&t.IDENTIFIER.test(e))return new s(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)},t.regexpCode=function(e){return new s(e.toString())}},2111:(e,t,r)=>{"use strict";const n=r(4641),s=r(3999),o=r(5580),a=r(4089),i=r(7059),c=r(5200);e.exports=(e,t,r,l)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,l);case"!=":return s(e,r,l);case">":return o(e,r,l);case">=":return a(e,r,l);case"<":return i(e,r,l);case"<=":return c(e,r,l);default:throw new TypeError(`Invalid operator: ${t}`)}}},2119:(e,t,r)=>{"use strict";var n=r(1231),s=r(5388);function o(e,t){var r=[];return e[t].forEach(function(e){var t=r.length;r.forEach(function(r,n){r.tag===e.tag&&r.kind===e.kind&&r.multi===e.multi&&(t=n)}),r[t]=e}),r}function a(e){return this.extend(e)}a.prototype.extend=function(e){var t=[],r=[];if(e instanceof s)r.push(e);else if(Array.isArray(e))r=r.concat(e);else{if(!e||!Array.isArray(e.implicit)&&!Array.isArray(e.explicit))throw new n("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");e.implicit&&(t=t.concat(e.implicit)),e.explicit&&(r=r.concat(e.explicit))}t.forEach(function(e){if(!(e instanceof s))throw new n("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(e.loadKind&&"scalar"!==e.loadKind)throw new n("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(e.multi)throw new n("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),r.forEach(function(e){if(!(e instanceof s))throw new n("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var i=Object.create(a.prototype);return i.implicit=(this.implicit||[]).concat(t),i.explicit=(this.explicit||[]).concat(r),i.compiledImplicit=o(i,"implicit"),i.compiledExplicit=o(i,"explicit"),i.compiledTypeMap=function(){var e,t,r={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}};function n(e){e.multi?(r.multi[e.kind].push(e),r.multi.fallback.push(e)):r[e.kind][e.tag]=r.fallback[e.tag]=e}for(e=0,t=arguments.length;e<t;e+=1)arguments[e].forEach(n);return r}(i.compiledImplicit,i.compiledExplicit),i},e.exports=a},2128:(e,t)=>{"use strict";function r(e,t){return null!=t&&t.length>0&&(t.startsWith("/")||(e+="/"),e+=t),e}Object.defineProperty(t,"__esModule",{value:!0}),t.githubUrl=function(e,t="github.com"){return`${e.protocol||"https"}://${e.host||t}`},t.getS3LikeProviderBaseUrl=function(e){const t=e.provider;if("s3"===t)return function(e){let t;if(1==e.accelerate)t=`https://${e.bucket}.s3-accelerate.amazonaws.com`;else if(null!=e.endpoint)t=`${e.endpoint}/${e.bucket}`;else if(e.bucket.includes(".")){if(null==e.region)throw new Error(`Bucket name "${e.bucket}" includes a dot, but S3 region is missing`);t="us-east-1"===e.region?`https://s3.amazonaws.com/${e.bucket}`:`https://s3-${e.region}.amazonaws.com/${e.bucket}`}else t="cn-north-1"===e.region?`https://${e.bucket}.s3.${e.region}.amazonaws.com.cn`:`https://${e.bucket}.s3.amazonaws.com`;return r(t,e.path)}(e);if("spaces"===t)return function(e){if(null==e.name)throw new Error("name is missing");if(null==e.region)throw new Error("region is missing");return r(`https://${e.name}.${e.region}.digitaloceanspaces.com`,e.path)}(e);throw new Error(`Not supported provider: ${t}`)}},2203:e=>{"use strict";e.exports=require("stream")},2261:(e,t)=>{"use strict";function r(e){const t=e.length;let r,n=0,s=0;for(;s<t;)n++,r=e.charCodeAt(s++),r>=55296&&r<=56319&&s<t&&(r=e.charCodeAt(s),56320==(64512&r)&&s++);return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,r.code='require("ajv/dist/runtime/ucs2length").default'},2348:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(450),s=r(6711),o=r(3875),a=r(302),i=r(8094),c=r(4267),l=r(9347),u=r(2785),d=r(3767),p=r(4715),h=[n.default,s.default,o.default,a.default,i.default,c.default,l.default,u.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},d.default,p.default];t.default=h},2369:(e,t,r)=>{"use strict";var n=r(5388);e.exports=new n("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return null!==e?e:{}}})},2378:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,function(e){e.Tag="tag",e.Mapping="mapping"}(r||(t.DiscrError=r={}))},2394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=t.Ajv=void 0;const n=r(7666),s=r(4824),o=r(7013),a=r(2455),i=["/properties"],c="http://json-schema.org/draft-07/schema";class l extends n.default{_addVocabularies(){super._addVocabularies(),s.default.forEach(e=>this.addVocabulary(e)),this.opts.discriminator&&this.addKeyword(o.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(a,i):a;this.addMetaSchema(e,c,!1),this.refs["http://json-schema.org/schema"]=c}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(c)?c:void 0)}}t.Ajv=l,e.exports=t=l,e.exports.Ajv=l,Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var u=r(9810);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return u.KeywordCxt}});var d=r(3789);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return d._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return d.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return d.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return d.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return d.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return d.CodeGen}});var p=r(9598);Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return p.default}});var h=r(303);Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return h.default}})},2407:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1039),s=r(111),o=r(5273),a=r(5273),i={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:i,parentSchema:c,it:l}=e,{opts:u}=l,d=(0,n.allSchemaProperties)(r),p=d.filter(e=>(0,o.alwaysValidSchema)(l,r[e]));if(0===d.length||p.length===d.length&&(!l.opts.unevaluated||!0===l.props))return;const h=u.strictSchema&&!u.allowMatchingProperties&&c.properties,f=t.name("valid");!0===l.props||l.props instanceof s.Name||(l.props=(0,a.evaluatedPropsToName)(t,l.props));const{props:m}=l;function g(e){for(const t in h)new RegExp(e).test(t)&&(0,o.checkStrictMode)(l,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}function y(r){t.forIn("key",i,o=>{t.if(s._`${(0,n.usePattern)(e,r)}.test(${o})`,()=>{const n=p.includes(r);n||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:o,dataPropType:a.Type.Str},f),l.opts.unevaluated&&!0!==m?t.assign(s._`${m}[${o}]`,!0):n||l.allErrors||t.if((0,s.not)(f),()=>t.break())})})}!function(){for(const e of d)h&&g(e),l.allErrors?y(e):(t.var(f,!0),y(e),t.if(f))}()}};t.default=i},2444:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericDifferentialDownloader=void 0;const n=r(9);class s extends n.DifferentialDownloader{download(e,t){return this.doDownload(e,t)}}t.GenericDifferentialDownloader=s},2455:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')},2474:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DownloadedUpdateHelper=void 0,t.createTempUpdateFile=async function(e,t,r){let n=0,s=i.join(t,e);for(let o=0;o<3;o++)try{return await(0,a.unlink)(s),s}catch(o){if("ENOENT"===o.code)return s;r.warn(`Error on remove temp update file: ${o}`),s=i.join(t,`${n++}-${e}`)}return s};const n=r(6982),s=r(9896),o=r(8142),a=r(4652),i=r(6928);t.DownloadedUpdateHelper=class{constructor(e){this.cacheDir=e,this._file=null,this._packageFile=null,this.versionInfo=null,this.fileInfo=null,this._downloadedFileInfo=null}get downloadedFileInfo(){return this._downloadedFileInfo}get file(){return this._file}get packageFile(){return this._packageFile}get cacheDirForPendingUpdate(){return i.join(this.cacheDir,"pending")}async validateDownloadedPath(e,t,r,n){if(null!=this.versionInfo&&this.file===e&&null!=this.fileInfo)return o(this.versionInfo,t)&&o(this.fileInfo.info,r.info)&&await(0,a.pathExists)(e)?e:null;const s=await this.getValidCachedUpdateFile(r,n);return null===s?null:(n.info(`Update has already been downloaded to ${e}).`),this._file=s,s)}async setDownloadedFile(e,t,r,n,s,o){this._file=e,this._packageFile=t,this.versionInfo=r,this.fileInfo=n,this._downloadedFileInfo={fileName:s,sha512:n.info.sha512,isAdminRightsRequired:!0===n.info.isAdminRightsRequired},o&&await(0,a.outputJson)(this.getUpdateInfoFile(),this._downloadedFileInfo)}async clear(){this._file=null,this._packageFile=null,this.versionInfo=null,this.fileInfo=null,await this.cleanCacheDirForPendingUpdate()}async cleanCacheDirForPendingUpdate(){try{await(0,a.emptyDir)(this.cacheDirForPendingUpdate)}catch(e){}}async getValidCachedUpdateFile(e,t){const r=this.getUpdateInfoFile();if(!await(0,a.pathExists)(r))return null;let o;try{o=await(0,a.readJson)(r)}catch(e){let r="No cached update info available";return"ENOENT"!==e.code&&(await this.cleanCacheDirForPendingUpdate(),r+=` (error on read: ${e.message})`),t.info(r),null}if(null===(null==o?void 0:o.fileName))return t.warn("Cached update info is corrupted: no fileName, directory for cached update will be cleaned"),await this.cleanCacheDirForPendingUpdate(),null;if(e.info.sha512!==o.sha512)return t.info(`Cached update sha512 checksum doesn't match the latest available update. New update must be downloaded. Cached: ${o.sha512}, expected: ${e.info.sha512}. Directory for cached update will be cleaned`),await this.cleanCacheDirForPendingUpdate(),null;const c=i.join(this.cacheDirForPendingUpdate,o.fileName);if(!await(0,a.pathExists)(c))return t.info("Cached update file doesn't exist"),null;const l=await function(e,t="sha512",r="base64",o){return new Promise((a,i)=>{const c=(0,n.createHash)(t);c.on("error",i).setEncoding(r),(0,s.createReadStream)(e,{...o,highWaterMark:1048576}).on("error",i).on("end",()=>{c.end(),a(c.read())}).pipe(c,{end:!1})})}(c);return e.info.sha512!==l?(t.warn(`Sha512 checksum doesn't match the latest available update. New update must be downloaded. Cached: ${l}, expected: ${e.info.sha512}`),await this.cleanCacheDirForPendingUpdate(),null):(this._downloadedFileInfo=o,c)}getUpdateInfoFile(){return i.join(this.cacheDirForPendingUpdate,"update-info.json")}}},2482:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(2017);n.code='require("ajv/dist/runtime/equal").default',t.default=n},2525:(e,t,r)=>{"use strict";const n=r(7638),s=r(560);e.exports=(e,t,r)=>{const o=[];let a=null,i=null;const c=e.sort((e,t)=>s(e,t,r));for(const e of c)n(e,t,r)?(i=e,a||(a=e)):(i&&o.push([a,i]),i=null,a=null);a&&o.push([a,null]);const l=[];for(const[e,t]of o)e===t?l.push(e):t||e!==c[0]?t?e===c[0]?l.push(`<=${t}`):l.push(`${e} - ${t}`):l.push(`>=${e}`):l.push("*");const u=l.join(" || "),d="string"==typeof t.raw?t.raw:String(t);return u.length<d.length?u:t}},2610:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(7065),s=r(7394),o=r(7366),a=r(6166),i=r(6925),c=r(1273),l=r(7705),u=r(396),d=r(5581),p=r(8013),h=r(6939),f=r(1665),m=r(9259),g=r(6636),y=r(7567),v=r(866);t.default=function(e=!1){const t=[h.default,f.default,m.default,g.default,y.default,v.default,l.default,u.default,c.default,d.default,p.default];return e?t.push(s.default,a.default):t.push(n.default,o.default),t.push(i.default),t}},2613:e=>{"use strict";e.exports=require("assert")},2629:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxItems"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxItems"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`${r}.length ${o} ${s}`)}};t.default=s},2652:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DataSplitter=void 0,t.copyData=l;const n=r(6551),s=r(9896),o=r(2203),a=r(1305),i=Buffer.from("\r\n\r\n");var c;function l(e,t,r,n,o){const a=(0,s.createReadStream)("",{fd:r,autoClose:!1,start:e.start,end:e.end-1});a.on("error",n),a.once("end",o),a.pipe(t,{end:!1})}!function(e){e[e.INIT=0]="INIT",e[e.HEADER=1]="HEADER",e[e.BODY=2]="BODY"}(c||(c={}));class u extends o.Writable{constructor(e,t,r,n,s,o){super(),this.out=e,this.options=t,this.partIndexToTaskIndex=r,this.partIndexToLength=s,this.finishHandler=o,this.partIndex=-1,this.headerListBuffer=null,this.readState=c.INIT,this.ignoreByteCount=0,this.remainingPartDataCount=0,this.actualPartLength=0,this.boundaryLength=n.length+4,this.ignoreByteCount=this.boundaryLength-2}get isFinished(){return this.partIndex===this.partIndexToLength.length}_write(e,t,r){this.isFinished?console.error(`Trailing ignored data: ${e.length} bytes`):this.handleData(e).then(r).catch(r)}async handleData(e){let t=0;if(0!==this.ignoreByteCount&&0!==this.remainingPartDataCount)throw(0,n.newError)("Internal error","ERR_DATA_SPLITTER_BYTE_COUNT_MISMATCH");if(this.ignoreByteCount>0){const r=Math.min(this.ignoreByteCount,e.length);this.ignoreByteCount-=r,t=r}else if(this.remainingPartDataCount>0){const r=Math.min(this.remainingPartDataCount,e.length);this.remainingPartDataCount-=r,await this.processPartData(e,0,r),t=r}if(t!==e.length){if(this.readState===c.HEADER){const r=this.searchHeaderListEnd(e,t);if(-1===r)return;t=r,this.readState=c.BODY,this.headerListBuffer=null}for(;;){if(this.readState===c.BODY)this.readState=c.INIT;else{this.partIndex++;let r=this.partIndexToTaskIndex.get(this.partIndex);if(null==r){if(!this.isFinished)throw(0,n.newError)("taskIndex is null","ERR_DATA_SPLITTER_TASK_INDEX_IS_NULL");r=this.options.end}const s=0===this.partIndex?this.options.start:this.partIndexToTaskIndex.get(this.partIndex-1)+1;if(s<r)await this.copyExistingData(s,r);else if(s>r)throw(0,n.newError)("prevTaskIndex must be < taskIndex","ERR_DATA_SPLITTER_TASK_INDEX_ASSERT_FAILED");if(this.isFinished)return this.onPartEnd(),void this.finishHandler();if(t=this.searchHeaderListEnd(e,t),-1===t)return void(this.readState=c.HEADER)}const r=this.partIndexToLength[this.partIndex],s=t+r,o=Math.min(s,e.length);if(await this.processPartStarted(e,t,o),this.remainingPartDataCount=r-(o-t),this.remainingPartDataCount>0)return;if(t=s+this.boundaryLength,t>=e.length)return void(this.ignoreByteCount=this.boundaryLength-(e.length-s))}}}copyExistingData(e,t){return new Promise((r,n)=>{const s=()=>{if(e===t)return void r();const o=this.options.tasks[e];o.kind===a.OperationKind.COPY?l(o,this.out,this.options.oldFileFd,n,()=>{e++,s()}):n(new Error("Task kind must be COPY"))};s()})}searchHeaderListEnd(e,t){const r=e.indexOf(i,t);if(-1!==r)return r+i.length;const n=0===t?e:e.slice(t);return null==this.headerListBuffer?this.headerListBuffer=n:this.headerListBuffer=Buffer.concat([this.headerListBuffer,n]),-1}onPartEnd(){const e=this.partIndexToLength[this.partIndex-1];if(this.actualPartLength!==e)throw(0,n.newError)(`Expected length: ${e} differs from actual: ${this.actualPartLength}`,"ERR_DATA_SPLITTER_LENGTH_MISMATCH");this.actualPartLength=0}processPartStarted(e,t,r){return 0!==this.partIndex&&this.onPartEnd(),this.processPartData(e,t,r)}processPartData(e,t,r){this.actualPartLength+=r-t;const n=this.out;return n.write(0===t&&e.length===r?e:e.slice(t,r))?Promise.resolve():new Promise((e,t)=>{n.on("error",t),n.once("drain",()=>{n.removeListener("error",t),e()})})}}t.DataSplitter=u},2657:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;const n=r(3789),s=r(6735),o=r(1085),a=r(2796);function i(e){const{gen:t,data:r,it:s}=e;t.if(s.parentData,()=>t.assign(r,n._`${s.parentData}[${s.parentDataProperty}]`))}function c(e,t,r){if(void 0===r)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,n.stringify)(r)})}t.macroKeywordCode=function(e,t){const{gen:r,keyword:s,schema:o,parentSchema:a,it:i}=e,l=t.macro.call(i.self,o,a,i),u=c(r,s,l);!1!==i.opts.validateSchema&&i.self.validateSchema(l,!0);const d=r.name("valid");e.subschema({schema:l,schemaPath:n.nil,errSchemaPath:`${i.errSchemaPath}/${s}`,topSchemaRef:u,compositeRule:!0},d),e.pass(d,()=>e.error(!0))},t.funcKeywordCode=function(e,t){var r;const{gen:l,keyword:u,schema:d,parentSchema:p,$data:h,it:f}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}(f,t);const m=!h&&t.compile?t.compile.call(f.self,d,p,f):t.validate,g=c(l,u,m),y=l.let("valid");function v(r=(t.async?n._`await `:n.nil)){const a=f.opts.passContext?s.default.this:s.default.self,i=!("compile"in t&&!h||!1===t.schema);l.assign(y,n._`${r}${(0,o.callValidateCode)(e,g,a,i)}`,t.modifying)}function w(e){var r;l.if((0,n.not)(null!==(r=t.valid)&&void 0!==r?r:y),e)}e.block$data(y,function(){if(!1===t.errors)v(),t.modifying&&i(e),w(()=>e.error());else{const r=t.async?function(){const e=l.let("ruleErrs",null);return l.try(()=>v(n._`await `),t=>l.assign(y,!1).if(n._`${t} instanceof ${f.ValidationError}`,()=>l.assign(e,n._`${t}.errors`),()=>l.throw(t))),e}():function(){const e=n._`${g}.errors`;return l.assign(e,null),v(n.nil),e}();t.modifying&&i(e),w(()=>function(e,t){const{gen:r}=e;r.if(n._`Array.isArray(${t})`,()=>{r.assign(s.default.vErrors,n._`${s.default.vErrors} === null ? ${t} : ${s.default.vErrors}.concat(${t})`).assign(s.default.errors,n._`${s.default.vErrors}.length`),(0,a.extendErrors)(e)},()=>e.error())}(e,r))}}),e.ok(null!==(r=t.valid)&&void 0!==r?r:y)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some(t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e)},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:n},s,o){if(Array.isArray(s.keyword)?!s.keyword.includes(o):s.keyword!==o)throw new Error("ajv implementation error");const a=s.dependencies;if(null==a?void 0:a.some(t=>!Object.prototype.hasOwnProperty.call(e,t)))throw new Error(`parent schema must have dependencies of ${o}: ${a.join(",")}`);if(s.validateSchema&&!s.validateSchema(e[o])){const e=`keyword "${o}" value is invalid at path "${n}": `+r.errorsText(s.validateSchema.errors);if("log"!==t.validateSchema)throw new Error(e);r.logger.error(e)}}},2658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MacUpdater=void 0;const n=r(6551),s=r(4652),o=r(9896),a=r(6928),i=r(8611),c=r(4718),l=r(5776),u=r(5317),d=r(6982);class p extends c.AppUpdater{constructor(e,t){super(e,t),this.nativeUpdater=r(4157).autoUpdater,this.squirrelDownloadedUpdate=!1,this.nativeUpdater.on("error",e=>{this._logger.warn(e),this.emit("error",e)}),this.nativeUpdater.on("update-downloaded",()=>{this.squirrelDownloadedUpdate=!0,this.debug("nativeUpdater.update-downloaded")})}debug(e){null!=this._logger.debug&&this._logger.debug(e)}closeServerIfExists(){this.server&&(this.debug("Closing proxy server"),this.server.close(e=>{e&&this.debug("proxy server wasn't already open, probably attempted closing again as a safety check before quit")}))}async doDownloadUpdate(e){let t=e.updateInfoAndProvider.provider.resolveFiles(e.updateInfoAndProvider.info);const r=this._logger,o="sysctl.proc_translated";let i=!1;try{this.debug("Checking for macOS Rosetta environment"),i=(0,u.execFileSync)("sysctl",[o],{encoding:"utf8"}).includes(`${o}: 1`),r.info(`Checked for macOS Rosetta environment (isRosetta=${i})`)}catch(e){r.warn(`sysctl shell command to check for macOS Rosetta environment failed: ${e}`)}let c=!1;try{this.debug("Checking for arm64 in uname");const e=(0,u.execFileSync)("uname",["-a"],{encoding:"utf8"}).includes("ARM");r.info(`Checked 'uname -a': arm64=${e}`),c=c||e}catch(e){r.warn(`uname shell command to check for arm64 failed: ${e}`)}c=c||"arm64"===process.arch||i;const d=e=>{var t;return e.url.pathname.includes("arm64")||(null===(t=e.info.url)||void 0===t?void 0:t.includes("arm64"))};t=c&&t.some(d)?t.filter(e=>c===d(e)):t.filter(e=>!d(e));const p=(0,l.findFile)(t,"zip",["pkg","dmg"]);if(null==p)throw(0,n.newError)(`ZIP file not provided: ${(0,n.safeStringifyJson)(t)}`,"ERR_UPDATER_ZIP_FILE_NOT_FOUND");const h=e.updateInfoAndProvider.provider,f="update.zip";return this.executeDownload({fileExtension:"zip",fileInfo:p,downloadUpdateOptions:e,task:async(t,n)=>{const o=a.join(this.downloadedUpdateHelper.cacheDir,f);let i=!0;((0,s.pathExistsSync)(o)?!e.disableDifferentialDownload:(r.info("Unable to locate previous update.zip for differential download (is this first install?), falling back to full download"),0))&&(i=await this.differentialDownloadInstaller(p,e,t,h,f)),i&&await this.httpExecutor.download(p.url,t,n)},done:async t=>{if(!e.disableDifferentialDownload)try{const e=a.join(this.downloadedUpdateHelper.cacheDir,f);await(0,s.copyFile)(t.downloadedFile,e)}catch(e){this._logger.warn(`Unable to copy file for caching for future differential downloads: ${e.message}`)}return this.updateDownloaded(p,t)}})}async updateDownloaded(e,t){var r;const n=t.downloadedFile,a=null!==(r=e.info.size)&&void 0!==r?r:(await(0,s.stat)(n)).size,c=this._logger,l=`fileToProxy=${e.url.href}`;this.closeServerIfExists(),this.debug(`Creating proxy server for native Squirrel.Mac (${l})`),this.server=(0,i.createServer)(),this.debug(`Proxy server for native Squirrel.Mac is created (${l})`),this.server.on("close",()=>{c.info(`Proxy server for native Squirrel.Mac is closed (${l})`)});const u=e=>{const t=e.address();return"string"==typeof t?t:`http://127.0.0.1:${null==t?void 0:t.port}`};return await new Promise((e,r)=>{const s=(0,d.randomBytes)(64).toString("base64").replace(/\//g,"_").replace(/\+/g,"-"),i=Buffer.from(`autoupdater:${s}`,"ascii"),p=`/${(0,d.randomBytes)(64).toString("hex")}.zip`;this.server.on("request",(t,i)=>{const l=t.url;if(c.info(`${l} requested`),"/"===l){if(!t.headers.authorization||-1===t.headers.authorization.indexOf("Basic "))return i.statusCode=401,i.statusMessage="Invalid Authentication Credentials",i.end(),void c.warn("No authenthication info");const e=t.headers.authorization.split(" ")[1],r=Buffer.from(e,"base64").toString("ascii"),[n,o]=r.split(":");if("autoupdater"!==n||o!==s)return i.statusCode=401,i.statusMessage="Invalid Authentication Credentials",i.end(),void c.warn("Invalid authenthication credentials");const a=Buffer.from(`{ "url": "${u(this.server)}${p}" }`);return i.writeHead(200,{"Content-Type":"application/json","Content-Length":a.length}),void i.end(a)}if(!l.startsWith(p))return c.warn(`${l} requested, but not supported`),i.writeHead(404),void i.end();c.info(`${p} requested by Squirrel.Mac, pipe ${n}`);let d=!1;i.on("finish",()=>{d||(this.nativeUpdater.removeListener("error",r),e([]))});const h=(0,o.createReadStream)(n);h.on("error",e=>{try{i.end()}catch(e){c.warn(`cannot end response: ${e}`)}d=!0,this.nativeUpdater.removeListener("error",r),r(new Error(`Cannot pipe "${n}": ${e}`))}),i.writeHead(200,{"Content-Type":"application/zip","Content-Length":a}),h.pipe(i)}),this.debug(`Proxy server for native Squirrel.Mac is starting to listen (${l})`),this.server.listen(0,"127.0.0.1",()=>{this.debug(`Proxy server for native Squirrel.Mac is listening (address=${u(this.server)}, ${l})`),this.nativeUpdater.setFeedURL({url:u(this.server),headers:{"Cache-Control":"no-cache",Authorization:`Basic ${i.toString("base64")}`}}),this.dispatchUpdateDownloaded(t),this.autoInstallOnAppQuit?(this.nativeUpdater.once("error",r),this.nativeUpdater.checkForUpdates()):e([])})})}handleUpdateDownloaded(){this.autoRunAppAfterInstall?this.nativeUpdater.quitAndInstall():this.app.quit(),this.closeServerIfExists()}quitAndInstall(){this.squirrelDownloadedUpdate?this.handleUpdateDownloaded():(this.nativeUpdater.on("update-downloaded",()=>this.handleUpdateDownloaded()),this.autoInstallOnAppQuit||this.nativeUpdater.checkForUpdates())}}t.MacUpdater=p},2664:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0;const n=r(111),s=r(5273);function o(e,t,r){const{gen:o,compositeRule:a,data:i,opts:c}=e;if(void 0===r)return;const l=n._`${i}${(0,n.getProperty)(t)}`;if(a)return void(0,s.checkStrictMode)(e,`default is ignored for: ${l}`);let u=n._`${l} === undefined`;"empty"===c.useDefaults&&(u=n._`${u} || ${l} === null || ${l} === ""`),o.if(u,n._`${l} = ${(0,n.stringify)(r)}`)}t.assignDefaults=function(e,t){const{properties:r,items:n}=e.schema;if("object"===t&&r)for(const t in r)o(e,t,r[t].default);else"array"===t&&Array.isArray(n)&&n.forEach((t,r)=>o(e,r,t.default))}},2705:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericProvider=void 0;const n=r(6551),s=r(906),o=r(5776);class a extends o.Provider{constructor(e,t,r){super(r),this.configuration=e,this.updater=t,this.baseUrl=(0,s.newBaseUrl)(this.configuration.url)}get channel(){const e=this.updater.channel||this.configuration.channel;return null==e?this.getDefaultChannelName():this.getCustomChannelName(e)}async getLatestVersion(){const e=(0,s.getChannelFilename)(this.channel),t=(0,s.newUrlFromBase)(e,this.baseUrl,this.updater.isAddNoCacheQuery);for(let r=0;;r++)try{return(0,o.parseUpdateInfo)(await this.httpRequest(t),e,t)}catch(t){if(t instanceof n.HttpError&&404===t.statusCode)throw(0,n.newError)(`Cannot find channel "${e}" update info: ${t.stack||t.message}`,"ERR_UPDATER_CHANNEL_FILE_NOT_FOUND");if("ECONNREFUSED"===t.code&&r<3){await new Promise((e,t)=>{try{setTimeout(e,1e3*r)}catch(e){t(e)}});continue}throw t}}resolveFiles(e){return(0,o.resolveFiles)(e,this.baseUrl)}}t.GenericProvider=a},2750:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9896),s=r(9023),o=r(1124),a=r(6284),i=r(1597),c={chmodAttempt:o.attemptifyAsync(s.promisify(n.chmod),a.default.onChangeError),chownAttempt:o.attemptifyAsync(s.promisify(n.chown),a.default.onChangeError),closeAttempt:o.attemptifyAsync(s.promisify(n.close)),fsyncAttempt:o.attemptifyAsync(s.promisify(n.fsync)),mkdirAttempt:o.attemptifyAsync(s.promisify(n.mkdir)),realpathAttempt:o.attemptifyAsync(s.promisify(n.realpath)),statAttempt:o.attemptifyAsync(s.promisify(n.stat)),unlinkAttempt:o.attemptifyAsync(s.promisify(n.unlink)),closeRetry:i.retryifyAsync(s.promisify(n.close),a.default.isRetriableError),fsyncRetry:i.retryifyAsync(s.promisify(n.fsync),a.default.isRetriableError),openRetry:i.retryifyAsync(s.promisify(n.open),a.default.isRetriableError),readFileRetry:i.retryifyAsync(s.promisify(n.readFile),a.default.isRetriableError),renameRetry:i.retryifyAsync(s.promisify(n.rename),a.default.isRetriableError),statRetry:i.retryifyAsync(s.promisify(n.stat),a.default.isRetriableError),writeRetry:i.retryifyAsync(s.promisify(n.write),a.default.isRetriableError),chmodSyncAttempt:o.attemptifySync(n.chmodSync,a.default.onChangeError),chownSyncAttempt:o.attemptifySync(n.chownSync,a.default.onChangeError),closeSyncAttempt:o.attemptifySync(n.closeSync),mkdirSyncAttempt:o.attemptifySync(n.mkdirSync),realpathSyncAttempt:o.attemptifySync(n.realpathSync),statSyncAttempt:o.attemptifySync(n.statSync),unlinkSyncAttempt:o.attemptifySync(n.unlinkSync),closeSyncRetry:i.retryifySync(n.closeSync,a.default.isRetriableError),fsyncSyncRetry:i.retryifySync(n.fsyncSync,a.default.isRetriableError),openSyncRetry:i.retryifySync(n.openSync,a.default.isRetriableError),readFileSyncRetry:i.retryifySync(n.readFileSync,a.default.isRetriableError),renameSyncRetry:i.retryifySync(n.renameSync,a.default.isRetriableError),statSyncRetry:i.retryifySync(n.statSync,a.default.isRetriableError),writeSyncRetry:i.retryifySync(n.writeSync,a.default.isRetriableError)};t.default=c},2754:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;const n=r(2054),s=r(2861),o=r(9270),a=r(111),i=r(5273);var c;function l(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(n.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}!function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(c||(t.DataType=c={})),t.getSchemaTypes=function(e){const t=l(e.type);if(t.includes("null")){if(!1===e.nullable)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw new Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=l,t.coerceAndCheckDataType=function(e,t){const{gen:r,data:n,opts:o}=e,i=function(e,t){return t?e.filter(e=>u.has(e)||"array"===t&&"array"===e):[]}(t,o.coerceTypes),l=t.length>0&&!(0===i.length&&1===t.length&&(0,s.schemaHasRulesForType)(e,t[0]));if(l){const s=p(t,n,o.strictNumbers,c.Wrong);r.if(s,()=>{i.length?function(e,t,r){const{gen:n,data:s,opts:o}=e,i=n.let("dataType",a._`typeof ${s}`),c=n.let("coerced",a._`undefined`);"array"===o.coerceTypes&&n.if(a._`${i} == 'object' && Array.isArray(${s}) && ${s}.length == 1`,()=>n.assign(s,a._`${s}[0]`).assign(i,a._`typeof ${s}`).if(p(t,s,o.strictNumbers),()=>n.assign(c,s))),n.if(a._`${c} !== undefined`);for(const e of r)(u.has(e)||"array"===e&&"array"===o.coerceTypes)&&l(e);function l(e){switch(e){case"string":return void n.elseIf(a._`${i} == "number" || ${i} == "boolean"`).assign(c,a._`"" + ${s}`).elseIf(a._`${s} === null`).assign(c,a._`""`);case"number":return void n.elseIf(a._`${i} == "boolean" || ${s} === null
              || (${i} == "string" && ${s} && ${s} == +${s})`).assign(c,a._`+${s}`);case"integer":return void n.elseIf(a._`${i} === "boolean" || ${s} === null
              || (${i} === "string" && ${s} && ${s} == +${s} && !(${s} % 1))`).assign(c,a._`+${s}`);case"boolean":return void n.elseIf(a._`${s} === "false" || ${s} === 0 || ${s} === null`).assign(c,!1).elseIf(a._`${s} === "true" || ${s} === 1`).assign(c,!0);case"null":return n.elseIf(a._`${s} === "" || ${s} === 0 || ${s} === false`),void n.assign(c,null);case"array":n.elseIf(a._`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${s} === null`).assign(c,a._`[${s}]`)}}n.else(),f(e),n.endIf(),n.if(a._`${c} !== undefined`,()=>{n.assign(s,c),function({gen:e,parentData:t,parentDataProperty:r},n){e.if(a._`${t} !== undefined`,()=>e.assign(a._`${t}[${r}]`,n))}(e,c)})}(e,t,i):f(e)})}return l};const u=new Set(["string","number","integer","boolean","null"]);function d(e,t,r,n=c.Correct){const s=n===c.Correct?a.operators.EQ:a.operators.NEQ;let o;switch(e){case"null":return a._`${t} ${s} null`;case"array":o=a._`Array.isArray(${t})`;break;case"object":o=a._`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":o=i(a._`!(${t} % 1) && !isNaN(${t})`);break;case"number":o=i();break;default:return a._`typeof ${t} ${s} ${e}`}return n===c.Correct?o:(0,a.not)(o);function i(e=a.nil){return(0,a.and)(a._`typeof ${t} == "number"`,e,r?a._`isFinite(${t})`:a.nil)}}function p(e,t,r,n){if(1===e.length)return d(e[0],t,r,n);let s;const o=(0,i.toHash)(e);if(o.array&&o.object){const e=a._`typeof ${t} != "object"`;s=o.null?e:a._`!${t} || ${e}`,delete o.null,delete o.array,delete o.object}else s=a.nil;o.number&&delete o.integer;for(const e in o)s=(0,a.and)(s,d(e,t,r,n));return s}t.checkDataType=d,t.checkDataTypes=p;const h={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?a._`{type: ${e}}`:a._`{type: ${t}}`};function f(e){const t=function(e){const{gen:t,data:r,schema:n}=e,s=(0,i.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:s,schemaValue:s,parentSchema:n,params:{},it:e}}(e);(0,o.reportError)(t,h)}t.reportTypeError=f},2780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(2017);n.code='require("ajv/dist/runtime/equal").default',t.default=n},2785:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(8840),s=r(3789),o=r(7083),a=r(2482),i={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>s.str`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>s._`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:i,schema:c,parentSchema:l,schemaCode:u,it:d}=e;if(!i&&!c)return;const p=t.let("valid"),h=l.items?(0,n.getSchemaTypes)(l.items):[];function f(o,a){const i=t.name("item"),c=(0,n.checkDataTypes)(h,i,d.opts.strictNumbers,n.DataType.Wrong),l=t.const("indices",s._`{}`);t.for(s._`;${o}--;`,()=>{t.let(i,s._`${r}[${o}]`),t.if(c,s._`continue`),h.length>1&&t.if(s._`typeof ${i} == "string"`,s._`${i} += "_"`),t.if(s._`typeof ${l}[${i}] == "number"`,()=>{t.assign(a,s._`${l}[${i}]`),e.error(),t.assign(p,!1).break()}).code(s._`${l}[${i}] = ${o}`)})}function m(n,i){const c=(0,o.useFunc)(t,a.default),l=t.name("outer");t.label(l).for(s._`;${n}--;`,()=>t.for(s._`${i} = ${n}; ${i}--;`,()=>t.if(s._`${c}(${r}[${n}], ${r}[${i}])`,()=>{e.error(),t.assign(p,!1).break(l)})))}e.block$data(p,function(){const n=t.let("i",s._`${r}.length`),o=t.let("j");e.setParams({i:n,j:o}),t.assign(p,!0),t.if(s._`${n} > 1`,()=>(h.length>0&&!h.some(e=>"object"===e||"array"===e)?f:m)(n,o))},s._`${u} === false`),e.ok(p)}};t.default=i},2788:(e,t,r)=>{"use strict";const n=r(8465);class s extends Error{constructor(e){super(),this.value=e}}const o=(e,t)=>Promise.resolve(e).then(t),a=e=>Promise.all(e).then(e=>!0===e[1]&&Promise.reject(new s(e[0])));e.exports=(e,t,r)=>{r=Object.assign({concurrency:1/0,preserveOrder:!0},r);const i=n(r.concurrency),c=[...e].map(e=>[e,i(o,e,t)]),l=n(r.preserveOrder?1:1/0);return Promise.all(c.map(e=>l(a,e))).then(()=>{}).catch(e=>e instanceof s?e.value:Promise.reject(e))}},2796:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;const n=r(3789),s=r(7083),o=r(6735);function a(e,t){const r=e.const("err",t);e.if(n._`${o.default.vErrors} === null`,()=>e.assign(o.default.vErrors,n._`[${r}]`),n._`${o.default.vErrors}.push(${r})`),e.code(n._`${o.default.errors}++`)}function i(e,t){const{gen:r,validateName:s,schemaEnv:o}=e;o.$async?r.throw(n._`new ${e.ValidationError}(${t})`):(r.assign(n._`${s}.errors`,t),r.return(!1))}t.keywordError={message:({keyword:e})=>n.str`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?n.str`"${e}" keyword must be ${t} ($data)`:n.str`"${e}" keyword is invalid ($data)`},t.reportError=function(e,r=t.keywordError,s,o){const{it:c}=e,{gen:u,compositeRule:d,allErrors:p}=c,h=l(e,r,s);(null!=o?o:d||p)?a(u,h):i(c,n._`[${h}]`)},t.reportExtraError=function(e,r=t.keywordError,n){const{it:s}=e,{gen:c,compositeRule:u,allErrors:d}=s;a(c,l(e,r,n)),u||d||i(s,o.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(o.default.errors,t),e.if(n._`${o.default.vErrors} !== null`,()=>e.if(t,()=>e.assign(n._`${o.default.vErrors}.length`,t),()=>e.assign(o.default.vErrors,null)))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:s,errsCount:a,it:i}){if(void 0===a)throw new Error("ajv implementation error");const c=e.name("err");e.forRange("i",a,o.default.errors,a=>{e.const(c,n._`${o.default.vErrors}[${a}]`),e.if(n._`${c}.instancePath === undefined`,()=>e.assign(n._`${c}.instancePath`,(0,n.strConcat)(o.default.instancePath,i.errorPath))),e.assign(n._`${c}.schemaPath`,n.str`${i.errSchemaPath}/${t}`),i.opts.verbose&&(e.assign(n._`${c}.schema`,r),e.assign(n._`${c}.data`,s))})};const c={keyword:new n.Name("keyword"),schemaPath:new n.Name("schemaPath"),params:new n.Name("params"),propertyName:new n.Name("propertyName"),message:new n.Name("message"),schema:new n.Name("schema"),parentSchema:new n.Name("parentSchema")};function l(e,t,r){const{createErrors:s}=e.it;return!1===s?n._`{}`:function(e,t,r={}){const{gen:s,it:a}=e,i=[u(a,r),d(e,r)];return function(e,{params:t,message:r},s){const{keyword:a,data:i,schemaValue:l,it:u}=e,{opts:d,propertyName:p,topSchemaRef:h,schemaPath:f}=u;s.push([c.keyword,a],[c.params,"function"==typeof t?t(e):t||n._`{}`]),d.messages&&s.push([c.message,"function"==typeof r?r(e):r]),d.verbose&&s.push([c.schema,l],[c.parentSchema,n._`${h}${f}`],[o.default.data,i]),p&&s.push([c.propertyName,p])}(e,t,i),s.object(...i)}(e,t,r)}function u({errorPath:e},{instancePath:t}){const r=t?n.str`${e}${(0,s.getErrorPath)(t,s.Type.Str)}`:e;return[o.default.instancePath,(0,n.strConcat)(o.default.instancePath,r)]}function d({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:o}){let a=o?t:n.str`${t}/${e}`;return r&&(a=n.str`${a}${(0,s.getErrorPath)(r,s.Type.Str)}`),[c.schemaPath,a]}},2861:(e,t)=>{"use strict";function r(e,t){return t.rules.some(t=>n(e,t))}function n(e,t){var r;return void 0!==e[t.keyword]||(null===(r=t.definition.implements)||void 0===r?void 0:r.some(t=>void 0!==e[t]))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},n){const s=t.RULES.types[n];return s&&!0!==s&&r(e,s)},t.shouldUseGroup=r,t.shouldUseRule=n},2877:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(4629);class s extends Error{constructor(e,t,r,s){super(s||`can't resolve reference ${r} from id ${t}`),this.missingRef=(0,n.resolveUrl)(e,t,r),this.missingSchema=(0,n.normalizeId)((0,n.getFullPath)(e,this.missingRef))}}t.default=s},2884:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=[r(329).default];t.default=n},2888:function(e,t,r){"use strict";e=r.nmd(e);var n,s,o,a,i,c,l=this&&this.__classPrivateFieldSet||function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},u=this&&this.__classPrivateFieldGet||function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};Object.defineProperty(t,"__esModule",{value:!0});const d=r(9023),p=r(9896),h=r(6928),f=r(6982),m=r(2613),g=r(4434),y=r(8486),v=r(4625),w=r(2949),_=r(9999),b=r(8992),$=r(8182),E=r(9797),S=r(9589),P=r(6034),O="aes-256-cbc",C=()=>Object.create(null),N=e=>null!=e;let I="";try{delete r.c[__filename],I=h.dirname(null!==(s=null===(n=e.parent)||void 0===n?void 0:n.filename)&&void 0!==s?s:".")}catch(e){}const T="__internal__",A=`${T}.migrations.version`;class k{constructor(e={}){var t;o.set(this,void 0),a.set(this,void 0),i.set(this,void 0),c.set(this,{}),this._deserialize=e=>JSON.parse(e),this._serialize=e=>JSON.stringify(e,void 0,"\t");const r={configName:"config",fileExtension:"json",projectSuffix:"nodejs",clearInvalidConfig:!1,accessPropertiesByDotNotation:!0,configFileMode:438,...e},n=P(()=>{const e=v.sync({cwd:I}),t=e&&JSON.parse(p.readFileSync(e,"utf8"));return null!=t?t:{}});if(!r.cwd){if(r.projectName||(r.projectName=n().name),!r.projectName)throw new Error("Project name could not be inferred. Please specify the `projectName` option.");r.cwd=w(r.projectName,{suffix:r.projectSuffix}).config}if(l(this,i,r,"f"),r.schema){if("object"!=typeof r.schema)throw new TypeError("The `schema` option must be an object.");const e=new b.default({allErrors:!0,useDefaults:!0});(0,$.default)(e);const t={type:"object",properties:r.schema};l(this,o,e.compile(t),"f");for(const[e,t]of Object.entries(r.schema))(null==t?void 0:t.default)&&(u(this,c,"f")[e]=t.default)}r.defaults&&l(this,c,{...u(this,c,"f"),...r.defaults},"f"),r.serialize&&(this._serialize=r.serialize),r.deserialize&&(this._deserialize=r.deserialize),this.events=new g.EventEmitter,l(this,a,r.encryptionKey,"f");const s=r.fileExtension?`.${r.fileExtension}`:"";this.path=h.resolve(r.cwd,`${null!==(t=r.configName)&&void 0!==t?t:"config"}${s}`);const d=this.store,f=Object.assign(C(),r.defaults,d);this._validate(f);try{m.deepEqual(d,f)}catch(e){this.store=f}if(r.watch&&this._watch(),r.migrations){if(r.projectVersion||(r.projectVersion=n().version),!r.projectVersion)throw new Error("Project version could not be inferred. Please specify the `projectVersion` option.");this._migrate(r.migrations,r.projectVersion,r.beforeEachMigration)}}get(e,t){if(u(this,i,"f").accessPropertiesByDotNotation)return this._get(e,t);const{store:r}=this;return e in r?r[e]:t}set(e,t){if("string"!=typeof e&&"object"!=typeof e)throw new TypeError("Expected `key` to be of type `string` or `object`, got "+typeof e);if("object"!=typeof e&&void 0===t)throw new TypeError("Use `delete()` to clear values");if(this._containsReservedKey(e))throw new TypeError(`Please don't use the ${T} key, as it's used to manage this module internal operations.`);const{store:r}=this,n=(e,t)=>{((e,t)=>{const r=typeof t;if(new Set(["undefined","symbol","function"]).has(r))throw new TypeError(`Setting a value of type \`${r}\` for key \`${e}\` is not allowed as it's not supported by JSON`)})(e,t),u(this,i,"f").accessPropertiesByDotNotation?y.set(r,e,t):r[e]=t};if("object"==typeof e){const t=e;for(const[e,r]of Object.entries(t))n(e,r)}else n(e,t);this.store=r}has(e){return u(this,i,"f").accessPropertiesByDotNotation?y.has(this.store,e):e in this.store}reset(...e){for(const t of e)N(u(this,c,"f")[t])&&this.set(t,u(this,c,"f")[t])}delete(e){const{store:t}=this;u(this,i,"f").accessPropertiesByDotNotation?y.delete(t,e):delete t[e],this.store=t}clear(){this.store=C();for(const e of Object.keys(u(this,c,"f")))this.reset(e)}onDidChange(e,t){if("string"!=typeof e)throw new TypeError("Expected `key` to be of type `string`, got "+typeof e);if("function"!=typeof t)throw new TypeError("Expected `callback` to be of type `function`, got "+typeof t);return this._handleChange(()=>this.get(e),t)}onDidAnyChange(e){if("function"!=typeof e)throw new TypeError("Expected `callback` to be of type `function`, got "+typeof e);return this._handleChange(()=>this.store,e)}get size(){return Object.keys(this.store).length}get store(){try{const e=p.readFileSync(this.path,u(this,a,"f")?null:"utf8"),t=this._encryptData(e),r=this._deserialize(t);return this._validate(r),Object.assign(C(),r)}catch(e){if("ENOENT"===(null==e?void 0:e.code))return this._ensureDirectory(),C();if(u(this,i,"f").clearInvalidConfig&&"SyntaxError"===e.name)return C();throw e}}set store(e){this._ensureDirectory(),this._validate(e),this._write(e),this.events.emit("change")}*[(o=new WeakMap,a=new WeakMap,i=new WeakMap,c=new WeakMap,Symbol.iterator)](){for(const[e,t]of Object.entries(this.store))yield[e,t]}_encryptData(e){if(!u(this,a,"f"))return e.toString();try{if(u(this,a,"f"))try{if(":"===e.slice(16,17).toString()){const t=e.slice(0,16),r=f.pbkdf2Sync(u(this,a,"f"),t.toString(),1e4,32,"sha512"),n=f.createDecipheriv(O,r,t);e=Buffer.concat([n.update(Buffer.from(e.slice(17))),n.final()]).toString("utf8")}else{const t=f.createDecipher(O,u(this,a,"f"));e=Buffer.concat([t.update(Buffer.from(e)),t.final()]).toString("utf8")}}catch(e){}}catch(e){}return e.toString()}_handleChange(e,t){let r=e();const n=()=>{const n=r,s=e();(0,d.isDeepStrictEqual)(s,n)||(r=s,t.call(this,s,n))};return this.events.on("change",n),()=>this.events.removeListener("change",n)}_validate(e){if(!u(this,o,"f"))return;if(u(this,o,"f").call(this,e)||!u(this,o,"f").errors)return;const t=u(this,o,"f").errors.map(({instancePath:e,message:t=""})=>`\`${e.slice(1)}\` ${t}`);throw new Error("Config schema violation: "+t.join("; "))}_ensureDirectory(){p.mkdirSync(h.dirname(this.path),{recursive:!0})}_write(e){let t=this._serialize(e);if(u(this,a,"f")){const e=f.randomBytes(16),r=f.pbkdf2Sync(u(this,a,"f"),e.toString(),1e4,32,"sha512"),n=f.createCipheriv(O,r,e);t=Buffer.concat([e,Buffer.from(":"),n.update(Buffer.from(t)),n.final()])}if(process.env.SNAP)p.writeFileSync(this.path,t,{mode:u(this,i,"f").configFileMode});else try{_.writeFileSync(this.path,t,{mode:u(this,i,"f").configFileMode})}catch(e){if("EXDEV"===(null==e?void 0:e.code))return void p.writeFileSync(this.path,t,{mode:u(this,i,"f").configFileMode});throw e}}_watch(){this._ensureDirectory(),p.existsSync(this.path)||this._write(C()),"win32"===process.platform?p.watch(this.path,{persistent:!1},E(()=>{this.events.emit("change")},{wait:100})):p.watchFile(this.path,{persistent:!1},E(()=>{this.events.emit("change")},{wait:5e3}))}_migrate(e,t,r){let n=this._get(A,"0.0.0");const s=Object.keys(e).filter(e=>this._shouldPerformMigration(e,n,t));let o={...this.store};for(const a of s)try{r&&r(this,{fromVersion:n,toVersion:a,finalVersion:t,versions:s}),(0,e[a])(this),this._set(A,a),n=a,o={...this.store}}catch(e){throw this.store=o,new Error(`Something went wrong during the migration! Changes applied to the store until this failed migration will be restored. ${e}`)}!this._isVersionInRangeFormat(n)&&S.eq(n,t)||this._set(A,t)}_containsReservedKey(e){return"object"==typeof e&&Object.keys(e)[0]===T||"string"==typeof e&&!!u(this,i,"f").accessPropertiesByDotNotation&&!!e.startsWith(`${T}.`)}_isVersionInRangeFormat(e){return null===S.clean(e)}_shouldPerformMigration(e,t,r){return this._isVersionInRangeFormat(e)?("0.0.0"===t||!S.satisfies(t,e))&&S.satisfies(r,e):!S.lte(e,t)&&!S.gt(e,r)}_get(e,t){return y.get(this.store,e,t)}_set(e,t){const{store:r}=this;y.set(r,e,t),this.store=r}}t.default=k,e.exports=k,e.exports.default=k},2916:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={},n={next:e=>{const t=r[e];if(!t)return;t.shift();const s=t[0];s?s(()=>n.next(e)):delete r[e]},schedule:e=>new Promise(t=>{let s=r[e];s||(s=r[e]=[]),s.push(t),s.length>1||t(()=>n.next(e))})};t.default=n},2938:(e,t,r)=>{"use strict";const n=r(3908);e.exports=(e,t)=>new n(e,t).major},2944:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1039),s=r(111),o={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>s.str`must match pattern "${e}"`,params:({schemaCode:e})=>s._`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:o,schemaCode:a,it:i}=e,c=i.opts.unicodeRegExp?"u":"",l=r?s._`(new RegExp(${a}, ${c}))`:(0,n.usePattern)(e,o);e.fail$data(s._`!${l}.test(${t})`)}};t.default=o},2949:(e,t,r)=>{"use strict";const n=r(6928),s=r(857),o=s.homedir(),a=s.tmpdir(),{env:i}=process,c=(e,t)=>{if("string"!=typeof e)throw new TypeError("Expected string, got "+typeof e);return(t=Object.assign({suffix:"nodejs"},t)).suffix&&(e+=`-${t.suffix}`),"darwin"===process.platform?(e=>{const t=n.join(o,"Library");return{data:n.join(t,"Application Support",e),config:n.join(t,"Preferences",e),cache:n.join(t,"Caches",e),log:n.join(t,"Logs",e),temp:n.join(a,e)}})(e):"win32"===process.platform?(e=>{const t=i.APPDATA||n.join(o,"AppData","Roaming"),r=i.LOCALAPPDATA||n.join(o,"AppData","Local");return{data:n.join(r,e,"Data"),config:n.join(t,e,"Config"),cache:n.join(r,e,"Cache"),log:n.join(r,e,"Log"),temp:n.join(a,e)}})(e):(e=>{const t=n.basename(o);return{data:n.join(i.XDG_DATA_HOME||n.join(o,".local","share"),e),config:n.join(i.XDG_CONFIG_HOME||n.join(o,".config"),e),cache:n.join(i.XDG_CACHE_HOME||n.join(o,".cache"),e),log:n.join(i.XDG_STATE_HOME||n.join(o,".local","state"),e),temp:n.join(a,t,e)}})(e)};e.exports=c,e.exports.default=c},3007:(e,t,r)=>{"use strict";const n=r(3908);e.exports=(e,t,r,s,o)=>{"string"==typeof r&&(o=s,s=r,r=void 0);try{return new n(e instanceof n?e.version:e,r).inc(t,s,o).version}catch(e){return null}}},3043:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const n=r(7083),s=r(2017),o=r(7242),a=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!c(e):!!t&&l(e)<=t)};const i=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function c(e){for(const t in e){if(i.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(c))return!0;if("object"==typeof r&&c(r))return!0}return!1}function l(e){let t=0;for(const r in e){if("$ref"===r)return 1/0;if(t++,!a.has(r)&&("object"==typeof e[r]&&(0,n.eachItem)(e[r],e=>t+=l(e)),t===1/0))return 1/0}return t}function u(e,t="",r){!1!==r&&(t=h(t));const n=e.parse(t);return d(e,n)}function d(e,t){return e.serialize(t).split("#")[0]+"#"}t.getFullPath=u,t._getFullPath=d;const p=/#\/?$/;function h(e){return e?e.replace(p,""):""}t.normalizeId=h,t.resolveUrl=function(e,t,r){return r=h(r),e.resolve(t,r)};const f=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};const{schemaId:r,uriResolver:n}=this.opts,a=h(e[r]||t),i={"":a},c=u(n,a,!1),l={},d=new Set;return o(e,{allKeys:!0},(e,t,n,s)=>{if(void 0===s)return;const o=c+t;let a=i[s];function u(t){const r=this.opts.uriResolver.resolve;if(t=h(a?r(a,t):t),d.has(t))throw m(t);d.add(t);let n=this.refs[t];return"string"==typeof n&&(n=this.refs[n]),"object"==typeof n?p(e,n.schema,t):t!==h(o)&&("#"===t[0]?(p(e,l[t],t),l[t]=e):this.refs[t]=o),t}function g(e){if("string"==typeof e){if(!f.test(e))throw new Error(`invalid anchor "${e}"`);u.call(this,`#${e}`)}}"string"==typeof e[r]&&(a=u.call(this,e[r])),g.call(this,e.$anchor),g.call(this,e.$dynamicAnchor),i[t]=a}),l;function p(e,t,r){if(void 0!==t&&!s(e,t))throw m(r)}function m(e){return new Error(`reference "${e}" resolves to more than one schema`)}}},3044:(e,t,r)=>{"use strict";const n=r(6928),s=r(5465);e.exports=(e,t={})=>{const r=n.resolve(t.cwd||""),{root:o}=n.parse(r),a=[].concat(e);return new Promise(e=>{!function t(r){s(a,{cwd:r}).then(s=>{s?e(n.join(r,s)):r===o?e(null):t(n.dirname(r))})}(r)})},e.exports.sync=(e,t={})=>{let r=n.resolve(t.cwd||"");const{root:o}=n.parse(r),a=[].concat(e);for(;;){const e=s.sync(a,{cwd:r});if(e)return n.join(r,e);if(r===o)return null;r=n.dirname(r)}}},3106:e=>{"use strict";e.exports=require("zlib")},3186:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r,t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class n extends r{constructor(e){if(super(),!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}t.Name=n;class s extends r{constructor(e){super(),this._items="string"==typeof e?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const e=this._items[0];return""===e||'""'===e}get str(){var e;return null!==(e=this._str)&&void 0!==e?e:this._str=this._items.reduce((e,t)=>`${e}${t}`,"")}get names(){var e;return null!==(e=this._names)&&void 0!==e?e:this._names=this._items.reduce((e,t)=>(t instanceof n&&(e[t.str]=(e[t.str]||0)+1),e),{})}}function o(e,...t){const r=[e[0]];let n=0;for(;n<t.length;)c(r,t[n]),r.push(e[++n]);return new s(r)}t._Code=s,t.nil=new s(""),t._=o;const a=new s("+");function i(e,...t){const r=[u(e[0])];let n=0;for(;n<t.length;)r.push(a),c(r,t[n]),r.push(a,u(e[++n]));return function(e){let t=1;for(;t<e.length-1;){if(e[t]===a){const r=l(e[t-1],e[t+1]);if(void 0!==r){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}(r),new s(r)}function c(e,t){var r;t instanceof s?e.push(...t._items):t instanceof n?e.push(t):e.push("number"==typeof(r=t)||"boolean"==typeof r||null===r?r:u(Array.isArray(r)?r.join(","):r))}function l(e,t){if('""'===t)return e;if('""'===e)return t;if("string"==typeof e){if(t instanceof n||'"'!==e[e.length-1])return;return"string"!=typeof t?`${e.slice(0,-1)}${t}"`:'"'===t[0]?e.slice(0,-1)+t.slice(1):void 0}return"string"!=typeof t||'"'!==t[0]||e instanceof n?void 0:`"${e}${t.slice(1)}`}function u(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.str=i,t.addCodeArg=c,t.strConcat=function(e,t){return t.emptyStr()?e:e.emptyStr()?t:i`${e}${t}`},t.stringify=function(e){return new s(u(e))},t.safeStringify=u,t.getProperty=function(e){return"string"==typeof e&&t.IDENTIFIER.test(e)?new s(`.${e}`):o`[${e}]`},t.getEsmExportName=function(e){if("string"==typeof e&&t.IDENTIFIER.test(e))return new s(`${e}`);throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)},t.regexpCode=function(e){return new s(e.toString())}},3189:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>n._`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:o,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(a.opts.discriminator&&o.discriminator)return;const i=r,c=t.let("valid",!1),l=t.let("passing",null),u=t.name("_valid");e.setParams({passing:l}),t.block(function(){i.forEach((r,o)=>{let i;(0,s.alwaysValidSchema)(a,r)?t.var(u,!0):i=e.subschema({keyword:"oneOf",schemaProp:o,compositeRule:!0},u),o>0&&t.if(n._`${u} && ${c}`).assign(c,!1).assign(l,n._`[${l}, ${o}]`).else(),t.if(u,()=>{t.assign(c,!0),t.assign(l,o),i&&e.mergeEvaluated(i,n.Name)})})}),e.result(c,()=>e.reset(),()=>e.error(!0))}};t.default=o},3193:e=>{"use strict";e.exports=require("string_decoder")},3281:(e,t,r)=>{"use strict";var n=r(4157),s=r(5879),o=r(3423),a=r.n(o),i=r(6928),c=r(4652),l=r(6982);const u=require("yauzl"),d=[{pattern:/^mp_(m|f)_freemode_01_p_(\d+)_(\d+)\.ydd$/,genderGroup:1,slotGroup:2,drawableGroup:2,textureGroup:3,description:"Standard prop format"},{pattern:/^mp_(m|f)_freemode_01_(\d+)_(\d+)\.ydd$/,genderGroup:1,slotGroup:2,drawableGroup:2,textureGroup:3,description:"Standard component format"},{pattern:/^mp_(m|f)_(\w+)_(\d+)_(\d+)\.(ydd|ytd)$/,genderGroup:1,slotGroup:2,drawableGroup:3,textureGroup:4,description:"Named slot format"}];class p{constructor(e){this.tempDir=e||i.join(r(857).tmpdir(),"dripforge-pro","imports")}async ingestAssets(e,t){const r=Date.now(),n={success:!0,itemsImported:0,itemsSkipped:0,errors:[],warnings:[],conflicts:[],duration:0};try{await c.ensureDir(this.tempDir);for(const r of e){const e=await c.stat(r);if(e.isDirectory())await this.processDirectory(r,t,n);else if(e.isFile()){const e=i.extname(r).toLowerCase();".zip"===e||".rar"===e?await this.processArchive(r,t,n):".ydd"===e||".ytd"===e?await this.processFile(r,t,n):n.warnings.push(`Unsupported file type: ${r}`)}}n.duration=Date.now()-r,n.success=0===n.errors.length}catch(e){n.success=!1,n.errors.push({file:"general",error:`Ingest failed: ${e instanceof Error?e.message:String(e)}`,severity:"error"})}return n}async processDirectory(e,t,r){try{const n=await c.readdir(e,{withFileTypes:!0});for(const s of n){const n=i.join(e,s.name);if(s.isDirectory())await this.processDirectory(n,t,r);else if(s.isFile()){const e=i.extname(s.name).toLowerCase();".ydd"===e||".ytd"===e?await this.processFile(n,t,r):".zip"!==e&&".rar"!==e||await this.processArchive(n,t,r)}}}catch(t){r.errors.push({file:e,error:`Failed to process directory: ${t instanceof Error?t.message:String(t)}`,severity:"error"})}}async processArchive(e,t,r){const n=i.join(this.tempDir,`extract_${Date.now()}`);try{await c.ensureDir(n),".zip"===i.extname(e).toLowerCase()?(await this.extractZip(e,n),await this.processDirectory(n,t,r)):r.warnings.push(`RAR archives not yet supported: ${e}`)}catch(t){r.errors.push({file:e,error:`Failed to process archive: ${t instanceof Error?t.message:String(t)}`,severity:"error"})}finally{await c.remove(n).catch(()=>{})}}async extractZip(e,t){return new Promise((r,n)=>{u.open(e,{lazyEntries:!0},(e,s)=>{if(e)return n(e);s.readEntry(),s.on("entry",e=>{if(/\/$/.test(e.fileName))s.readEntry();else{const r=i.join(t,e.fileName);c.ensureDir(i.dirname(r)).then(()=>{s.openReadStream(e,(e,t)=>{if(e)return n(e);const o=c.createWriteStream(r);t.pipe(o),o.on("close",()=>s.readEntry())})})}}),s.on("end",()=>r()),s.on("error",n)})})}async processFile(e,t,r){try{const n=i.basename(e),s=this.detectAssetType(n);if(!s)return r.warnings.push(`Could not detect asset type for: ${n}`),void r.itemsSkipped++;const o=await this.createItem(e,s,t);o?(await this.checkForConflicts(o)&&r.conflicts.push({file:n,existingItem:"existing-item-id",conflictType:"slot",resolution:"rename"}),r.itemsImported++):r.itemsSkipped++}catch(t){r.errors.push({file:e,error:`Failed to process file: ${t instanceof Error?t.message:String(t)}`,severity:"error"})}}detectAssetType(e){for(const t of d){const r=e.match(t.pattern);if(r){const e=r[t.genderGroup],n=r[t.slotGroup],s=parseInt(r[t.drawableGroup]),o=parseInt(r[t.textureGroup]),a="m"===e?"male":"female",i=this.isComponentSlot(n),c=i?"component":"prop",l=i?this.mapToComponent(n):this.mapToProp(n);if(l)return{gender:a,slot:l,kind:c,drawable:s,texture:o,confidence:.9,source:"filename"}}}return null}isComponentSlot(e){return["0","1","2","3","4","5","6","7","8","9","10","11"].includes(e)||["face","mask","hair","torso","legs","bag","feet","accs","undershirt","armor","decals","tops"].includes(e.toLowerCase())}mapToComponent(e){return{0:"face",face:"face",1:"mask",mask:"mask",2:"hair",hair:"hair",3:"torso",torso:"torso",4:"legs",legs:"legs",5:"bag",bag:"bag",6:"feet",feet:"feet",7:"accs",accs:"accs",8:"undershirt",undershirt:"undershirt",9:"armor",armor:"armor",10:"decals",decals:"decals",11:"tops",tops:"tops"}[e.toLowerCase()]||null}mapToProp(e){return{0:"hat",hat:"hat",1:"glasses",glasses:"glasses",2:"ears",ears:"ears",6:"watch",watch:"watch",7:"bracelet",bracelet:"bracelet"}[e.toLowerCase()]||null}async createItem(e,t,r){try{const n=await c.stat(e),s=await c.readFile(e),o=l.createHash("md5").update(s).digest("hex");return{id:this.generateId(),gender:t.gender,kind:t.kind,slot:t.slot,drawable:t.drawable,textures:[t.texture],files:{ydd:".ydd"===i.extname(e)?e:"",ytds:".ytd"===i.extname(e)?[e]:[],originalPath:e,size:n.size,checksum:o},metadata:{imported:new Date,modified:new Date,source:r,version:"1.0.0"}}}catch(e){return console.error("Failed to create item:",e),null}}async checkForConflicts(e){return!1}generateId(){return Math.random().toString(36).substr(2,9)+Date.now().toString(36)}}class h{constructor(){this.slotMappings=new Map,this.reservedRanges=[]}async routeSlots(e,t){this.reservedRanges=t.reservedRanges;const r=[],n=[],s=[];for(const t of e){const e=this.detectConflict(t);if(e){const o=await this.resolveConflict(t,e);n.push(o);const a=this.applyResolution(t,o);r.push(a);const i=this.createSlotMapping(a);s.push(i),this.slotMappings.set(this.getMappingKey(i),i)}else{r.push(t);const e=this.createSlotMapping(t);s.push(e),this.slotMappings.set(this.getMappingKey(e),e)}}return{resolvedItems:r,conflicts:n,newMappings:s}}async checkConflicts(e){const t=[];for(const r of e){const e=this.detectConflict(r);if(e){const n=await this.resolveConflict(r,e);t.push(n)}}return t}detectConflict(e){const t=this.getItemMappingKey(e),r=this.slotMappings.get(t);return r&&r.assignedTo!==e.id?r:this.isSlotReserved(e)?{gender:e.gender,slot:e.slot,drawable:e.drawable,texture:e.textures[0]||0,reserved:!0}:null}async resolveConflict(e,t){const r=this.createSlotMapping(e),n=this.findNextAvailableSlot(e);return{itemId:e.id,originalSlot:r,newSlot:n,reason:t.reserved?"Reserved range conflict":"Slot already occupied",autoResolved:!0}}findNextAvailableSlot(e){let t=e.drawable,r=0;for(;r<1e3;){if(t++,this.isSlotInReservedRange(e.gender,e.slot,t))continue;const n=`${e.gender}_${e.slot}_${t}_${e.textures[0]||0}`;if(!this.slotMappings.has(n))return{gender:e.gender,slot:e.slot,drawable:t,texture:e.textures[0]||0,reserved:!1,assignedTo:e.id};r++}return{gender:e.gender,slot:e.slot,drawable:9e3+Math.floor(1e3*Math.random()),texture:e.textures[0]||0,reserved:!1,assignedTo:e.id}}applyResolution(e,t){return{...e,drawable:t.newSlot.drawable,textures:[t.newSlot.texture]}}isSlotReserved(e){return this.isSlotInReservedRange(e.gender,e.slot,e.drawable)}isSlotInReservedRange(e,t,r){return this.reservedRanges.some(n=>n.gender===e&&n.slot===t&&r>=n.startDrawable&&r<=n.endDrawable)}createSlotMapping(e){return{gender:e.gender,slot:e.slot,drawable:e.drawable,texture:e.textures[0]||0,reserved:!1,assignedTo:e.id}}getItemMappingKey(e){return`${e.gender}_${e.slot}_${e.drawable}_${e.textures[0]||0}`}getMappingKey(e){return`${e.gender}_${e.slot}_${e.drawable}_${e.texture}`}loadExistingMappings(e){this.slotMappings.clear();for(const t of e){const e=this.getMappingKey(t);this.slotMappings.set(e,t)}}getAllMappings(){return Array.from(this.slotMappings.values())}getSlotStatistics(){const e={totalSlots:this.slotMappings.size,usedSlots:this.slotMappings.size,reservedSlots:0,availableSlots:0,byGender:{male:{total:0,used:0,reserved:0},female:{total:0,used:0,reserved:0}},bySlot:{}};for(const t of this.reservedRanges){const r=t.endDrawable-t.startDrawable+1;e.reservedSlots+=r,e.byGender[t.gender].reserved+=r;const n=`${t.gender}_${t.slot}`;e.bySlot[n]||(e.bySlot[n]={total:0,used:0,reserved:0}),e.bySlot[n].reserved+=r}for(const t of this.slotMappings.values()){e.byGender[t.gender].used++;const r=`${t.gender}_${t.slot}`;e.bySlot[r]||(e.bySlot[r]={total:0,used:0,reserved:0}),e.bySlot[r].used++}return e.byGender.male.total=1e4,e.byGender.female.total=1e4,e.availableSlots=2e4-e.usedSlots-e.reservedSlots,e}validateMappings(){const e=[],t=[],r=new Set,n=new Map;for(const e of this.slotMappings.values()){const s=this.getMappingKey(e);r.has(s)&&(n.has(s)||n.set(s,[]),n.get(s).push(e.assignedTo||"unknown")),r.add(s),this.isSlotInReservedRange(e.gender,e.slot,e.drawable)&&!e.reserved&&t.push(`Slot ${s} is in reserved range but not marked as reserved`)}for(const[t,r]of n)e.push(`Duplicate slot assignment ${t} for items: ${r.join(", ")}`);return{isValid:0===e.length,errors:e,warnings:t}}}class f{constructor(e){this.tempDir=e||i.join(r(857).tmpdir(),"dripforge-pro","qa")}async validateAssets(e){const t=new Map;let r=0,n=0,s=0;for(const o of e)try{const e=this.createMockItem(o),a=await this.validateSingleAsset(e);t.set(o,a),a.isValid?r++:n++,s+=a.warnings.length}catch(e){const r={isValid:!1,errors:[{type:"missing_file",message:`Failed to validate asset: ${e instanceof Error?e.message:String(e)}`,severity:"critical"}],warnings:[],metrics:this.getDefaultMetrics()};t.set(o,r),n++}return{results:t,summary:{totalItems:e.length,passed:r,failed:n,warnings:s}}}async validateSingleAsset(e){const t=[],r=[],n=await this.calculateAssetMetrics(e);e.files.ydd&&!await c.pathExists(e.files.ydd)&&t.push({type:"missing_file",message:"YDD file not found",file:e.files.ydd,severity:"critical"});for(const r of e.files.ytds)await c.pathExists(r)||t.push({type:"missing_file",message:"YTD file not found",file:r,severity:"critical"});return e.files.size>52428800&&r.push({type:"performance",message:"Asset file size is very large (>50MB)",suggestion:"Consider optimizing textures or reducing mesh complexity"}),n.triangleCount>5e4&&r.push({type:"performance",message:`High triangle count: ${n.triangleCount}`,suggestion:"Consider creating LOD models or reducing mesh complexity"}),n.lodCoverage<.5&&r.push({type:"quality",message:"Low LOD coverage detected",suggestion:"Generate additional LOD levels for better performance"}),n.textureMemory>104857600&&r.push({type:"performance",message:`High texture memory usage: ${Math.round(n.textureMemory/1024/1024)}MB`,suggestion:"Consider compressing textures or reducing resolution"}),{isValid:0===t.length,errors:t,warnings:r,metrics:n}}async detectClipping(e,t="fast"){const r=[];let n=0;const s=e.filter(e=>"male"===e.gender),o=e.filter(e=>"female"===e.gender);await this.checkClippingInGroup(s,r,t),await this.checkClippingInGroup(o,r,t),n=r.reduce((e,t)=>e+t.intersectionVolume,0);let a="none",i=0;if(r.length>0){const e=n/r.length;e<.01?(a="minor",i=25):e<.05?(a="moderate",i=50):(a="severe",i=75),i+=Math.min(5*r.length,25)}return{hasClipping:r.length>0,clippingPairs:r,severity:a,score:Math.min(i,100)}}async checkClippingInGroup(e,t,r){for(let n=0;n<e.length;n++)for(let s=n+1;s<e.length;s++){const o=e[n],a=e[s];if(o.slot===a.slot)continue;if(this.isExpectedOverlap(o.slot,a.slot))continue;const i=await this.checkClippingBetweenItems(o,a,r);i&&t.push(i)}}async checkClippingBetweenItems(e,t,r){return"fast"===r?this.fastClippingCheck(e,t):this.deepClippingCheck(e,t)}async fastClippingCheck(e,t){const r=this.getMockBoundingBox(e),n=this.getMockBoundingBox(t),s=this.calculateBoundingBoxIntersection(r,n);return s>0?{model1:e.id,model2:t.id,intersectionVolume:s,intersectionPoints:[],severity:s>.05?"severe":s>.01?"moderate":"minor"}:null}async deepClippingCheck(e,t){return this.fastClippingCheck(e,t)}isExpectedOverlap(e,t){return[["undershirt","torso"],["undershirt","tops"],["torso","tops"],["legs","feet"]].some(r=>r[0]===e&&r[1]===t||r[0]===t&&r[1]===e)}async calculateAssetMetrics(e){return{totalFiles:1+e.files.ytds.length,totalSize:e.files.size,triangleCount:Math.floor(3e4*Math.random())+5e3,textureMemory:Math.floor(50*Math.random()*1024*1024),lodCoverage:Math.random(),averageQuality:.8+.2*Math.random()}}getDefaultMetrics(){return{totalFiles:0,totalSize:0,triangleCount:0,textureMemory:0,lodCoverage:0,averageQuality:0}}createMockItem(e){return{id:e,gender:"male",kind:"component",slot:"torso",drawable:1,textures:[0],files:{ydd:`/mock/path/${e}.ydd`,ytds:[`/mock/path/${e}.ytd`],originalPath:`/mock/path/${e}`,size:Math.floor(10*Math.random()*1024*1024),checksum:"mock-checksum"},metadata:{imported:new Date,modified:new Date,source:"mock",version:"1.0.0"}}}getMockBoundingBox(e){return{torso:{min:[-.5,.5,-.3],max:[.5,1.5,.3]},legs:{min:[-.4,-.5,-.3],max:[.4,.8,.3]},feet:{min:[-.2,-.8,-.4],max:[.2,-.3,.2]}}[e.slot]||{min:[-.3,-.3,-.3],max:[.3,.3,.3]}}calculateBoundingBoxIntersection(e,t){return Math.max(0,Math.min(e.max[0],t.max[0])-Math.max(e.min[0],t.min[0]))*Math.max(0,Math.min(e.max[1],t.max[1])-Math.max(e.min[1],t.min[1]))*Math.max(0,Math.min(e.max[2],t.max[2])-Math.max(e.min[2],t.min[2]))}}const m={Config:{Locale:"en",Price:100,DrawDistance:100,MarkerSize:{x:1.5,y:1.5,z:1},MarkerColor:{r:102,g:102,b:204},MarkerType:1},Shops:[]};class g{constructor(e){this.tempDir=e||i.join(r(857).tmpdir(),"dripforge-pro","exports")}async exportProject(e,t){const r=[];await c.ensureDir(e.outputPath);for(const n of e.formats)if(n.enabled)try{const s=await this.exportFormat(n.type,e,t);r.push(s)}catch(t){r.push({format:n.type,outputPath:e.outputPath,fileCount:0,totalSize:0,success:!1,errors:[`Export failed: ${t instanceof Error?t.message:String(t)}`],warnings:[]})}return r}async exportFormat(e,t,r){switch(e){case"fivem-resource":return this.exportFiveMResource(t,r);case"qb-clothing":return this.exportQBClothing(t,r);case"esx":return this.exportESX(t,r);case"icons":return this.exportIcons(t,r);case"marketing":return this.exportMarketing(t,r);case"escrow":return this.exportEscrow(t,r);default:throw new Error(`Unsupported export format: ${e}`)}}async exportFiveMResource(e,t){const r=i.join(e.outputPath,"fivem-resource");await c.ensureDir(r);let n=0,s=0;const o=[];try{const o={fx_version:"cerulean",game:"gta5",lua54:"yes",author:e.metadata.author,description:e.metadata.description,version:e.metadata.version},a=i.join(r,"stream");await c.ensureDir(a);const l=[];for(const e of t){if(e.files.ydd&&await c.pathExists(e.files.ydd)){const t=this.generateFileName(e,"ydd"),r=i.join(a,t);await c.copy(e.files.ydd,r),l.push(`stream/${t}`),s+=(await c.stat(r)).size,n++}for(const t of e.files.ytds)if(await c.pathExists(t)){const r=this.generateFileName(e,"ytd"),o=i.join(a,r);await c.copy(t,o),l.push(`stream/${r}`),s+=(await c.stat(o)).size,n++}}o.files=l;const u=i.join(r,"fxmanifest.lua");await c.writeFile(u,this.generateManifestLua(o)),n++,e.options.includeDocumentation&&(await this.createDocumentation(r,t,e),n+=3)}catch(e){o.push(`FiveM resource export failed: ${e instanceof Error?e.message:String(e)}`)}return{format:"fivem-resource",outputPath:r,fileCount:n,totalSize:s,success:0===o.length,errors:o,warnings:[]}}async exportQBClothing(e,t){const r=i.join(e.outputPath,"qb-clothing.json"),n={male:{},female:{}},s=t.filter(e=>"male"===e.gender),o=t.filter(e=>"female"===e.gender);return this.populateQBConfig(n.male,s),this.populateQBConfig(n.female,o),await c.writeFile(r,JSON.stringify(n,null,2)),{format:"qb-clothing",outputPath:r,fileCount:1,totalSize:(await c.stat(r)).size,success:!0,errors:[],warnings:[]}}async exportESX(e,t){const r=i.join(e.outputPath,"esx-config.lua"),n={...m};n.Shops=[{name:"Clothing Store",coords:{x:72.3,y:-1399.1,z:29.4},clothes:{male:{},female:{}}}],t.filter(e=>"male"===e.gender),t.filter(e=>"female"===e.gender);const s=this.generateESXLua(n);return await c.writeFile(r,s),{format:"esx",outputPath:r,fileCount:1,totalSize:(await c.stat(r)).size,success:!0,errors:[],warnings:[]}}async exportIcons(e,t){const r=i.join(e.outputPath,"icons");await c.ensureDir(r);let n=0,s=0;for(const e of t){const t=`${e.gender}_${e.slot}_${e.drawable}_${e.textures[0]||0}.png`,o=i.join(r,t),a=Buffer.from([137,80,78,71,13,10,26,10,0,0,0,13,73,72,68,82,0,0,0,1,0,0,0,1,8,6,0,0,0,31,21,196,137,0,0,0,10,73,68,65,84,120,156,99,0,1,0,0,5,0,1,13,10,45,180,0,0,0,0,73,69,78,68,174,66,96,130]);await c.writeFile(o,a),s+=a.length,n++}return{format:"icons",outputPath:r,fileCount:n,totalSize:s,success:!0,errors:[],warnings:["Icons are placeholder - 3D rendering not yet implemented"]}}async exportMarketing(e,t){const r=i.join(e.outputPath,"marketing");return await c.ensureDir(r),await c.ensureDir(i.join(r,"individual")),await c.ensureDir(i.join(r,"collections")),await c.ensureDir(i.join(r,"banners")),{format:"marketing",outputPath:r,fileCount:0,totalSize:0,success:!0,errors:[],warnings:["Marketing materials generation not yet implemented"]}}async exportEscrow(e,t){const r=i.join(e.outputPath,"escrow");await c.ensureDir(r);const n=i.join(r,"clean");return await c.ensureDir(n),{format:"escrow",outputPath:r,fileCount:0,totalSize:0,success:!0,errors:[],warnings:["Escrow packaging not yet implemented"]}}generateFileName(e,t){const r="prop"===e.kind?"p_":"";return`mp_${"male"===e.gender?"m":"f"}_freemode_01_${r}${e.drawable}_${e.textures[0]||0}.${t}`}generateManifestLua(e){return`fx_version '${e.fx_version}'\ngame '${e.game}'\n${e.lua54?`lua54 '${e.lua54}'`:""}\n\nauthor '${e.author}'\ndescription '${e.description}'\nversion '${e.version}'\n\nfiles {\n${e.files?.map(e=>`    '${e}'`).join(",\n")||""}\n}`}populateQBConfig(e,t){for(const r of t){const t=this.mapSlotToQB(r.slot);t&&(e[t]||(e[t]=[]),e[t].push({drawable:r.drawable,texture:r.textures[0]||0,label:r.labels?.[0]||`${r.slot} ${r.drawable}`}))}}mapSlotToQB(e){return{face:"face",mask:"mask",hair:"hair",torso:"torso",legs:"legs",bag:"bag",feet:"shoes",accs:"accessory",undershirt:"undershirt",armor:"kevlar",decals:"badge",tops:"torso2"}[e]||null}generateESXLua(e){return`Config = {}\nConfig.Locale = '${e.Config.Locale}'\nConfig.Price = ${e.Config.Price}\nConfig.DrawDistance = ${e.Config.DrawDistance}\n\n-- Shops configuration would go here\n-- This is a simplified version\n`}async createDocumentation(e,t,r){const n=i.join(e,"docs");await c.ensureDir(n);const s=`# ${r.metadata.description}\n\nGenerated by DripForge Pro\n\n## Installation\n1. Place the resource in your resources folder\n2. Add \`ensure ${i.basename(e)}\` to your server.cfg\n3. Restart your server\n\n## Items Included\n- ${t.length} clothing items\n- ${t.filter(e=>"male"===e.gender).length} male items\n- ${t.filter(e=>"female"===e.gender).length} female items\n\n## Support\nVisit: ${r.metadata.website||"N/A"}\nDiscord: ${r.metadata.discord||"N/A"}\n`;await c.writeFile(i.join(n,"README.md"),s);const o=t.map(e=>`${e.gender}_${e.slot}_${e.drawable}_${e.textures[0]||0}`).join("\n");await c.writeFile(i.join(n,"slot-mapping.txt"),o),await c.writeFile(i.join(n,"INSTALLATION.md"),"# Installation Guide\n\n## Requirements\n- FiveM Server\n- Compatible clothing framework (QB-Core, ESX, etc.)\n\n## Steps\n1. Download and extract the resource\n2. Place in your resources folder\n3. Add to server.cfg\n4. Configure clothing shop (if applicable)\n5. Restart server\n\n## Troubleshooting\n- Ensure all files are properly streamed\n- Check for slot conflicts with existing clothing\n- Verify framework compatibility\n")}}function y(e,t){const r="darwin"===process.platform,s=[...r?[{label:n.app.getName(),submenu:[{role:"about"},{type:"separator"},{label:"Preferences...",accelerator:"Cmd+,",click:()=>e?.webContents.send("menu:preferences")},{type:"separator"},{role:"services"},{type:"separator"},{role:"hide"},{role:"hideOthers"},{role:"unhide"},{type:"separator"},{role:"quit"}]}]:[],{label:"File",submenu:[{label:"New Project",accelerator:"CmdOrCtrl+N",click:()=>e?.webContents.send("menu:new-project")},{label:"Open Project...",accelerator:"CmdOrCtrl+O",click:()=>e?.webContents.send("menu:open-project")},{label:"Open Recent",submenu:v(t,e)},{type:"separator"},{label:"Save Project",accelerator:"CmdOrCtrl+S",click:()=>e?.webContents.send("menu:save-project")},{label:"Save Project As...",accelerator:"CmdOrCtrl+Shift+S",click:()=>e?.webContents.send("menu:save-project-as")},{type:"separator"},{label:"Import Assets...",accelerator:"CmdOrCtrl+I",click:()=>e?.webContents.send("menu:import-assets")},{label:"Export Project...",accelerator:"CmdOrCtrl+E",click:()=>e?.webContents.send("menu:export-project")},{type:"separator"},...r?[]:[{label:"Preferences...",accelerator:"Ctrl+,",click:()=>e?.webContents.send("menu:preferences")},{type:"separator"}],r?{role:"close"}:{role:"quit"}]},{label:"Edit",submenu:[{role:"undo"},{role:"redo"},{type:"separator"},{role:"cut"},{role:"copy"},{role:"paste"},{role:"selectAll"},{type:"separator"},{label:"Find",accelerator:"CmdOrCtrl+F",click:()=>e?.webContents.send("menu:find")},{label:"Find Next",accelerator:"CmdOrCtrl+G",click:()=>e?.webContents.send("menu:find-next")}]},{label:"View",submenu:[{label:"Tree View",accelerator:"CmdOrCtrl+1",click:()=>e?.webContents.send("menu:view-tree")},{label:"Grid View",accelerator:"CmdOrCtrl+2",click:()=>e?.webContents.send("menu:view-grid")},{label:"List View",accelerator:"CmdOrCtrl+3",click:()=>e?.webContents.send("menu:view-list")},{type:"separator"},{label:"Show Preview Panel",accelerator:"CmdOrCtrl+P",click:()=>e?.webContents.send("menu:toggle-preview")},{label:"Show Properties Panel",accelerator:"CmdOrCtrl+Shift+P",click:()=>e?.webContents.send("menu:toggle-properties")},{label:"Show Console",accelerator:"CmdOrCtrl+Shift+C",click:()=>e?.webContents.send("menu:toggle-console")},{type:"separator"},{label:"Zoom In",accelerator:"CmdOrCtrl+Plus",click:()=>e?.webContents.send("menu:zoom-in")},{label:"Zoom Out",accelerator:"CmdOrCtrl+-",click:()=>e?.webContents.send("menu:zoom-out")},{label:"Reset Zoom",accelerator:"CmdOrCtrl+0",click:()=>e?.webContents.send("menu:zoom-reset")},{type:"separator"},{role:"reload"},{role:"forceReload"},{role:"toggleDevTools"},{type:"separator"},{role:"togglefullscreen"}]},{label:"Tools",submenu:[{label:"Route Slots",accelerator:"CmdOrCtrl+R",click:()=>e?.webContents.send("menu:route-slots")},{label:"Quality Assurance Scan",accelerator:"CmdOrCtrl+Q",click:()=>e?.webContents.send("menu:qa-scan")},{label:"Generate Thumbnails",accelerator:"CmdOrCtrl+T",click:()=>e?.webContents.send("menu:generate-thumbnails")},{label:"Optimize Assets",click:()=>e?.webContents.send("menu:optimize-assets")},{type:"separator"},{label:"Batch Operations",submenu:[{label:"Batch Import",click:()=>e?.webContents.send("menu:batch-import")},{label:"Batch Export",click:()=>e?.webContents.send("menu:batch-export")},{label:"Batch Rename",click:()=>e?.webContents.send("menu:batch-rename")}]},{type:"separator"},{label:"Open Blender",click:()=>e?.webContents.send("menu:open-blender")},{label:"Open Temp Folder",click:()=>{const e=t.get("tempDirectory");n.shell.openPath(e)}}]},{label:"Window",submenu:[{role:"minimize"},{role:"zoom"},...r?[{type:"separator"},{role:"front"},{type:"separator"},{role:"window"}]:[{role:"close"}]]},{label:"Help",submenu:[{label:"Getting Started",click:()=>n.shell.openExternal("https://dripforge.pro/docs/getting-started")},{label:"Documentation",click:()=>n.shell.openExternal("https://dripforge.pro/docs")},{label:"Video Tutorials",click:()=>n.shell.openExternal("https://dripforge.pro/tutorials")},{label:"Community Discord",click:()=>n.shell.openExternal("https://discord.gg/dripforge")},{type:"separator"},{label:"Report Bug",click:()=>n.shell.openExternal("https://github.com/dripforge/pro/issues")},{label:"Feature Request",click:()=>n.shell.openExternal("https://github.com/dripforge/pro/discussions")},{type:"separator"},{label:"Check for Updates",click:()=>e?.webContents.send("menu:check-updates")},...r?[]:[{type:"separator"},{label:"About DripForge Pro",click:()=>e?.webContents.send("menu:about")}]]}];return n.Menu.buildFromTemplate(s)}function v(e,t){const s=e.get("recentProjects",[]);if(0===s.length)return[{label:"No Recent Projects",enabled:!1}];const o=s.map((e,n)=>({label:`${n+1}. ${r(6928).basename(e)}`,accelerator:n<9?`CmdOrCtrl+${n+1}`:void 0,click:()=>t?.webContents.send("menu:open-recent",e)}));return o.push({type:"separator"},{label:"Clear Recent Projects",click:()=>{e.set("recentProjects",[]);const r=y(t,e);n.Menu.setApplicationMenu(r)}}),o}const w=new class{constructor(){this.mainWindow=null,this.isDev=!1,this.store=new(a())({defaults:{theme:"dark",language:"en",autoSave:!0,autoSaveInterval:3e5,maxRecentProjects:10,tempDirectory:i.join(n.app.getPath("temp"),"dripforge-pro"),logLevel:"info"}}),this.initializeApp()}async initializeApp(){var e;"win32"===process.platform&&n.app.setAppUserModelId("com.dripforge.pro"),n.app.whenReady().then(()=>{this.createMainWindow(),this.setupAppMenu(),this.initializeServices(),this.setupAutoUpdater(),n.app.on("activate",()=>{0===n.BrowserWindow.getAllWindows().length&&this.createMainWindow()})}),n.app.on("window-all-closed",()=>{"darwin"!==process.platform&&n.app.quit()}),n.app.on("before-quit",async e=>{if(this.mainWindow&&!this.mainWindow.isDestroyed()){e.preventDefault();const t=await this.showUnsavedChangesDialog();"save"!==t&&"discard"!==t||n.app.quit()}}),e=this.store,n.ipcMain.handle("fs:read-file",async(e,t)=>{try{return await c.readFile(t,"utf8")}catch(e){throw new Error(`Failed to read file: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("fs:write-file",async(e,t,r)=>{try{return await c.ensureDir(i.dirname(t)),await c.writeFile(t,r,"utf8"),!0}catch(e){throw new Error(`Failed to write file: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("fs:exists",async(e,t)=>c.pathExists(t)),n.ipcMain.handle("fs:read-dir",async(e,t)=>{try{return(await c.readdir(t,{withFileTypes:!0})).map(e=>({name:e.name,isDirectory:e.isDirectory(),isFile:e.isFile(),path:i.join(t,e.name)}))}catch(e){throw new Error(`Failed to read directory: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("dialog:open-file",async(e,t)=>await n.dialog.showOpenDialog(t)),n.ipcMain.handle("dialog:save-file",async(e,t)=>await n.dialog.showSaveDialog(t)),n.ipcMain.handle("dialog:show-message",async(e,t)=>await n.dialog.showMessageBox(t)),n.ipcMain.handle("dialog:show-error",async(e,t,r)=>{n.dialog.showErrorBox(t,r)}),n.ipcMain.handle("shell:open-external",async(e,t)=>{await n.shell.openExternal(t)}),n.ipcMain.handle("shell:show-item-in-folder",async(e,t)=>{n.shell.showItemInFolder(t)}),n.ipcMain.handle("shell:open-path",async(e,t)=>n.shell.openPath(t)),n.ipcMain.handle("settings:get",(t,r)=>r?e.get(r):e.store),n.ipcMain.handle("settings:set",(t,r,n)=>(e.set(r,n),!0)),n.ipcMain.handle("settings:reset",()=>(e.clear(),!0)),n.ipcMain.handle("project:create",async(e,t)=>{try{return{id:Math.random().toString(36).substr(2,9)+Date.now().toString(36),name:t.name||"Untitled Project",description:t.description||"",created:new Date,modified:new Date,items:[],slotMappings:[],settings:{reservedRanges:[],qaSettings:{enableClippingDetection:!0,clippingTolerance:.01,requireLODs:!0,maxTriangles:5e4,deepScanMode:!1},exportSettings:{includeQBClothing:!0,includeESX:!1,includeIcons:!0,includeMarketing:!1,escrowMode:!1,watermarkImages:!1},previewSettings:{defaultPose:"idle",renderQuality:"medium",enablePBR:!0,showWireframe:!1}},exportHistory:[]}}catch(e){throw new Error(`Failed to create project: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("project:save",async(t,r,s)=>{try{if(!s){const e=await n.dialog.showSaveDialog({title:"Save Project",defaultPath:`${r.name}.dfp`,filters:[{name:"DripForge Project",extensions:["dfp"]}]});if(e.canceled||!e.filePath)return null;s=e.filePath}r.modified=new Date,await c.writeFile(s,JSON.stringify(r,null,2));const t=e.get("recentProjects",[]),o=[s,...t.filter(e=>e!==s)].slice(0,e.get("maxRecentProjects",10));return e.set("recentProjects",o),s}catch(e){throw new Error(`Failed to save project: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("project:load",async(t,r)=>{try{if(!r){const e=await n.dialog.showOpenDialog({title:"Open Project",filters:[{name:"DripForge Project",extensions:["dfp"]}],properties:["openFile"]});if(e.canceled||!e.filePaths.length)return null;r=e.filePaths[0]}const t=await c.readFile(r,"utf8"),s=JSON.parse(t),o=e.get("recentProjects",[]),a=[r,...o.filter(e=>e!==r)].slice(0,e.get("maxRecentProjects",10));return e.set("recentProjects",a),{project:s,filePath:r}}catch(e){throw new Error(`Failed to load project: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("assets:ingest",async(e,t,r)=>{try{const e=new p;return await e.ingestAssets(t,r)}catch(e){throw new Error(`Failed to ingest assets: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("assets:validate",async(e,t)=>{try{const e=new f;return await e.validateAssets(t)}catch(e){throw new Error(`Failed to validate assets: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("slots:route",async(e,t,r)=>{try{const e=new h;return await e.routeSlots(t,r)}catch(e){throw new Error(`Failed to route slots: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("slots:check-conflicts",async(e,t)=>{try{const e=new h;return await e.checkConflicts(t)}catch(e){throw new Error(`Failed to check conflicts: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("export:generate",async(e,t,r)=>{try{const e=new g;return await e.exportProject(t,r)}catch(e){throw new Error(`Failed to export project: ${e instanceof Error?e.message:String(e)}`)}}),n.ipcMain.handle("window:minimize",()=>{const e=n.BrowserWindow.getFocusedWindow();e?.minimize()}),n.ipcMain.handle("window:maximize",()=>{const e=n.BrowserWindow.getFocusedWindow();e?.isMaximized()?e.unmaximize():e?.maximize()}),n.ipcMain.handle("window:close",()=>{const e=n.BrowserWindow.getFocusedWindow();e?.close()}),n.ipcMain.handle("app:get-version",()=>r(5949).rE),n.ipcMain.handle("app:get-platform",()=>process.platform),n.ipcMain.handle("app:restart",()=>{const{app:e}=r(4157);e.relaunch(),e.exit()})}createMainWindow(){const e=this.store.get("windowState",{width:1400,height:900,x:void 0,y:void 0,maximized:!1});this.mainWindow=new n.BrowserWindow({width:e.width,height:e.height,x:e.x,y:e.y,minWidth:1200,minHeight:800,show:!1,icon:this.getAppIcon(),titleBarStyle:"darwin"===process.platform?"hiddenInset":"default",webPreferences:{nodeIntegration:!1,contextIsolation:!0,preload:i.join(__dirname,"preload.js"),webSecurity:!this.isDev}}),this.isDev?(this.mainWindow.loadURL("http://localhost:4000"),this.mainWindow.webContents.openDevTools()):this.mainWindow.loadFile(i.join(__dirname,"../renderer/index.html")),this.mainWindow.once("ready-to-show",()=>{this.mainWindow&&(e.maximized&&this.mainWindow.maximize(),this.mainWindow.show(),this.isDev&&this.mainWindow.focus())}),this.mainWindow.on("close",e=>{if(this.mainWindow&&!this.mainWindow.isDestroyed()){const e=this.mainWindow.getBounds(),t=this.mainWindow.isMaximized();this.store.set("windowState",{...e,maximized:t})}}),this.mainWindow.on("closed",()=>{this.mainWindow=null}),this.mainWindow.webContents.setWindowOpenHandler(({url:e})=>(n.shell.openExternal(e),{action:"deny"}))}getAppIcon(){const e="win32"===process.platform?"icon.ico":"icon.png";return i.join(__dirname,"../assets/icons",e)}setupAppMenu(){const e=y(this.mainWindow,this.store);n.Menu.setApplicationMenu(e)}async initializeServices(){try{await async function(e){const t=e.get("tempDirectory");await c.ensureDir(t),await c.ensureDir(i.join(t,"imports")),await c.ensureDir(i.join(t,"exports")),await c.ensureDir(i.join(t,"thumbnails")),await c.ensureDir(i.join(t,"cache")),await c.ensureDir(i.join(t,"logs")),function(e){const t=e.get("logLevel","info"),r=e.get("tempDirectory"),n=i.join(r,"logs",`dripforge-${(new Date).toISOString().split("T")[0]}.log`),s={log:console.log,error:console.error,warn:console.warn,debug:console.debug},o=(e,...t)=>{const r=(new Date).toISOString(),s=t.map(e=>"object"==typeof e?JSON.stringify(e):String(e)).join(" "),o=`[${r}] [${e.toUpperCase()}] ${s}\n`;c.appendFile(n,o).catch(()=>{})};console.log=(...e)=>{s.log(...e),["debug","info","warn","error"].includes(t)&&o("info",...e)},console.error=(...e)=>{s.error(...e),["warn","error"].includes(t)&&o("error",...e)},console.warn=(...e)=>{s.warn(...e),["warn","error"].includes(t)&&o("warn",...e)},console.debug=(...e)=>{s.debug(...e),"debug"===t&&o("debug",...e)}}(e),await async function(e){try{const t=Date.now()-864e5,r=["imports","exports","thumbnails","cache"];for(const n of r){const r=i.join(e,n);if(await c.pathExists(r)){const e=await c.readdir(r);for(const n of e){const e=i.join(r,n);(await c.stat(e)).mtime.getTime()<t&&await c.remove(e)}}}}catch(e){console.warn("Failed to cleanup temp files:",e)}}(t),console.log("Services initialized successfully")}(this.store),console.log("Services initialized successfully")}catch(e){console.error("Failed to initialize services:",e),n.dialog.showErrorBox("Initialization Error","Failed to initialize application services. Some features may not work correctly.")}}setupAutoUpdater(){this.isDev||(s.autoUpdater.checkForUpdatesAndNotify(),s.autoUpdater.on("update-available",()=>{n.dialog.showMessageBox(this.mainWindow,{type:"info",title:"Update Available",message:"A new version of DripForge Pro is available. It will be downloaded in the background.",buttons:["OK"]})}),s.autoUpdater.on("update-downloaded",()=>{n.dialog.showMessageBox(this.mainWindow,{type:"info",title:"Update Ready",message:"Update downloaded. The application will restart to apply the update.",buttons:["Restart Now","Later"]}).then(e=>{0===e.response&&s.autoUpdater.quitAndInstall()})}))}async showUnsavedChangesDialog(){switch((await n.dialog.showMessageBox(this.mainWindow,{type:"warning",title:"Unsaved Changes",message:"You have unsaved changes. What would you like to do?",buttons:["Save","Discard","Cancel"],defaultId:0,cancelId:2})).response){case 0:return"save";case 1:return"discard";default:return"cancel"}}getMainWindow(){return this.mainWindow}getStore(){return this.store}};if(n.app.setAsDefaultProtocolClient("dripforge"),process.argv.length>=2){const e=process.argv[process.argv.length-1];e&&e.endsWith(".dfp")&&n.app.whenReady().then(()=>{w.getMainWindow()?.webContents.send("open-project",e)})}},3378:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DebUpdater=void 0;const n=r(9322),s=r(5776),o=r(3765);class a extends n.BaseUpdater{constructor(e,t){super(e,t)}doDownloadUpdate(e){const t=e.updateInfoAndProvider.provider,r=(0,s.findFile)(t.resolveFiles(e.updateInfoAndProvider.info),"deb",["AppImage","rpm","pacman"]);return this.executeDownload({fileExtension:"deb",fileInfo:r,downloadUpdateOptions:e,task:async(e,t)=>{this.listenerCount(o.DOWNLOAD_PROGRESS)>0&&(t.onProgress=e=>this.emit(o.DOWNLOAD_PROGRESS,e)),await this.httpExecutor.download(r.url,e,t)}})}get installerPath(){var e,t;return null!==(t=null===(e=super.installerPath)||void 0===e?void 0:e.replace(/ /g,"\\ "))&&void 0!==t?t:null}doInstall(e){const t=this.wrapSudo(),r=/pkexec/i.test(t)?"":'"',n=this.installerPath;if(null==n)return this.dispatchError(new Error("No valid update available, can't quit and install")),!1;const s=["dpkg","-i",n,"||","apt-get","install","-f","-y"];return this.spawnSyncLog(t,[`${r}/bin/bash`,"-c",`'${s.join(" ")}'${r}`]),e.isForceRunAfter&&this.app.relaunch(),!0}}t.DebUpdater=a},3411:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CancellationError=t.CancellationToken=void 0;const n=r(4434);class s extends n.EventEmitter{get cancelled(){return this._cancelled||null!=this._parent&&this._parent.cancelled}set parent(e){this.removeParentCancelHandler(),this._parent=e,this.parentCancelHandler=()=>this.cancel(),this._parent.onCancel(this.parentCancelHandler)}constructor(e){super(),this.parentCancelHandler=null,this._parent=null,this._cancelled=!1,null!=e&&(this.parent=e)}cancel(){this._cancelled=!0,this.emit("cancel")}onCancel(e){this.cancelled?e():this.once("cancel",e)}createPromise(e){if(this.cancelled)return Promise.reject(new o);const t=()=>{if(null!=r)try{this.removeListener("cancel",r),r=null}catch(e){}};let r=null;return new Promise((t,n)=>{let s=null;r=()=>{try{null!=s&&(s(),s=null)}finally{n(new o)}},this.cancelled?r():(this.onCancel(r),e(t,n,e=>{s=e}))}).then(e=>(t(),e)).catch(e=>{throw t(),e})}removeParentCancelHandler(){const e=this._parent;null!=e&&null!=this.parentCancelHandler&&(e.removeListener("cancel",this.parentCancelHandler),this.parentCancelHandler=null)}dispose(){try{this.removeParentCancelHandler()}finally{this.removeAllListeners(),this._parent=null}}}t.CancellationToken=s;class o extends Error{constructor(){super("cancelled")}}t.CancellationError=o},3423:(e,t,r)=>{"use strict";const n=r(6928),{app:s,ipcMain:o,ipcRenderer:a,shell:i}=r(4157),c=r(2888);let l=!1;const u=()=>{if(!o||!s)throw new Error("Electron Store: You need to call `.initRenderer()` from the main process.");const e={defaultCwd:s.getPath("userData"),appVersion:s.getVersion()};return l||(o.on("electron-store-get-data",t=>{t.returnValue=e}),l=!0),e};e.exports=class extends c{constructor(e){let t,r;if(a){const e=a.sendSync("electron-store-get-data");if(!e)throw new Error("Electron Store: You need to call `.initRenderer()` from the main process.");({defaultCwd:t,appVersion:r}=e)}else o&&s&&({defaultCwd:t,appVersion:r}=u());(e={name:"config",...e}).projectVersion||(e.projectVersion=r),e.cwd?e.cwd=n.isAbsolute(e.cwd)?e.cwd:n.join(t,e.cwd):e.cwd=t,e.configName=e.name,delete e.name,super(e)}static initRenderer(){u()}async openInEditor(){const e=await i.openPath(this.path);if(e)throw new Error(e)}}},3491:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>n.str`must match format "${e}"`,params:({schemaCode:e})=>n._`{format: ${e}}`},code(e,t){const{gen:r,data:s,$data:o,schema:a,schemaCode:i,it:c}=e,{opts:l,errSchemaPath:u,schemaEnv:d,self:p}=c;l.validateFormats&&(o?function(){const o=r.scopeValue("formats",{ref:p.formats,code:l.code.formats}),a=r.const("fDef",n._`${o}[${i}]`),c=r.let("fType"),u=r.let("format");r.if(n._`typeof ${a} == "object" && !(${a} instanceof RegExp)`,()=>r.assign(c,n._`${a}.type || "string"`).assign(u,n._`${a}.validate`),()=>r.assign(c,n._`"string"`).assign(u,a)),e.fail$data((0,n.or)(!1===l.strictSchema?n.nil:n._`${i} && !${u}`,function(){const e=d.$async?n._`(${a}.async ? await ${u}(${s}) : ${u}(${s}))`:n._`${u}(${s})`,r=n._`(typeof ${u} == "function" ? ${e} : ${u}.test(${s}))`;return n._`${u} && ${u} !== true && ${c} === ${t} && !${r}`}()))}():function(){const o=p.formats[a];if(!o)return void function(){if(!1!==l.strictSchema)throw new Error(e());function e(){return`unknown format "${a}" ignored in schema at path "${u}"`}p.logger.warn(e())}();if(!0===o)return;const[i,c,h]=function(e){const t=e instanceof RegExp?(0,n.regexpCode)(e):l.code.formats?n._`${l.code.formats}${(0,n.getProperty)(a)}`:void 0,s=r.scopeValue("formats",{key:a,ref:e,code:t});return"object"!=typeof e||e instanceof RegExp?["string",e,s]:[e.type||"string",e.validate,n._`${s}.validate`]}(o);i===t&&e.pass(function(){if("object"==typeof o&&!(o instanceof RegExp)&&o.async){if(!d.$async)throw new Error("async format in sync schema");return n._`await ${h}(${s})`}return"function"==typeof c?n._`${h}(${s})`:n._`${h}.test(${s})`}())}())}};t.default=s},3518:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5273),s={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const o=t.name("valid");r.forEach((t,r)=>{if((0,n.alwaysValidSchema)(s,t))return;const a=e.subschema({keyword:"allOf",schemaProp:r},o);e.ok(o),e.mergeEvaluated(a)})}};t.default=s},3619:(e,t)=>{"use strict";function r(e,t){if("object"==typeof e&&null!==e&&"object"==typeof t&&null!==t){const n=Object.keys(e),s=Object.keys(t);return n.length===s.length&&n.every(n=>r(e[n],t[n]))}return e===t}Object.defineProperty(t,"__esModule",{value:!0}),t.MemoLazy=void 0,t.MemoLazy=class{constructor(e,t){this.selector=e,this.creator=t,this.selected=void 0,this._value=void 0}get hasValue(){return void 0!==this._value}get value(){const e=this.selector();if(void 0!==this._value&&r(this.selected,e))return this._value;this.selected=e;const t=this.creator(e);return this.value=t,t}set value(e){this._value=e}}},3765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UpdaterSignal=t.UPDATE_DOWNLOADED=t.DOWNLOAD_PROGRESS=t.CancellationToken=void 0,t.addHandler=o;const n=r(6551);Object.defineProperty(t,"CancellationToken",{enumerable:!0,get:function(){return n.CancellationToken}}),t.DOWNLOAD_PROGRESS="download-progress",t.UPDATE_DOWNLOADED="update-downloaded",t.UpdaterSignal=class{constructor(e){this.emitter=e}login(e){o(this.emitter,"login",e)}progress(e){o(this.emitter,t.DOWNLOAD_PROGRESS,e)}updateDownloaded(e){o(this.emitter,t.UPDATE_DOWNLOADED,e)}updateCancelled(e){o(this.emitter,"update-cancelled",e)}};const s=!1;function o(e,t,r){s?e.on(t,(...e)=>{console.log("%s %s",t,e),r(...e)}):e.on(t,r)}},3767:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o=r(2482),a={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>n._`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:a,schemaCode:i,schema:c}=e;a||c&&"object"==typeof c?e.fail$data(n._`!${(0,s.useFunc)(t,o.default)}(${r}, ${i})`):e.fail(n._`${c} !== ${r}`)}};t.default=a},3781:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;const n=r(111),s=r(5273);t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:o,schemaPath:a,errSchemaPath:i,topSchemaRef:c}){if(void 0!==t&&void 0!==o)throw new Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){const o=e.schema[t];return void 0===r?{schema:o,schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:o[r],schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}${(0,n.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,s.escapeFragment)(r)}`}}if(void 0!==o){if(void 0===a||void 0===i||void 0===c)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:o,schemaPath:a,topSchemaRef:c,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:o,data:a,dataTypes:i,propertyName:c}){if(void 0!==a&&void 0!==r)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:l}=t;if(void 0!==r){const{errorPath:a,dataPathArr:i,opts:c}=t;u(l.let("data",n._`${t.data}${(0,n.getProperty)(r)}`,!0)),e.errorPath=n.str`${a}${(0,s.getErrorPath)(r,o,c.jsPropertySyntax)}`,e.parentDataProperty=n._`${r}`,e.dataPathArr=[...i,e.parentDataProperty]}function u(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}void 0!==a&&(u(a instanceof n.Name?a:l.let("data",a,!0)),void 0!==c&&(e.propertyName=c)),i&&(e.dataTypes=i)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:s,allErrors:o}){void 0!==n&&(e.compositeRule=n),void 0!==s&&(e.createErrors=s),void 0!==o&&(e.allErrors=o),e.jtdDiscriminator=t,e.jtdMetadata=r}},3789:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;const n=r(2104),s=r(5885);var o=r(2104);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return o._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return o.str}}),Object.defineProperty(t,"strConcat",{enumerable:!0,get:function(){return o.strConcat}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return o.nil}}),Object.defineProperty(t,"getProperty",{enumerable:!0,get:function(){return o.getProperty}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return o.stringify}}),Object.defineProperty(t,"regexpCode",{enumerable:!0,get:function(){return o.regexpCode}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return o.Name}});var a=r(5885);Object.defineProperty(t,"Scope",{enumerable:!0,get:function(){return a.Scope}}),Object.defineProperty(t,"ValueScope",{enumerable:!0,get:function(){return a.ValueScope}}),Object.defineProperty(t,"ValueScopeName",{enumerable:!0,get:function(){return a.ValueScopeName}}),Object.defineProperty(t,"varKinds",{enumerable:!0,get:function(){return a.varKinds}}),t.operators={GT:new n._Code(">"),GTE:new n._Code(">="),LT:new n._Code("<"),LTE:new n._Code("<="),EQ:new n._Code("==="),NEQ:new n._Code("!=="),NOT:new n._Code("!"),OR:new n._Code("||"),AND:new n._Code("&&"),ADD:new n._Code("+")};class i{optimizeNodes(){return this}optimizeNames(e,t){return this}}class c extends i{constructor(e,t,r){super(),this.varKind=e,this.name=t,this.rhs=r}render({es5:e,_n:t}){const r=e?s.varKinds.var:this.varKind,n=void 0===this.rhs?"":` = ${this.rhs}`;return`${r} ${this.name}${n};`+t}optimizeNames(e,t){if(e[this.name.str])return this.rhs&&(this.rhs=A(this.rhs,e,t)),this}get names(){return this.rhs instanceof n._CodeOrName?this.rhs.names:{}}}class l extends i{constructor(e,t,r){super(),this.lhs=e,this.rhs=t,this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(!(this.lhs instanceof n.Name)||e[this.lhs.str]||this.sideEffects)return this.rhs=A(this.rhs,e,t),this}get names(){return T(this.lhs instanceof n.Name?{}:{...this.lhs.names},this.rhs)}}class u extends l{constructor(e,t,r,n){super(e,r,n),this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class d extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`${this.label}:`+e}}class p extends i{constructor(e){super(),this.label=e,this.names={}}render({_n:e}){return`break${this.label?` ${this.label}`:""};`+e}}class h extends i{constructor(e){super(),this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class f extends i{constructor(e){super(),this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(e,t){return this.code=A(this.code,e,t),this}get names(){return this.code instanceof n._CodeOrName?this.code.names:{}}}class m extends i{constructor(e=[]){super(),this.nodes=e}render(e){return this.nodes.reduce((t,r)=>t+r.render(e),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;for(;t--;){const r=e[t].optimizeNodes();Array.isArray(r)?e.splice(t,1,...r):r?e[t]=r:e.splice(t,1)}return e.length>0?this:void 0}optimizeNames(e,t){const{nodes:r}=this;let n=r.length;for(;n--;){const s=r[n];s.optimizeNames(e,t)||(k(e,s.names),r.splice(n,1))}return r.length>0?this:void 0}get names(){return this.nodes.reduce((e,t)=>I(e,t.names),{})}}class g extends m{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class y extends m{}class v extends g{}v.kind="else";class w extends g{constructor(e,t){super(t),this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);return this.else&&(t+="else "+this.else.render(e)),t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(!0===e)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new v(e):e}return t?!1===e?t instanceof w?t:t.nodes:this.nodes.length?this:new w(R(e),t instanceof w?[t]:t.nodes):!1!==e&&this.nodes.length?this:void 0}optimizeNames(e,t){var r;if(this.else=null===(r=this.else)||void 0===r?void 0:r.optimizeNames(e,t),super.optimizeNames(e,t)||this.else)return this.condition=A(this.condition,e,t),this}get names(){const e=super.names;return T(e,this.condition),this.else&&I(e,this.else.names),e}}w.kind="if";class _ extends g{}_.kind="for";class b extends _{constructor(e){super(),this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iteration=A(this.iteration,e,t),this}get names(){return I(super.names,this.iteration.names)}}class $ extends _{constructor(e,t,r,n){super(),this.varKind=e,this.name=t,this.from=r,this.to=n}render(e){const t=e.es5?s.varKinds.var:this.varKind,{name:r,from:n,to:o}=this;return`for(${t} ${r}=${n}; ${r}<${o}; ${r}++)`+super.render(e)}get names(){const e=T(super.names,this.from);return T(e,this.to)}}class E extends _{constructor(e,t,r,n){super(),this.loop=e,this.varKind=t,this.name=r,this.iterable=n}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(super.optimizeNames(e,t))return this.iterable=A(this.iterable,e,t),this}get names(){return I(super.names,this.iterable.names)}}class S extends g{constructor(e,t,r){super(),this.name=e,this.args=t,this.async=r}render(e){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(e)}}S.kind="func";class P extends m{render(e){return"return "+super.render(e)}}P.kind="return";class O extends g{render(e){let t="try"+super.render(e);return this.catch&&(t+=this.catch.render(e)),this.finally&&(t+=this.finally.render(e)),t}optimizeNodes(){var e,t;return super.optimizeNodes(),null===(e=this.catch)||void 0===e||e.optimizeNodes(),null===(t=this.finally)||void 0===t||t.optimizeNodes(),this}optimizeNames(e,t){var r,n;return super.optimizeNames(e,t),null===(r=this.catch)||void 0===r||r.optimizeNames(e,t),null===(n=this.finally)||void 0===n||n.optimizeNames(e,t),this}get names(){const e=super.names;return this.catch&&I(e,this.catch.names),this.finally&&I(e,this.finally.names),e}}class C extends g{constructor(e){super(),this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}C.kind="catch";class N extends g{render(e){return"finally"+super.render(e)}}function I(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function T(e,t){return t instanceof n._CodeOrName?I(e,t.names):e}function A(e,t,r){return e instanceof n.Name?o(e):(s=e)instanceof n._Code&&s._items.some(e=>e instanceof n.Name&&1===t[e.str]&&void 0!==r[e.str])?new n._Code(e._items.reduce((e,t)=>(t instanceof n.Name&&(t=o(t)),t instanceof n._Code?e.push(...t._items):e.push(t),e),[])):e;var s;function o(e){const n=r[e.str];return void 0===n||1!==t[e.str]?e:(delete t[e.str],n)}}function k(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function R(e){return"boolean"==typeof e||"number"==typeof e||null===e?!e:n._`!${M(e)}`}N.kind="finally",t.CodeGen=class{constructor(e,t={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...t,_n:t.lines?"\n":""},this._extScope=e,this._scope=new s.Scope({parent:e}),this._nodes=[new y]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);return(this._values[r.prefix]||(this._values[r.prefix]=new Set)).add(r),r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,n){const s=this._scope.toName(t);return void 0!==r&&n&&(this._constants[s.str]=r),this._leafNode(new c(e,s,r)),s}const(e,t,r){return this._def(s.varKinds.const,e,t,r)}let(e,t,r){return this._def(s.varKinds.let,e,t,r)}var(e,t,r){return this._def(s.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new l(e,t,r))}add(e,r){return this._leafNode(new u(e,t.operators.ADD,r))}code(e){return"function"==typeof e?e():e!==n.nil&&this._leafNode(new f(e)),this}object(...e){const t=["{"];for(const[r,s]of e)t.length>1&&t.push(","),t.push(r),(r!==s||this.opts.es5)&&(t.push(":"),(0,n.addCodeArg)(t,s));return t.push("}"),new n._Code(t)}if(e,t,r){if(this._blockNode(new w(e)),t&&r)this.code(t).else().code(r).endIf();else if(t)this.code(t).endIf();else if(r)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(e){return this._elseNode(new w(e))}else(){return this._elseNode(new v)}endIf(){return this._endBlockNode(w,v)}_for(e,t){return this._blockNode(e),t&&this.code(t).endFor(),this}for(e,t){return this._for(new b(e),t)}forRange(e,t,r,n,o=(this.opts.es5?s.varKinds.var:s.varKinds.let)){const a=this._scope.toName(e);return this._for(new $(o,a,t,r),()=>n(a))}forOf(e,t,r,o=s.varKinds.const){const a=this._scope.toName(e);if(this.opts.es5){const e=t instanceof n.Name?t:this.var("_arr",t);return this.forRange("_i",0,n._`${e}.length`,t=>{this.var(a,n._`${e}[${t}]`),r(a)})}return this._for(new E("of",o,a,t),()=>r(a))}forIn(e,t,r,o=(this.opts.es5?s.varKinds.var:s.varKinds.const)){if(this.opts.ownProperties)return this.forOf(e,n._`Object.keys(${t})`,r);const a=this._scope.toName(e);return this._for(new E("in",o,a,t),()=>r(a))}endFor(){return this._endBlockNode(_)}label(e){return this._leafNode(new d(e))}break(e){return this._leafNode(new p(e))}return(e){const t=new P;if(this._blockNode(t),this.code(e),1!==t.nodes.length)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(P)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const n=new O;if(this._blockNode(n),this.code(e),t){const e=this.name("e");this._currNode=n.catch=new C(e),t(e)}return r&&(this._currNode=n.finally=new N,this.code(r)),this._endBlockNode(C,N)}throw(e){return this._leafNode(new h(e))}block(e,t){return this._blockStarts.push(this._nodes.length),e&&this.code(e).endBlock(t),this}endBlock(e){const t=this._blockStarts.pop();if(void 0===t)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||void 0!==e&&r!==e)throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`);return this._nodes.length=t,this}func(e,t=n.nil,r,s){return this._blockNode(new S(e,t,r)),s&&this.code(s).endFunc(),this}endFunc(){return this._endBlockNode(S)}optimize(e=1){for(;e-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(e){return this._currNode.nodes.push(e),this}_blockNode(e){this._currNode.nodes.push(e),this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof w))throw new Error('CodeGen: "else" without "if"');return this._currNode=t.else=e,this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}},t.not=R;const x=j(t.operators.AND);t.and=function(...e){return e.reduce(x)};const D=j(t.operators.OR);function j(e){return(t,r)=>t===n.nil?r:r===n.nil?t:n._`${M(t)} ${e} ${M(r)}`}function M(e){return e instanceof n.Name?e:n._`(${e})`}t.or=function(...e){return e.reduce(D)}},3874:(e,t,r)=>{"use strict";const n=r(8311);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}},3875:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o=r(2261),a={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxLength"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:a,it:i}=e,c="maxLength"===t?n.operators.GT:n.operators.LT,l=!1===i.opts.unicode?n._`${r}.length`:n._`${(0,s.useFunc)(e.gen,o.default)}(${r})`;e.fail$data(n._`${l} ${c} ${a}`)}};t.default=a},3904:(e,t,r)=>{"use strict";const n=Symbol("SemVer ANY");class s{static get ANY(){return n}constructor(e,t){if(t=o(t),e instanceof s){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),l("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,l("comp",this)}parse(e){const t=this.options.loose?a[i.COMPARATORLOOSE]:a[i.COMPARATOR],r=e.match(t);if(!r)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new u(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(l("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}return c(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new d(e.value,t).test(this.value):""===e.operator?""===e.value||new d(this.value,t).test(e.semver):!((t=o(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))||(!this.operator.startsWith(">")||!e.operator.startsWith(">"))&&(!this.operator.startsWith("<")||!e.operator.startsWith("<"))&&(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))&&!(c(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))&&!(c(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}}e.exports=s;const o=r(8587),{safeRe:a,t:i}=r(9718),c=r(2111),l=r(7272),u=r(3908),d=r(8311)},3908:(e,t,r)=>{"use strict";const n=r(7272),{MAX_LENGTH:s,MAX_SAFE_INTEGER:o}=r(6874),{safeRe:a,t:i}=r(9718),c=r(8587),{compareIdentifiers:l}=r(1123);class u{constructor(e,t){if(t=c(t),e instanceof u){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw new TypeError(`version is longer than ${s} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?a[i.LOOSE]:a[i.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>o||this.major<0)throw new TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<o)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof u)){if("string"==typeof e&&e===this.version)return 0;e=new u(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof u||(e=new u(e,this.options)),l(this.major,e.major)||l(this.minor,e.minor)||l(this.patch,e.patch)}comparePre(e){if(e instanceof u||(e=new u(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],s=e.prerelease[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return l(r,s)}while(++t)}compareBuild(e){e instanceof u||(e=new u(e,this.options));let t=0;do{const r=this.build[t],s=e.build[t];if(n("build compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return l(r,s)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw new Error("invalid increment argument: identifier is empty");if(t){const e=`-${t}`.match(this.options.loose?a[i.PRERELEASELOOSE]:a[i.PRERELEASE]);if(!e||e[1]!==t)throw new Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(r)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===l(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=u},3916:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RpmUpdater=void 0;const n=r(9322),s=r(3765),o=r(5776);class a extends n.BaseUpdater{constructor(e,t){super(e,t)}doDownloadUpdate(e){const t=e.updateInfoAndProvider.provider,r=(0,o.findFile)(t.resolveFiles(e.updateInfoAndProvider.info),"rpm",["AppImage","deb","pacman"]);return this.executeDownload({fileExtension:"rpm",fileInfo:r,downloadUpdateOptions:e,task:async(e,t)=>{this.listenerCount(s.DOWNLOAD_PROGRESS)>0&&(t.onProgress=e=>this.emit(s.DOWNLOAD_PROGRESS,e)),await this.httpExecutor.download(r.url,e,t)}})}get installerPath(){var e,t;return null!==(t=null===(e=super.installerPath)||void 0===e?void 0:e.replace(/ /g,"\\ "))&&void 0!==t?t:null}doInstall(e){const t=this.wrapSudo(),r=/pkexec/i.test(t)?"":'"',n=this.spawnSyncLog("which zypper"),s=this.installerPath;if(null==s)return this.dispatchError(new Error("No valid update available, can't quit and install")),!1;let o;return o=n?[n,"--no-refresh","install","--allow-unsigned-rpm","-y","-f",s]:[this.spawnSyncLog("which dnf || which yum"),"-y","install",s],this.spawnSyncLog(t,[`${r}/bin/bash`,"-c",`'${o.join(" ")}'${r}`]),e.isForceRunAfter&&this.app.relaunch(),!0}}t.RpmUpdater=a},3927:(e,t,r)=>{"use strict";const n=r(909);e.exports=(e,t)=>e.sort((e,r)=>n(e,r,t))},3929:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},3999:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t,r)=>0!==n(e,t,r)},4018:(e,t)=>{"use strict";function r(e,t){return{validate:e,compare:t}}Object.defineProperty(t,"__esModule",{value:!0}),t.formatNames=t.fastFormats=t.fullFormats=void 0,t.fullFormats={date:r(o,a),time:r(c,l),"date-time":r(function(e){const t=e.split(u);return 2===t.length&&o(t[0])&&c(t[1],!0)},d),duration:/^P(?!$)((\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?|(\d+W)?)$/,uri:function(e){return p.test(e)&&h.test(e)},"uri-reference":/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,"uri-template":/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,url:/^(?:https?|ftp):\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)(?:\.(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i,regex:function(e){if(v.test(e))return!1;try{return new RegExp(e),!0}catch(e){return!1}},uuid:/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,"json-pointer":/^(?:\/(?:[^~/]|~0|~1)*)*$/,"json-pointer-uri-fragment":/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,"relative-json-pointer":/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,byte:function(e){return f.lastIndex=0,f.test(e)},int32:{type:"number",validate:function(e){return Number.isInteger(e)&&e<=g&&e>=m}},int64:{type:"number",validate:function(e){return Number.isInteger(e)}},float:{type:"number",validate:y},double:{type:"number",validate:y},password:!0,binary:!0},t.fastFormats={...t.fullFormats,date:r(/^\d\d\d\d-[0-1]\d-[0-3]\d$/,a),time:r(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,l),"date-time":r(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,d),uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i},t.formatNames=Object.keys(t.fullFormats);const n=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,s=[0,31,28,31,30,31,30,31,31,30,31,30,31];function o(e){const t=n.exec(e);if(!t)return!1;const r=+t[1],o=+t[2],a=+t[3];return o>=1&&o<=12&&a>=1&&a<=(2===o&&function(e){return e%4==0&&(e%100!=0||e%400==0)}(r)?29:s[o])}function a(e,t){if(e&&t)return e>t?1:e<t?-1:0}const i=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function c(e,t){const r=i.exec(e);if(!r)return!1;const n=+r[1],s=+r[2],o=+r[3],a=r[5];return(n<=23&&s<=59&&o<=59||23===n&&59===s&&60===o)&&(!t||""!==a)}function l(e,t){if(!e||!t)return;const r=i.exec(e),n=i.exec(t);return r&&n?(e=r[1]+r[2]+r[3]+(r[4]||""))>(t=n[1]+n[2]+n[3]+(n[4]||""))?1:e<t?-1:0:void 0}const u=/t|\s/i;function d(e,t){if(!e||!t)return;const[r,n]=e.split(u),[s,o]=t.split(u),i=a(r,s);return void 0!==i?i||l(n,o):void 0}const p=/\/|:/,h=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,f=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm,m=-(2**31),g=2**31-1;function y(){return!0}const v=/[^\\]\\Z/},4043:(e,t,r)=>{!function(e){e.parser=function(e,t){return new s(e,t)},e.SAXParser=s,e.SAXStream=a,e.createStream=function(e,t){return new a(e,t)},e.MAX_BUFFER_LENGTH=65536;var t,n=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];function s(t,r){if(!(this instanceof s))return new s(t,r);var o=this;!function(e){for(var t=0,r=n.length;t<r;t++)e[n[t]]=""}(o),o.q=o.c="",o.bufferCheckPosition=e.MAX_BUFFER_LENGTH,o.opt=r||{},o.opt.lowercase=o.opt.lowercase||o.opt.lowercasetags,o.looseCase=o.opt.lowercase?"toLowerCase":"toUpperCase",o.tags=[],o.closed=o.closedRoot=o.sawRoot=!1,o.tag=o.error=null,o.strict=!!t,o.noscript=!(!t&&!o.opt.noscript),o.state=S.BEGIN,o.strictEntities=o.opt.strictEntities,o.ENTITIES=o.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),o.attribList=[],o.opt.xmlns&&(o.ns=Object.create(d)),void 0===o.opt.unquotedAttributeValues&&(o.opt.unquotedAttributeValues=!t),o.trackPosition=!1!==o.opt.position,o.trackPosition&&(o.position=o.line=o.column=0),O(o,"onready")}e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(e){function t(){}return t.prototype=e,new t}),Object.keys||(Object.keys=function(e){var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}),s.prototype={end:function(){A(this)},write:function(t){var r=this;if(this.error)throw this.error;if(r.closed)return T(r,"Cannot write after close. Assign an onready handler.");if(null===t)return A(r);"object"==typeof t&&(t=t.toString());for(var s=0,o="";o=L(t,s++),r.c=o,o;)switch(r.trackPosition&&(r.position++,"\n"===o?(r.line++,r.column=0):r.column++),r.state){case S.BEGIN:if(r.state=S.BEGIN_WHITESPACE,"\ufeff"===o)continue;F(r,o);continue;case S.BEGIN_WHITESPACE:F(r,o);continue;case S.TEXT:if(r.sawRoot&&!r.closedRoot){for(var a=s-1;o&&"<"!==o&&"&"!==o;)(o=L(t,s++))&&r.trackPosition&&(r.position++,"\n"===o?(r.line++,r.column=0):r.column++);r.textNode+=t.substring(a,s-1)}"<"!==o||r.sawRoot&&r.closedRoot&&!r.strict?(g(o)||r.sawRoot&&!r.closedRoot||k(r,"Text data outside of root node."),"&"===o?r.state=S.TEXT_ENTITY:r.textNode+=o):(r.state=S.OPEN_WAKA,r.startTagPosition=r.position);continue;case S.SCRIPT:"<"===o?r.state=S.SCRIPT_ENDING:r.script+=o;continue;case S.SCRIPT_ENDING:"/"===o?r.state=S.CLOSE_TAG:(r.script+="<"+o,r.state=S.SCRIPT);continue;case S.OPEN_WAKA:if("!"===o)r.state=S.SGML_DECL,r.sgmlDecl="";else if(g(o));else if(w(p,o))r.state=S.OPEN_TAG,r.tagName=o;else if("/"===o)r.state=S.CLOSE_TAG,r.tagName="";else if("?"===o)r.state=S.PROC_INST,r.procInstName=r.procInstBody="";else{if(k(r,"Unencoded <"),r.startTagPosition+1<r.position){var l=r.position-r.startTagPosition;o=new Array(l).join(" ")+o}r.textNode+="<"+o,r.state=S.TEXT}continue;case S.SGML_DECL:if(r.sgmlDecl+o==="--"){r.state=S.COMMENT,r.comment="",r.sgmlDecl="";continue}r.doctype&&!0!==r.doctype&&r.sgmlDecl?(r.state=S.DOCTYPE_DTD,r.doctype+="<!"+r.sgmlDecl+o,r.sgmlDecl=""):(r.sgmlDecl+o).toUpperCase()===i?(C(r,"onopencdata"),r.state=S.CDATA,r.sgmlDecl="",r.cdata=""):(r.sgmlDecl+o).toUpperCase()===c?(r.state=S.DOCTYPE,(r.doctype||r.sawRoot)&&k(r,"Inappropriately located doctype declaration"),r.doctype="",r.sgmlDecl=""):">"===o?(C(r,"onsgmldeclaration",r.sgmlDecl),r.sgmlDecl="",r.state=S.TEXT):y(o)?(r.state=S.SGML_DECL_QUOTED,r.sgmlDecl+=o):r.sgmlDecl+=o;continue;case S.SGML_DECL_QUOTED:o===r.q&&(r.state=S.SGML_DECL,r.q=""),r.sgmlDecl+=o;continue;case S.DOCTYPE:">"===o?(r.state=S.TEXT,C(r,"ondoctype",r.doctype),r.doctype=!0):(r.doctype+=o,"["===o?r.state=S.DOCTYPE_DTD:y(o)&&(r.state=S.DOCTYPE_QUOTED,r.q=o));continue;case S.DOCTYPE_QUOTED:r.doctype+=o,o===r.q&&(r.q="",r.state=S.DOCTYPE);continue;case S.DOCTYPE_DTD:"]"===o?(r.doctype+=o,r.state=S.DOCTYPE):"<"===o?(r.state=S.OPEN_WAKA,r.startTagPosition=r.position):y(o)?(r.doctype+=o,r.state=S.DOCTYPE_DTD_QUOTED,r.q=o):r.doctype+=o;continue;case S.DOCTYPE_DTD_QUOTED:r.doctype+=o,o===r.q&&(r.state=S.DOCTYPE_DTD,r.q="");continue;case S.COMMENT:"-"===o?r.state=S.COMMENT_ENDING:r.comment+=o;continue;case S.COMMENT_ENDING:"-"===o?(r.state=S.COMMENT_ENDED,r.comment=I(r.opt,r.comment),r.comment&&C(r,"oncomment",r.comment),r.comment=""):(r.comment+="-"+o,r.state=S.COMMENT);continue;case S.COMMENT_ENDED:">"!==o?(k(r,"Malformed comment"),r.comment+="--"+o,r.state=S.COMMENT):r.doctype&&!0!==r.doctype?r.state=S.DOCTYPE_DTD:r.state=S.TEXT;continue;case S.CDATA:"]"===o?r.state=S.CDATA_ENDING:r.cdata+=o;continue;case S.CDATA_ENDING:"]"===o?r.state=S.CDATA_ENDING_2:(r.cdata+="]"+o,r.state=S.CDATA);continue;case S.CDATA_ENDING_2:">"===o?(r.cdata&&C(r,"oncdata",r.cdata),C(r,"onclosecdata"),r.cdata="",r.state=S.TEXT):"]"===o?r.cdata+="]":(r.cdata+="]]"+o,r.state=S.CDATA);continue;case S.PROC_INST:"?"===o?r.state=S.PROC_INST_ENDING:g(o)?r.state=S.PROC_INST_BODY:r.procInstName+=o;continue;case S.PROC_INST_BODY:if(!r.procInstBody&&g(o))continue;"?"===o?r.state=S.PROC_INST_ENDING:r.procInstBody+=o;continue;case S.PROC_INST_ENDING:">"===o?(C(r,"onprocessinginstruction",{name:r.procInstName,body:r.procInstBody}),r.procInstName=r.procInstBody="",r.state=S.TEXT):(r.procInstBody+="?"+o,r.state=S.PROC_INST_BODY);continue;case S.OPEN_TAG:w(h,o)?r.tagName+=o:(R(r),">"===o?j(r):"/"===o?r.state=S.OPEN_TAG_SLASH:(g(o)||k(r,"Invalid character in tag name"),r.state=S.ATTRIB));continue;case S.OPEN_TAG_SLASH:">"===o?(j(r,!0),M(r)):(k(r,"Forward-slash in opening tag not followed by >"),r.state=S.ATTRIB);continue;case S.ATTRIB:if(g(o))continue;">"===o?j(r):"/"===o?r.state=S.OPEN_TAG_SLASH:w(p,o)?(r.attribName=o,r.attribValue="",r.state=S.ATTRIB_NAME):k(r,"Invalid attribute name");continue;case S.ATTRIB_NAME:"="===o?r.state=S.ATTRIB_VALUE:">"===o?(k(r,"Attribute without value"),r.attribValue=r.attribName,D(r),j(r)):g(o)?r.state=S.ATTRIB_NAME_SAW_WHITE:w(h,o)?r.attribName+=o:k(r,"Invalid attribute name");continue;case S.ATTRIB_NAME_SAW_WHITE:if("="===o)r.state=S.ATTRIB_VALUE;else{if(g(o))continue;k(r,"Attribute without value"),r.tag.attributes[r.attribName]="",r.attribValue="",C(r,"onattribute",{name:r.attribName,value:""}),r.attribName="",">"===o?j(r):w(p,o)?(r.attribName=o,r.state=S.ATTRIB_NAME):(k(r,"Invalid attribute name"),r.state=S.ATTRIB)}continue;case S.ATTRIB_VALUE:if(g(o))continue;y(o)?(r.q=o,r.state=S.ATTRIB_VALUE_QUOTED):(r.opt.unquotedAttributeValues||T(r,"Unquoted attribute value"),r.state=S.ATTRIB_VALUE_UNQUOTED,r.attribValue=o);continue;case S.ATTRIB_VALUE_QUOTED:if(o!==r.q){"&"===o?r.state=S.ATTRIB_VALUE_ENTITY_Q:r.attribValue+=o;continue}D(r),r.q="",r.state=S.ATTRIB_VALUE_CLOSED;continue;case S.ATTRIB_VALUE_CLOSED:g(o)?r.state=S.ATTRIB:">"===o?j(r):"/"===o?r.state=S.OPEN_TAG_SLASH:w(p,o)?(k(r,"No whitespace between attributes"),r.attribName=o,r.attribValue="",r.state=S.ATTRIB_NAME):k(r,"Invalid attribute name");continue;case S.ATTRIB_VALUE_UNQUOTED:if(!v(o)){"&"===o?r.state=S.ATTRIB_VALUE_ENTITY_U:r.attribValue+=o;continue}D(r),">"===o?j(r):r.state=S.ATTRIB;continue;case S.CLOSE_TAG:if(r.tagName)">"===o?M(r):w(h,o)?r.tagName+=o:r.script?(r.script+="</"+r.tagName,r.tagName="",r.state=S.SCRIPT):(g(o)||k(r,"Invalid tagname in closing tag"),r.state=S.CLOSE_TAG_SAW_WHITE);else{if(g(o))continue;_(p,o)?r.script?(r.script+="</"+o,r.state=S.SCRIPT):k(r,"Invalid tagname in closing tag."):r.tagName=o}continue;case S.CLOSE_TAG_SAW_WHITE:if(g(o))continue;">"===o?M(r):k(r,"Invalid characters in closing tag");continue;case S.TEXT_ENTITY:case S.ATTRIB_VALUE_ENTITY_Q:case S.ATTRIB_VALUE_ENTITY_U:var u,d;switch(r.state){case S.TEXT_ENTITY:u=S.TEXT,d="textNode";break;case S.ATTRIB_VALUE_ENTITY_Q:u=S.ATTRIB_VALUE_QUOTED,d="attribValue";break;case S.ATTRIB_VALUE_ENTITY_U:u=S.ATTRIB_VALUE_UNQUOTED,d="attribValue"}if(";"===o){var b=U(r);r.opt.unparsedEntities&&!Object.values(e.XML_ENTITIES).includes(b)?(r.entity="",r.state=u,r.write(b)):(r[d]+=b,r.entity="",r.state=u)}else w(r.entity.length?m:f,o)?r.entity+=o:(k(r,"Invalid character in entity name"),r[d]+="&"+r.entity+o,r.entity="",r.state=u);continue;default:throw new Error(r,"Unknown state: "+r.state)}return r.position>=r.bufferCheckPosition&&function(t){for(var r=Math.max(e.MAX_BUFFER_LENGTH,10),s=0,o=0,a=n.length;o<a;o++){var i=t[n[o]].length;if(i>r)switch(n[o]){case"textNode":N(t);break;case"cdata":C(t,"oncdata",t.cdata),t.cdata="";break;case"script":C(t,"onscript",t.script),t.script="";break;default:T(t,"Max buffer length exceeded: "+n[o])}s=Math.max(s,i)}var c=e.MAX_BUFFER_LENGTH-s;t.bufferCheckPosition=c+t.position}(r),r},resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){var e;N(e=this),""!==e.cdata&&(C(e,"oncdata",e.cdata),e.cdata=""),""!==e.script&&(C(e,"onscript",e.script),e.script="")}};try{t=r(2203).Stream}catch(e){t=function(){}}t||(t=function(){});var o=e.EVENTS.filter(function(e){return"error"!==e&&"end"!==e});function a(e,r){if(!(this instanceof a))return new a(e,r);t.apply(this),this._parser=new s(e,r),this.writable=!0,this.readable=!0;var n=this;this._parser.onend=function(){n.emit("end")},this._parser.onerror=function(e){n.emit("error",e),n._parser.error=null},this._decoder=null,o.forEach(function(e){Object.defineProperty(n,"on"+e,{get:function(){return n._parser["on"+e]},set:function(t){if(!t)return n.removeAllListeners(e),n._parser["on"+e]=t,t;n.on(e,t)},enumerable:!0,configurable:!1})})}a.prototype=Object.create(t.prototype,{constructor:{value:a}}),a.prototype.write=function(e){if("function"==typeof Buffer&&"function"==typeof Buffer.isBuffer&&Buffer.isBuffer(e)){if(!this._decoder){var t=r(3193).StringDecoder;this._decoder=new t("utf8")}e=this._decoder.write(e)}return this._parser.write(e.toString()),this.emit("data",e),!0},a.prototype.end=function(e){return e&&e.length&&this.write(e),this._parser.end(),!0},a.prototype.on=function(e,r){var n=this;return n._parser["on"+e]||-1===o.indexOf(e)||(n._parser["on"+e]=function(){var t=1===arguments.length?[arguments[0]]:Array.apply(null,arguments);t.splice(0,0,e),n.emit.apply(n,t)}),t.prototype.on.call(n,e,r)};var i="[CDATA[",c="DOCTYPE",l="http://www.w3.org/XML/1998/namespace",u="http://www.w3.org/2000/xmlns/",d={xml:l,xmlns:u},p=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,h=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,f=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,m=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function g(e){return" "===e||"\n"===e||"\r"===e||"\t"===e}function y(e){return'"'===e||"'"===e}function v(e){return">"===e||g(e)}function w(e,t){return e.test(t)}function _(e,t){return!w(e,t)}var b,$,E,S=0;for(var P in e.STATE={BEGIN:S++,BEGIN_WHITESPACE:S++,TEXT:S++,TEXT_ENTITY:S++,OPEN_WAKA:S++,SGML_DECL:S++,SGML_DECL_QUOTED:S++,DOCTYPE:S++,DOCTYPE_QUOTED:S++,DOCTYPE_DTD:S++,DOCTYPE_DTD_QUOTED:S++,COMMENT_STARTING:S++,COMMENT:S++,COMMENT_ENDING:S++,COMMENT_ENDED:S++,CDATA:S++,CDATA_ENDING:S++,CDATA_ENDING_2:S++,PROC_INST:S++,PROC_INST_BODY:S++,PROC_INST_ENDING:S++,OPEN_TAG:S++,OPEN_TAG_SLASH:S++,ATTRIB:S++,ATTRIB_NAME:S++,ATTRIB_NAME_SAW_WHITE:S++,ATTRIB_VALUE:S++,ATTRIB_VALUE_QUOTED:S++,ATTRIB_VALUE_CLOSED:S++,ATTRIB_VALUE_UNQUOTED:S++,ATTRIB_VALUE_ENTITY_Q:S++,ATTRIB_VALUE_ENTITY_U:S++,CLOSE_TAG:S++,CLOSE_TAG_SAW_WHITE:S++,SCRIPT:S++,SCRIPT_ENDING:S++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach(function(t){var r=e.ENTITIES[t],n="number"==typeof r?String.fromCharCode(r):r;e.ENTITIES[t]=n}),e.STATE)e.STATE[e.STATE[P]]=P;function O(e,t,r){e[t]&&e[t](r)}function C(e,t,r){e.textNode&&N(e),O(e,t,r)}function N(e){e.textNode=I(e.opt,e.textNode),e.textNode&&O(e,"ontext",e.textNode),e.textNode=""}function I(e,t){return e.trim&&(t=t.trim()),e.normalize&&(t=t.replace(/\s+/g," ")),t}function T(e,t){return N(e),e.trackPosition&&(t+="\nLine: "+e.line+"\nColumn: "+e.column+"\nChar: "+e.c),t=new Error(t),e.error=t,O(e,"onerror",t),e}function A(e){return e.sawRoot&&!e.closedRoot&&k(e,"Unclosed root tag"),e.state!==S.BEGIN&&e.state!==S.BEGIN_WHITESPACE&&e.state!==S.TEXT&&T(e,"Unexpected end"),N(e),e.c="",e.closed=!0,O(e,"onend"),s.call(e,e.strict,e.opt),e}function k(e,t){if("object"!=typeof e||!(e instanceof s))throw new Error("bad call to strictFail");e.strict&&T(e,t)}function R(e){e.strict||(e.tagName=e.tagName[e.looseCase]());var t=e.tags[e.tags.length-1]||e,r=e.tag={name:e.tagName,attributes:{}};e.opt.xmlns&&(r.ns=t.ns),e.attribList.length=0,C(e,"onopentagstart",r)}function x(e,t){var r=e.indexOf(":")<0?["",e]:e.split(":"),n=r[0],s=r[1];return t&&"xmlns"===e&&(n="xmlns",s=""),{prefix:n,local:s}}function D(e){if(e.strict||(e.attribName=e.attribName[e.looseCase]()),-1!==e.attribList.indexOf(e.attribName)||e.tag.attributes.hasOwnProperty(e.attribName))e.attribName=e.attribValue="";else{if(e.opt.xmlns){var t=x(e.attribName,!0),r=t.prefix,n=t.local;if("xmlns"===r)if("xml"===n&&e.attribValue!==l)k(e,"xml: prefix must be bound to "+l+"\nActual: "+e.attribValue);else if("xmlns"===n&&e.attribValue!==u)k(e,"xmlns: prefix must be bound to "+u+"\nActual: "+e.attribValue);else{var s=e.tag,o=e.tags[e.tags.length-1]||e;s.ns===o.ns&&(s.ns=Object.create(o.ns)),s.ns[n]=e.attribValue}e.attribList.push([e.attribName,e.attribValue])}else e.tag.attributes[e.attribName]=e.attribValue,C(e,"onattribute",{name:e.attribName,value:e.attribValue});e.attribName=e.attribValue=""}}function j(e,t){if(e.opt.xmlns){var r=e.tag,n=x(e.tagName);r.prefix=n.prefix,r.local=n.local,r.uri=r.ns[n.prefix]||"",r.prefix&&!r.uri&&(k(e,"Unbound namespace prefix: "+JSON.stringify(e.tagName)),r.uri=n.prefix);var s=e.tags[e.tags.length-1]||e;r.ns&&s.ns!==r.ns&&Object.keys(r.ns).forEach(function(t){C(e,"onopennamespace",{prefix:t,uri:r.ns[t]})});for(var o=0,a=e.attribList.length;o<a;o++){var i=e.attribList[o],c=i[0],l=i[1],u=x(c,!0),d=u.prefix,p=u.local,h=""===d?"":r.ns[d]||"",f={name:c,value:l,prefix:d,local:p,uri:h};d&&"xmlns"!==d&&!h&&(k(e,"Unbound namespace prefix: "+JSON.stringify(d)),f.uri=d),e.tag.attributes[c]=f,C(e,"onattribute",f)}e.attribList.length=0}e.tag.isSelfClosing=!!t,e.sawRoot=!0,e.tags.push(e.tag),C(e,"onopentag",e.tag),t||(e.noscript||"script"!==e.tagName.toLowerCase()?e.state=S.TEXT:e.state=S.SCRIPT,e.tag=null,e.tagName=""),e.attribName=e.attribValue="",e.attribList.length=0}function M(e){if(!e.tagName)return k(e,"Weird empty close tag."),e.textNode+="</>",void(e.state=S.TEXT);if(e.script){if("script"!==e.tagName)return e.script+="</"+e.tagName+">",e.tagName="",void(e.state=S.SCRIPT);C(e,"onscript",e.script),e.script=""}var t=e.tags.length,r=e.tagName;e.strict||(r=r[e.looseCase]());for(var n=r;t--&&e.tags[t].name!==n;)k(e,"Unexpected close tag");if(t<0)return k(e,"Unmatched closing tag: "+e.tagName),e.textNode+="</"+e.tagName+">",void(e.state=S.TEXT);e.tagName=r;for(var s=e.tags.length;s-- >t;){var o=e.tag=e.tags.pop();e.tagName=e.tag.name,C(e,"onclosetag",e.tagName);var a={};for(var i in o.ns)a[i]=o.ns[i];var c=e.tags[e.tags.length-1]||e;e.opt.xmlns&&o.ns!==c.ns&&Object.keys(o.ns).forEach(function(t){var r=o.ns[t];C(e,"onclosenamespace",{prefix:t,uri:r})})}0===t&&(e.closedRoot=!0),e.tagName=e.attribValue=e.attribName="",e.attribList.length=0,e.state=S.TEXT}function U(e){var t,r=e.entity,n=r.toLowerCase(),s="";return e.ENTITIES[r]?e.ENTITIES[r]:e.ENTITIES[n]?e.ENTITIES[n]:("#"===(r=n).charAt(0)&&("x"===r.charAt(1)?(r=r.slice(2),s=(t=parseInt(r,16)).toString(16)):(r=r.slice(1),s=(t=parseInt(r,10)).toString(10))),r=r.replace(/^0+/,""),isNaN(t)||s.toLowerCase()!==r?(k(e,"Invalid character entity"),"&"+e.entity+";"):String.fromCodePoint(t))}function F(e,t){"<"===t?(e.state=S.OPEN_WAKA,e.startTagPosition=e.position):g(t)||(k(e,"Non-whitespace before first tag."),e.textNode=t,e.state=S.TEXT)}function L(e,t){var r="";return t<e.length&&(r=e.charAt(t)),r}S=e.STATE,String.fromCodePoint||(b=String.fromCharCode,$=Math.floor,E=function(){var e,t,r=[],n=-1,s=arguments.length;if(!s)return"";for(var o="";++n<s;){var a=Number(arguments[n]);if(!isFinite(a)||a<0||a>1114111||$(a)!==a)throw RangeError("Invalid code point: "+a);a<=65535?r.push(a):(e=55296+((a-=65536)>>10),t=a%1024+56320,r.push(e,t)),(n+1===s||r.length>16384)&&(o+=b.apply(null,r),r.length=0)}return o},Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:E,configurable:!0,writable:!0}):String.fromCodePoint=E)}(t)},4051:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(8452),s=r(1039),o=r(5273),a=r(8330),i={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:i,data:c,it:l}=e;"all"===l.opts.removeAdditional&&void 0===i.additionalProperties&&a.default.code(new n.KeywordCxt(l,a.default,"additionalProperties"));const u=(0,s.allSchemaProperties)(r);for(const e of u)l.definedProperties.add(e);l.opts.unevaluated&&u.length&&!0!==l.props&&(l.props=o.mergeEvaluated.props(t,(0,o.toHash)(u),l.props));const d=u.filter(e=>!(0,o.alwaysValidSchema)(l,r[e]));if(0===d.length)return;const p=t.name("valid");for(const r of d)h(r)?f(r):(t.if((0,s.propertyInData)(t,c,r,l.opts.ownProperties)),f(r),l.allErrors||t.else().var(p,!0),t.endIf()),e.it.definedProperties.add(r),e.ok(p);function h(e){return l.opts.useDefaults&&!l.compositeRule&&void 0!==r[e].default}function f(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},p)}}};t.default=i},4063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>n._`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:o,it:a}=e;if((0,s.alwaysValidSchema)(a,r))return;const i=t.name("valid");t.forIn("key",o,r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},i),t.if((0,n.not)(i),()=>{e.error(!0),a.allErrors||t.break()})}),e.ok(i)}};t.default=o},4089:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t,r)=>n(e,t,r)>=0},4157:e=>{"use strict";e.exports=require("electron")},4169:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n={interval:25,intervalId:void 0,limit:r(4763).LIMIT_FILES_DESCRIPTORS,queueActive:new Set,queueWaiting:new Set,init:()=>{n.intervalId||(n.intervalId=setInterval(n.tick,n.interval))},reset:()=>{n.intervalId&&(clearInterval(n.intervalId),delete n.intervalId)},add:e=>{n.queueWaiting.add(e),n.queueActive.size<n.limit/2?n.tick():n.init()},remove:e=>{n.queueWaiting.delete(e),n.queueActive.delete(e)},schedule:()=>new Promise(e=>{const t=()=>n.remove(r),r=()=>e(t);n.add(r)}),tick:()=>{if(!(n.queueActive.size>=n.limit)){if(!n.queueWaiting.size)return n.reset();for(const e of n.queueWaiting){if(n.queueActive.size>=n.limit)break;n.queueWaiting.delete(e),n.queueActive.add(e),e()}}}};t.default=n},4261:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.newError=function(e,t){const r=new Error(e);return r.code=t,r}},4267:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1085),s=r(3789),o=r(7083),a={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>s.str`must have required property '${e}'`,params:({params:{missingProperty:e}})=>s._`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:a,data:i,$data:c,it:l}=e,{opts:u}=l;if(!c&&0===r.length)return;const d=r.length>=u.loopRequired;if(l.allErrors?function(){if(d||c)e.block$data(s.nil,p);else for(const t of r)(0,n.checkReportMissingProp)(e,t)}():function(){const o=t.let("missing");if(d||c){const r=t.let("valid",!0);e.block$data(r,()=>function(r,o){e.setParams({missingProperty:r}),t.forOf(r,a,()=>{t.assign(o,(0,n.propertyInData)(t,i,r,u.ownProperties)),t.if((0,s.not)(o),()=>{e.error(),t.break()})},s.nil)}(o,r)),e.ok(r)}else t.if((0,n.checkMissingProp)(e,r,o)),(0,n.reportMissingProp)(e,o),t.else()}(),u.strictRequired){const t=e.parentSchema.properties,{definedProperties:n}=e.it;for(const e of r)if(void 0===(null==t?void 0:t[e])&&!n.has(e)){const t=`required property "${e}" is not defined at "${l.schemaEnv.baseId+l.errSchemaPath}" (strictRequired)`;(0,o.checkStrictMode)(l,t,l.opts.strictRequired)}}function p(){t.forOf("prop",a,r=>{e.setParams({missingProperty:r}),t.if((0,n.noPropertyInData)(t,i,r,u.ownProperties),()=>e.error())})}}};t.default=a},4277:(e,t,r)=>{"use strict";const n=r(909);e.exports=(e,t)=>e.sort((e,r)=>n(r,e,t))},4287:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:r(1039).validateUnion,error:{message:"must match a schema in anyOf"}};t.default=n},4405:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.executeTasksUsingMultipleRangeRequests=function(e,t,r,i,c){const l=u=>{if(u>=t.length)return null!=e.fileMetadataBuffer&&r.write(e.fileMetadataBuffer),void r.end();const d=u+1e3;!function(e,t,r,i,c){let l="bytes=",u=0;const d=new Map,p=[];for(let e=t.start;e<t.end;e++){const r=t.tasks[e];r.kind===o.OperationKind.DOWNLOAD&&(l+=`${r.start}-${r.end-1}, `,d.set(u,e),u++,p.push(r.end-r.start))}if(u<=1){const n=l=>{if(l>=t.end)return void i();const u=t.tasks[l++];if(u.kind===o.OperationKind.COPY)(0,s.copyData)(u,r,t.oldFileFd,c,()=>n(l));else{const t=e.createRequestOptions();t.headers.Range=`bytes=${u.start}-${u.end-1}`;const s=e.httpExecutor.createRequest(t,e=>{a(e,c)&&(e.pipe(r,{end:!1}),e.once("end",()=>n(l)))});e.httpExecutor.addErrorAndTimeoutHandlers(s,c),s.end()}};return void n(t.start)}const h=e.createRequestOptions();h.headers.Range=l.substring(0,l.length-2);const f=e.httpExecutor.createRequest(h,e=>{if(!a(e,c))return;const o=(0,n.safeGetHeader)(e,"content-type"),l=/^multipart\/.+?(?:; boundary=(?:(?:"(.+)")|(?:([^\s]+))))$/i.exec(o);if(null==l)return void c(new Error(`Content-Type "multipart/byteranges" is expected, but got "${o}"`));const u=new s.DataSplitter(r,t,d,l[1]||l[2],p,i);u.on("error",c),e.pipe(u),e.on("end",()=>{setTimeout(()=>{f.abort(),c(new Error("Response ends without calling any handlers"))},1e4)})});e.httpExecutor.addErrorAndTimeoutHandlers(f,c),f.end()}(e,{tasks:t,start:u,end:Math.min(t.length,d),oldFileFd:i},r,()=>l(d),c)};return l},t.checkIsRangesSupported=a;const n=r(6551),s=r(2652),o=r(1305);function a(e,t){if(e.statusCode>=400)return t((0,n.createHttpError)(e)),!1;if(206!==e.statusCode){const r=(0,n.safeGetHeader)(e,"accept-ranges");if(null==r||"none"===r)return t(new Error(`Server doesn't support Accept-Ranges (response code ${e.statusCode})`)),!1}return!0}},4434:e=>{"use strict";e.exports=require("events")},4445:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o=r(6983),a={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxLength"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:a,it:i}=e,c="maxLength"===t?n.operators.GT:n.operators.LT,l=!1===i.opts.unicode?n._`${r}.length`:n._`${(0,s.useFunc)(e.gen,o.default)}(${r})`;e.fail$data(n._`${l} ${c} ${a}`)}};t.default=a},4466:(e,t,r)=>{"use strict";var n=r(8433),s=r(5388);function o(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function a(e){return 48<=e&&e<=55}function i(e){return 48<=e&&e<=57}e.exports=new s("tag:yaml.org,2002:int",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t,r=e.length,n=0,s=!1;if(!r)return!1;if("-"!==(t=e[n])&&"+"!==t||(t=e[++n]),"0"===t){if(n+1===r)return!0;if("b"===(t=e[++n])){for(n++;n<r;n++)if("_"!==(t=e[n])){if("0"!==t&&"1"!==t)return!1;s=!0}return s&&"_"!==t}if("x"===t){for(n++;n<r;n++)if("_"!==(t=e[n])){if(!o(e.charCodeAt(n)))return!1;s=!0}return s&&"_"!==t}if("o"===t){for(n++;n<r;n++)if("_"!==(t=e[n])){if(!a(e.charCodeAt(n)))return!1;s=!0}return s&&"_"!==t}}if("_"===t)return!1;for(;n<r;n++)if("_"!==(t=e[n])){if(!i(e.charCodeAt(n)))return!1;s=!0}return!(!s||"_"===t)},construct:function(e){var t,r=e,n=1;if(-1!==r.indexOf("_")&&(r=r.replace(/_/g,"")),"-"!==(t=r[0])&&"+"!==t||("-"===t&&(n=-1),t=(r=r.slice(1))[0]),"0"===r)return 0;if("0"===t){if("b"===r[1])return n*parseInt(r.slice(2),2);if("x"===r[1])return n*parseInt(r.slice(2),16);if("o"===r[1])return n*parseInt(r.slice(2),8)}return n*parseInt(r,10)},predicate:function(e){return"[object Number]"===Object.prototype.toString.call(e)&&e%1==0&&!n.isNegativeZero(e)},represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},4483:e=>{"use strict";e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},4493:(e,t,r)=>{"use strict";const n=r(3908);e.exports=(e,t)=>new n(e,t).patch},4572:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PrivateGitHubProvider=void 0;const n=r(6551),s=r(7210),o=r(6928),a=r(7016),i=r(906),c=r(1203),l=r(5776);class u extends c.BaseGitHubProvider{constructor(e,t,r,n){super(e,"api.github.com",n),this.updater=t,this.token=r}createRequestOptions(e,t){const r=super.createRequestOptions(e,t);return r.redirect="manual",r}async getLatestVersion(){const e=new n.CancellationToken,t=(0,i.getChannelFilename)(this.getDefaultChannelName()),r=await this.getLatestVersionInfo(e),o=r.assets.find(e=>e.name===t);if(null==o)throw(0,n.newError)(`Cannot find ${t} in the release ${r.html_url||r.name}`,"ERR_UPDATER_CHANNEL_FILE_NOT_FOUND");const c=new a.URL(o.url);let l;try{l=(0,s.load)(await this.httpRequest(c,this.configureHeaders("application/octet-stream"),e))}catch(e){if(e instanceof n.HttpError&&404===e.statusCode)throw(0,n.newError)(`Cannot find ${t} in the latest release artifacts (${c}): ${e.stack||e.message}`,"ERR_UPDATER_CHANNEL_FILE_NOT_FOUND");throw e}return l.assets=r.assets,l}get fileExtraDownloadHeaders(){return this.configureHeaders("application/octet-stream")}configureHeaders(e){return{accept:e,authorization:`token ${this.token}`}}async getLatestVersionInfo(e){const t=this.updater.allowPrerelease;let r=this.basePath;t||(r=`${r}/latest`);const s=(0,i.newUrlFromBase)(r,this.baseUrl);try{const r=JSON.parse(await this.httpRequest(s,this.configureHeaders("application/vnd.github.v3+json"),e));return t?r.find(e=>e.prerelease)||r[0]:r}catch(e){throw(0,n.newError)(`Unable to find latest version on GitHub (${s}), please ensure a production release exists: ${e.stack||e.message}`,"ERR_UPDATER_LATEST_VERSION_NOT_FOUND")}}get basePath(){return this.computeGithubBasePath(`/repos/${this.options.owner}/${this.options.repo}/releases`)}resolveFiles(e){return(0,l.getFileList)(e).map(t=>{const r=o.posix.basename(t.url).replace(/ /g,"-"),s=e.assets.find(e=>null!=e&&e.name===r);if(null==s)throw(0,n.newError)(`Cannot find asset "${r}" in: ${JSON.stringify(e.assets,null,2)}`,"ERR_UPDATER_ASSET_NOT_FOUND");return{url:new a.URL(s.url),info:t}})}}t.PrivateGitHubProvider=u},4625:(e,t,r)=>{"use strict";const n=r(3044);e.exports=async({cwd:e}={})=>n("package.json",{cwd:e}),e.exports.sync=({cwd:e}={})=>n.sync("package.json",{cwd:e})},4629:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const n=r(5273),s=r(2017),o=r(9404),a=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);t.inlineRef=function(e,t=!0){return"boolean"==typeof e||(!0===t?!c(e):!!t&&l(e)<=t)};const i=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function c(e){for(const t in e){if(i.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(c))return!0;if("object"==typeof r&&c(r))return!0}return!1}function l(e){let t=0;for(const r in e){if("$ref"===r)return 1/0;if(t++,!a.has(r)&&("object"==typeof e[r]&&(0,n.eachItem)(e[r],e=>t+=l(e)),t===1/0))return 1/0}return t}function u(e,t="",r){!1!==r&&(t=h(t));const n=e.parse(t);return d(e,n)}function d(e,t){return e.serialize(t).split("#")[0]+"#"}t.getFullPath=u,t._getFullPath=d;const p=/#\/?$/;function h(e){return e?e.replace(p,""):""}t.normalizeId=h,t.resolveUrl=function(e,t,r){return r=h(r),e.resolve(t,r)};const f=/^[a-z_][-a-z0-9._]*$/i;t.getSchemaRefs=function(e,t){if("boolean"==typeof e)return{};const{schemaId:r,uriResolver:n}=this.opts,a=h(e[r]||t),i={"":a},c=u(n,a,!1),l={},d=new Set;return o(e,{allKeys:!0},(e,t,n,s)=>{if(void 0===s)return;const o=c+t;let a=i[s];function u(t){const r=this.opts.uriResolver.resolve;if(t=h(a?r(a,t):t),d.has(t))throw m(t);d.add(t);let n=this.refs[t];return"string"==typeof n&&(n=this.refs[n]),"object"==typeof n?p(e,n.schema,t):t!==h(o)&&("#"===t[0]?(p(e,l[t],t),l[t]=e):this.refs[t]=o),t}function g(e){if("string"==typeof e){if(!f.test(e))throw new Error(`invalid anchor "${e}"`);u.call(this,`#${e}`)}}"string"==typeof e[r]&&(a=u.call(this,e[r])),g.call(this,e.$anchor),g.call(this,e.$dynamicAnchor),i[t]=a}),l;function p(e,t,r){if(void 0!==t&&!s(e,t))throw m(r)}function m(e){return new Error(`reference "${e}" resolves to more than one schema`)}}},4641:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t,r)=>0===n(e,t,r)},4652:e=>{"use strict";e.exports=require("fs-extra")},4715:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o=r(2482),a={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>n._`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:a,schema:i,schemaCode:c,it:l}=e;if(!a&&0===i.length)throw new Error("enum must have non-empty array");const u=i.length>=l.opts.loopEnum;let d;const p=()=>null!=d?d:d=(0,s.useFunc)(t,o.default);let h;if(u||a)h=t.let("valid"),e.block$data(h,function(){t.assign(h,!1),t.forOf("v",c,e=>t.if(n._`${p()}(${r}, ${e})`,()=>t.assign(h,!0).break()))});else{if(!Array.isArray(i))throw new Error("ajv implementation error");const e=t.const("vSchema",c);h=(0,n.or)(...i.map((t,s)=>function(e,t){const s=i[t];return"object"==typeof s&&null!==s?n._`${p()}(${r}, ${e}[${t}])`:n._`${r} === ${s}`}(e,s)))}e.pass(h)}};t.default=a},4718:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NoOpLogger=t.AppUpdater=void 0;const n=r(6551),s=r(6982),o=r(857),a=r(4434),i=r(4652),c=r(7210),l=r(8801),u=r(6928),d=r(9589),p=r(2474),h=r(910),f=r(369),m=r(2705),g=r(7981),y=r(3106),v=r(906),w=r(2444),_=r(3765);class b extends a.EventEmitter{get channel(){return this._channel}set channel(e){if(null!=this._channel){if("string"!=typeof e)throw(0,n.newError)(`Channel must be a string, but got: ${e}`,"ERR_UPDATER_INVALID_CHANNEL");if(0===e.length)throw(0,n.newError)("Channel must be not an empty string","ERR_UPDATER_INVALID_CHANNEL")}this._channel=e,this.allowDowngrade=!0}addAuthHeader(e){this.requestHeaders=Object.assign({},this.requestHeaders,{authorization:e})}get netSession(){return(0,f.getNetSession)()}get logger(){return this._logger}set logger(e){this._logger=null==e?new $:e}set updateConfigPath(e){this.clientPromise=null,this._appUpdateConfigPath=e,this.configOnDisk=new l.Lazy(()=>this.loadUpdateConfig())}get isUpdateSupported(){return this._isUpdateSupported}set isUpdateSupported(e){e&&(this._isUpdateSupported=e)}constructor(e,t){super(),this.autoDownload=!0,this.autoInstallOnAppQuit=!0,this.autoRunAppAfterInstall=!0,this.allowPrerelease=!1,this.fullChangelog=!1,this.allowDowngrade=!1,this.disableWebInstaller=!1,this.disableDifferentialDownload=!1,this.forceDevUpdateConfig=!1,this._channel=null,this.downloadedUpdateHelper=null,this.requestHeaders=null,this._logger=console,this.signals=new _.UpdaterSignal(this),this._appUpdateConfigPath=null,this._isUpdateSupported=e=>this.checkIfUpdateSupported(e),this.clientPromise=null,this.stagingUserIdPromise=new l.Lazy(()=>this.getOrCreateStagingUserId()),this.configOnDisk=new l.Lazy(()=>this.loadUpdateConfig()),this.checkForUpdatesPromise=null,this.downloadPromise=null,this.updateInfoAndProvider=null,this._testOnlyOptions=null,this.on("error",e=>{this._logger.error(`Error: ${e.stack||e.message}`)}),null==t?(this.app=new h.ElectronAppAdapter,this.httpExecutor=new f.ElectronHttpExecutor((e,t)=>this.emit("login",e,t))):(this.app=t,this.httpExecutor=null);const r=this.app.version,s=(0,d.parse)(r);if(null==s)throw(0,n.newError)(`App version is not a valid semver version: "${r}"`,"ERR_UPDATER_INVALID_VERSION");this.currentVersion=s,this.allowPrerelease=function(e){const t=(0,d.prerelease)(e);return null!=t&&t.length>0}(s),null!=e&&(this.setFeedURL(e),"string"!=typeof e&&e.requestHeaders&&(this.requestHeaders=e.requestHeaders))}getFeedURL(){return"Deprecated. Do not use it."}setFeedURL(e){const t=this.createProviderRuntimeOptions();let r;r="string"==typeof e?new m.GenericProvider({provider:"generic",url:e},this,{...t,isUseMultipleRangeRequest:(0,g.isUrlProbablySupportMultiRangeRequests)(e)}):(0,g.createClient)(e,this,t),this.clientPromise=Promise.resolve(r)}checkForUpdates(){if(!this.isUpdaterActive())return Promise.resolve(null);let e=this.checkForUpdatesPromise;if(null!=e)return this._logger.info("Checking for update (already in progress)"),e;const t=()=>this.checkForUpdatesPromise=null;return this._logger.info("Checking for update"),e=this.doCheckForUpdates().then(e=>(t(),e)).catch(e=>{throw t(),this.emit("error",e,`Cannot check for updates: ${(e.stack||e).toString()}`),e}),this.checkForUpdatesPromise=e,e}isUpdaterActive(){return!(!this.app.isPackaged&&!this.forceDevUpdateConfig&&(this._logger.info("Skip checkForUpdates because application is not packed and dev update config is not forced"),1))}checkForUpdatesAndNotify(e){return this.checkForUpdates().then(t=>(null==t?void 0:t.downloadPromise)?(t.downloadPromise.then(()=>{const n=b.formatDownloadNotification(t.updateInfo.version,this.app.name,e);new(r(4157).Notification)(n).show()}),t):(null!=this._logger.debug&&this._logger.debug("checkForUpdatesAndNotify called, downloadPromise is null"),t))}static formatDownloadNotification(e,t,r){return null==r&&(r={title:"A new update is ready to install",body:"{appName} version {version} has been downloaded and will be automatically installed on exit"}),{title:r.title.replace("{appName}",t).replace("{version}",e),body:r.body.replace("{appName}",t).replace("{version}",e)}}async isStagingMatch(e){const t=e.stagingPercentage;let r=t;if(null==r)return!0;if(r=parseInt(r,10),isNaN(r))return this._logger.warn(`Staging percentage is NaN: ${t}`),!0;r/=100;const s=await this.stagingUserIdPromise.value,o=n.UUID.parse(s).readUInt32BE(12)/4294967295;return this._logger.info(`Staging percentage: ${r}, percentage: ${o}, user id: ${s}`),o<r}computeFinalHeaders(e){return null!=this.requestHeaders&&Object.assign(e,this.requestHeaders),e}async isUpdateAvailable(e){const t=(0,d.parse)(e.version);if(null==t)throw(0,n.newError)(`This file could not be downloaded, or the latest version (from update server) does not have a valid semver version: "${e.version}"`,"ERR_UPDATER_INVALID_VERSION");const r=this.currentVersion;if((0,d.eq)(t,r))return!1;if(!await Promise.resolve(this.isUpdateSupported(e)))return!1;if(!await this.isStagingMatch(e))return!1;const s=(0,d.gt)(t,r),o=(0,d.lt)(t,r);return!!s||this.allowDowngrade&&o}checkIfUpdateSupported(e){const t=null==e?void 0:e.minimumSystemVersion,r=(0,o.release)();if(t)try{if((0,d.lt)(r,t))return this._logger.info(`Current OS version ${r} is less than the minimum OS version required ${t} for version ${r}`),!1}catch(e){this._logger.warn(`Failed to compare current OS version(${r}) with minimum OS version(${t}): ${(e.message||e).toString()}`)}return!0}async getUpdateInfoAndProvider(){await this.app.whenReady(),null==this.clientPromise&&(this.clientPromise=this.configOnDisk.value.then(e=>(0,g.createClient)(e,this,this.createProviderRuntimeOptions())));const e=await this.clientPromise,t=await this.stagingUserIdPromise.value;return e.setRequestHeaders(this.computeFinalHeaders({"x-user-staging-id":t})),{info:await e.getLatestVersion(),provider:e}}createProviderRuntimeOptions(){return{isUseMultipleRangeRequest:!0,platform:null==this._testOnlyOptions?process.platform:this._testOnlyOptions.platform,executor:this.httpExecutor}}async doCheckForUpdates(){this.emit("checking-for-update");const e=await this.getUpdateInfoAndProvider(),t=e.info;if(!await this.isUpdateAvailable(t))return this._logger.info(`Update for version ${this.currentVersion.format()} is not available (latest version: ${t.version}, downgrade is ${this.allowDowngrade?"allowed":"disallowed"}).`),this.emit("update-not-available",t),{isUpdateAvailable:!1,versionInfo:t,updateInfo:t};this.updateInfoAndProvider=e,this.onUpdateAvailable(t);const r=new n.CancellationToken;return{isUpdateAvailable:!0,versionInfo:t,updateInfo:t,cancellationToken:r,downloadPromise:this.autoDownload?this.downloadUpdate(r):null}}onUpdateAvailable(e){this._logger.info(`Found version ${e.version} (url: ${(0,n.asArray)(e.files).map(e=>e.url).join(", ")})`),this.emit("update-available",e)}downloadUpdate(e=new n.CancellationToken){const t=this.updateInfoAndProvider;if(null==t){const e=new Error("Please check update first");return this.dispatchError(e),Promise.reject(e)}if(null!=this.downloadPromise)return this._logger.info("Downloading update (already in progress)"),this.downloadPromise;this._logger.info(`Downloading update from ${(0,n.asArray)(t.info.files).map(e=>e.url).join(", ")}`);const r=e=>{if(!(e instanceof n.CancellationError))try{this.dispatchError(e)}catch(e){this._logger.warn(`Cannot dispatch error event: ${e.stack||e}`)}return e};return this.downloadPromise=this.doDownloadUpdate({updateInfoAndProvider:t,requestHeaders:this.computeRequestHeaders(t.provider),cancellationToken:e,disableWebInstaller:this.disableWebInstaller,disableDifferentialDownload:this.disableDifferentialDownload}).catch(e=>{throw r(e)}).finally(()=>{this.downloadPromise=null}),this.downloadPromise}dispatchError(e){this.emit("error",e,(e.stack||e).toString())}dispatchUpdateDownloaded(e){this.emit(_.UPDATE_DOWNLOADED,e)}async loadUpdateConfig(){return null==this._appUpdateConfigPath&&(this._appUpdateConfigPath=this.app.appUpdateConfigPath),(0,c.load)(await(0,i.readFile)(this._appUpdateConfigPath,"utf-8"))}computeRequestHeaders(e){const t=e.fileExtraDownloadHeaders;if(null!=t){const e=this.requestHeaders;return null==e?t:{...t,...e}}return this.computeFinalHeaders({accept:"*/*"})}async getOrCreateStagingUserId(){const e=u.join(this.app.userDataPath,".updaterId");try{const t=await(0,i.readFile)(e,"utf-8");if(n.UUID.check(t))return t;this._logger.warn(`Staging user id file exists, but content was invalid: ${t}`)}catch(e){"ENOENT"!==e.code&&this._logger.warn(`Couldn't read staging user ID, creating a blank one: ${e}`)}const t=n.UUID.v5((0,s.randomBytes)(4096),n.UUID.OID);this._logger.info(`Generated new staging user ID: ${t}`);try{await(0,i.outputFile)(e,t)}catch(e){this._logger.warn(`Couldn't write out staging user ID: ${e}`)}return t}get isAddNoCacheQuery(){const e=this.requestHeaders;if(null==e)return!0;for(const t of Object.keys(e)){const e=t.toLowerCase();if("authorization"===e||"private-token"===e)return!1}return!0}async getOrCreateDownloadHelper(){let e=this.downloadedUpdateHelper;if(null==e){const t=(await this.configOnDisk.value).updaterCacheDirName,r=this._logger;null==t&&r.error("updaterCacheDirName is not specified in app-update.yml Was app build using at least electron-builder 20.34.0?");const n=u.join(this.app.baseCachePath,t||this.app.name);null!=r.debug&&r.debug(`updater cache dir: ${n}`),e=new p.DownloadedUpdateHelper(n),this.downloadedUpdateHelper=e}return e}async executeDownload(e){const t=e.fileInfo,r={headers:e.downloadUpdateOptions.requestHeaders,cancellationToken:e.downloadUpdateOptions.cancellationToken,sha2:t.info.sha2,sha512:t.info.sha512};this.listenerCount(_.DOWNLOAD_PROGRESS)>0&&(r.onProgress=e=>this.emit(_.DOWNLOAD_PROGRESS,e));const s=e.downloadUpdateOptions.updateInfoAndProvider.info,o=s.version,a=t.packageInfo,c=await this.getOrCreateDownloadHelper(),l=c.cacheDirForPendingUpdate;await(0,i.mkdir)(l,{recursive:!0});const d=function(){const t=decodeURIComponent(e.fileInfo.url.pathname);return t.endsWith(`.${e.fileExtension}`)?u.basename(t):e.fileInfo.info.url}();let h=u.join(l,d);const f=null==a?null:u.join(l,`package-${o}${u.extname(a.path)||".7z"}`),m=async r=>(await c.setDownloadedFile(h,f,s,t,d,r),await e.done({...s,downloadedFile:h}),null==f?[h]:[h,f]),g=this._logger,y=await c.validateDownloadedPath(h,s,t,g);if(null!=y)return h=y,await m(!1);const v=async()=>(await c.clear().catch(()=>{}),await(0,i.unlink)(h).catch(()=>{})),w=await(0,p.createTempUpdateFile)(`temp-${d}`,l,g);try{await e.task(w,r,f,v),await(0,n.retry)(()=>(0,i.rename)(w,h),60,500,0,0,e=>e instanceof Error&&/^EBUSY:/.test(e.message))}catch(e){throw await v(),e instanceof n.CancellationError&&(g.info("cancelled"),this.emit("update-cancelled",s)),e}return g.info(`New version ${o} has been downloaded to ${h}`),await m(!0)}async differentialDownloadInstaller(e,t,r,n,s){try{if(null!=this._testOnlyOptions&&!this._testOnlyOptions.isUseDifferentialDownload)return!0;const o=(0,v.blockmapFiles)(e.url,this.app.version,t.updateInfoAndProvider.info.version);this._logger.info(`Download block maps (old: "${o[0]}", new: ${o[1]})`);const a=async e=>{const r=await this.httpExecutor.downloadToBuffer(e,{headers:t.requestHeaders,cancellationToken:t.cancellationToken});if(null==r||0===r.length)throw new Error(`Blockmap "${e.href}" is empty`);try{return JSON.parse((0,y.gunzipSync)(r).toString())}catch(t){throw new Error(`Cannot parse blockmap "${e.href}", error: ${t}`)}},i={newUrl:e.url,oldFile:u.join(this.downloadedUpdateHelper.cacheDir,s),logger:this._logger,newFile:r,isUseMultipleRangeRequest:n.isUseMultipleRangeRequest,requestHeaders:t.requestHeaders,cancellationToken:t.cancellationToken};this.listenerCount(_.DOWNLOAD_PROGRESS)>0&&(i.onProgress=e=>this.emit(_.DOWNLOAD_PROGRESS,e));const c=await Promise.all(o.map(e=>a(e)));return await new w.GenericDifferentialDownloader(e.info,this.httpExecutor,i).download(c[0],c[1]),!1}catch(e){if(this._logger.error(`Cannot download differentially, fallback to full download: ${e.stack||e}`),null!=this._testOnlyOptions)throw e;return!0}}}t.AppUpdater=b;class ${info(e){}warn(e){}error(e){}}t.NoOpLogger=$},4727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(6928),s=r(4763),o=r(2750),a={store:{},create:e=>{const t=`000000${Math.floor(16777215*Math.random()).toString(16)}`.slice(-6);return`${e}.tmp-${Date.now().toString().slice(-10)}${t}`},get:(e,t,r=!0)=>{const n=a.truncate(t(e));return n in a.store?a.get(e,t,r):(a.store[n]=r,[n,()=>delete a.store[n]])},purge:e=>{a.store[e]&&(delete a.store[e],o.default.unlinkAttempt(e))},purgeSync:e=>{a.store[e]&&(delete a.store[e],o.default.unlinkSyncAttempt(e))},purgeSyncAll:()=>{for(const e in a.store)a.purgeSync(e)},truncate:e=>{const t=n.basename(e);if(t.length<=s.LIMIT_BASENAME_LENGTH)return e;const r=/^(\.?)(.*?)((?:\.[^.]+)?(?:\.tmp-\d{10}[a-f0-9]{6})?)$/.exec(t);if(!r)return e;const o=t.length-s.LIMIT_BASENAME_LENGTH;return`${e.slice(0,-t.length)}${r[1]}${r[2].slice(0,-o)}${r[3]}`}};process.on("exit",a.purgeSyncAll),t.default=a},4747:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(2378),o=r(5857),a=r(2877),i=r(5273),c={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===s.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>n._`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:c,parentSchema:l,it:u}=e,{oneOf:d}=l;if(!u.opts.discriminator)throw new Error("discriminator: requires discriminator option");const p=c.propertyName;if("string"!=typeof p)throw new Error("discriminator: requires propertyName");if(c.mapping)throw new Error("discriminator: mapping is not supported");if(!d)throw new Error("discriminator: requires oneOf keyword");const h=t.let("valid",!1),f=t.const("tag",n._`${r}${(0,n.getProperty)(p)}`);function m(r){const s=t.name("valid"),o=e.subschema({keyword:"oneOf",schemaProp:r},s);return e.mergeEvaluated(o,n.Name),s}t.if(n._`typeof ${f} == "string"`,()=>function(){const r=function(){var e;const t={},r=s(l);let n=!0;for(let t=0;t<d.length;t++){let l=d[t];if((null==l?void 0:l.$ref)&&!(0,i.schemaHasRulesButRef)(l,u.self.RULES)){const e=l.$ref;if(l=o.resolveRef.call(u.self,u.schemaEnv.root,u.baseId,e),l instanceof o.SchemaEnv&&(l=l.schema),void 0===l)throw new a.default(u.opts.uriResolver,u.baseId,e)}const h=null===(e=null==l?void 0:l.properties)||void 0===e?void 0:e[p];if("object"!=typeof h)throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${p}"`);n=n&&(r||s(l)),c(h,t)}if(!n)throw new Error(`discriminator: "${p}" must be required`);return t;function s({required:e}){return Array.isArray(e)&&e.includes(p)}function c(e,t){if(e.const)h(e.const,t);else{if(!e.enum)throw new Error(`discriminator: "properties/${p}" must have "const" or "enum"`);for(const r of e.enum)h(r,t)}}function h(e,r){if("string"!=typeof e||e in t)throw new Error(`discriminator: "${p}" values must be unique strings`);t[e]=r}}();t.if(!1);for(const e in r)t.elseIf(n._`${f} === ${e}`),t.assign(h,m(r[e]));t.else(),e.error(!1,{discrError:s.DiscrError.Mapping,tag:f,tagName:p}),t.endIf()}(),()=>e.error(!1,{discrError:s.DiscrError.Tag,tag:f,tagName:p})),e.ok(h)}};t.default=c},4763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP=t.LIMIT_FILES_DESCRIPTORS=t.LIMIT_BASENAME_LENGTH=t.IS_USER_ROOT=t.IS_POSIX=t.DEFAULT_TIMEOUT_SYNC=t.DEFAULT_TIMEOUT_ASYNC=t.DEFAULT_WRITE_OPTIONS=t.DEFAULT_READ_OPTIONS=t.DEFAULT_FOLDER_MODE=t.DEFAULT_FILE_MODE=t.DEFAULT_ENCODING=void 0,t.DEFAULT_ENCODING="utf8",t.DEFAULT_FILE_MODE=438,t.DEFAULT_FOLDER_MODE=511,t.DEFAULT_READ_OPTIONS={},t.DEFAULT_WRITE_OPTIONS={},t.DEFAULT_TIMEOUT_ASYNC=5e3,t.DEFAULT_TIMEOUT_SYNC=100;const r=!!process.getuid;t.IS_POSIX=r;const n=!!process.getuid&&!process.getuid();t.IS_USER_ROOT=n,t.LIMIT_BASENAME_LENGTH=128,t.LIMIT_FILES_DESCRIPTORS=1e4,t.NOOP=()=>{}},4781:(e,t,r)=>{"use strict";var n=r(8433),s=r(1231),o=r(5489),a=Object.prototype.toString,i=Object.prototype.hasOwnProperty,c=65279,l={0:"\\0",7:"\\a",8:"\\b",9:"\\t",10:"\\n",11:"\\v",12:"\\f",13:"\\r",27:"\\e",34:'\\"',92:"\\\\",133:"\\N",160:"\\_",8232:"\\L",8233:"\\P"},u=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],d=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function p(e){var t,r,o;if(t=e.toString(16).toUpperCase(),e<=255)r="x",o=2;else if(e<=65535)r="u",o=4;else{if(!(e<=4294967295))throw new s("code point within a string may not be greater than 0xFFFFFFFF");r="U",o=8}return"\\"+r+n.repeat("0",o-t.length)+t}function h(e){this.schema=e.schema||o,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=n.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=function(e,t){var r,n,s,o,a,c,l;if(null===t)return{};for(r={},s=0,o=(n=Object.keys(t)).length;s<o;s+=1)a=n[s],c=String(t[a]),"!!"===a.slice(0,2)&&(a="tag:yaml.org,2002:"+a.slice(2)),(l=e.compiledTypeMap.fallback[a])&&i.call(l.styleAliases,c)&&(c=l.styleAliases[c]),r[a]=c;return r}(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType='"'===e.quotingType?2:1,this.forceQuotes=e.forceQuotes||!1,this.replacer="function"==typeof e.replacer?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function f(e,t){for(var r,s=n.repeat(" ",t),o=0,a=-1,i="",c=e.length;o<c;)-1===(a=e.indexOf("\n",o))?(r=e.slice(o),o=c):(r=e.slice(o,a+1),o=a+1),r.length&&"\n"!==r&&(i+=s),i+=r;return i}function m(e,t){return"\n"+n.repeat(" ",e.indent*t)}function g(e){return 32===e||9===e}function y(e){return 32<=e&&e<=126||161<=e&&e<=55295&&8232!==e&&8233!==e||57344<=e&&e<=65533&&e!==c||65536<=e&&e<=1114111}function v(e){return y(e)&&e!==c&&13!==e&&10!==e}function w(e,t,r){var n=v(e),s=n&&!g(e);return(r?n:n&&44!==e&&91!==e&&93!==e&&123!==e&&125!==e)&&35!==e&&!(58===t&&!s)||v(t)&&!g(t)&&35===e||58===t&&s}function _(e,t){var r,n=e.charCodeAt(t);return n>=55296&&n<=56319&&t+1<e.length&&(r=e.charCodeAt(t+1))>=56320&&r<=57343?1024*(n-55296)+r-56320+65536:n}function b(e){return/^\n* /.test(e)}function $(e,t,r,n,o){e.dump=function(){if(0===t.length)return 2===e.quotingType?'""':"''";if(!e.noCompatMode&&(-1!==u.indexOf(t)||d.test(t)))return 2===e.quotingType?'"'+t+'"':"'"+t+"'";var a=e.indent*Math.max(1,r),i=-1===e.lineWidth?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-a),h=n||e.flowLevel>-1&&r>=e.flowLevel;switch(function(e,t,r,n,s,o,a,i){var l,u,d=0,p=null,h=!1,f=!1,m=-1!==n,v=-1,$=y(u=_(e,0))&&u!==c&&!g(u)&&45!==u&&63!==u&&58!==u&&44!==u&&91!==u&&93!==u&&123!==u&&125!==u&&35!==u&&38!==u&&42!==u&&33!==u&&124!==u&&61!==u&&62!==u&&39!==u&&34!==u&&37!==u&&64!==u&&96!==u&&function(e){return!g(e)&&58!==e}(_(e,e.length-1));if(t||a)for(l=0;l<e.length;d>=65536?l+=2:l++){if(!y(d=_(e,l)))return 5;$=$&&w(d,p,i),p=d}else{for(l=0;l<e.length;d>=65536?l+=2:l++){if(10===(d=_(e,l)))h=!0,m&&(f=f||l-v-1>n&&" "!==e[v+1],v=l);else if(!y(d))return 5;$=$&&w(d,p,i),p=d}f=f||m&&l-v-1>n&&" "!==e[v+1]}return h||f?r>9&&b(e)?5:a?2===o?5:2:f?4:3:!$||a||s(e)?2===o?5:2:1}(t,h,e.indent,i,function(t){return function(e,t){var r,n;for(r=0,n=e.implicitTypes.length;r<n;r+=1)if(e.implicitTypes[r].resolve(t))return!0;return!1}(e,t)},e.quotingType,e.forceQuotes&&!n,o)){case 1:return t;case 2:return"'"+t.replace(/'/g,"''")+"'";case 3:return"|"+E(t,e.indent)+S(f(t,a));case 4:return">"+E(t,e.indent)+S(f(function(e,t){for(var r,n,s,o=/(\n+)([^\n]*)/g,a=(s=-1!==(s=e.indexOf("\n"))?s:e.length,o.lastIndex=s,P(e.slice(0,s),t)),i="\n"===e[0]||" "===e[0];n=o.exec(e);){var c=n[1],l=n[2];r=" "===l[0],a+=c+(i||r||""===l?"":"\n")+P(l,t),i=r}return a}(t,i),a));case 5:return'"'+function(e){for(var t,r="",n=0,s=0;s<e.length;n>=65536?s+=2:s++)n=_(e,s),!(t=l[n])&&y(n)?(r+=e[s],n>=65536&&(r+=e[s+1])):r+=t||p(n);return r}(t)+'"';default:throw new s("impossible error: invalid scalar style")}}()}function E(e,t){var r=b(e)?String(t):"",n="\n"===e[e.length-1];return r+(!n||"\n"!==e[e.length-2]&&"\n"!==e?n?"":"-":"+")+"\n"}function S(e){return"\n"===e[e.length-1]?e.slice(0,-1):e}function P(e,t){if(""===e||" "===e[0])return e;for(var r,n,s=/ [^ ]/g,o=0,a=0,i=0,c="";r=s.exec(e);)(i=r.index)-o>t&&(n=a>o?a:i,c+="\n"+e.slice(o,n),o=n+1),a=i;return c+="\n",e.length-o>t&&a>o?c+=e.slice(o,a)+"\n"+e.slice(a+1):c+=e.slice(o),c.slice(1)}function O(e,t,r,n){var s,o,a,i="",c=e.tag;for(s=0,o=r.length;s<o;s+=1)a=r[s],e.replacer&&(a=e.replacer.call(r,String(s),a)),(N(e,t+1,a,!0,!0,!1,!0)||void 0===a&&N(e,t+1,null,!0,!0,!1,!0))&&(n&&""===i||(i+=m(e,t)),e.dump&&10===e.dump.charCodeAt(0)?i+="-":i+="- ",i+=e.dump);e.tag=c,e.dump=i||"[]"}function C(e,t,r){var n,o,c,l,u,d;for(c=0,l=(o=r?e.explicitTypes:e.implicitTypes).length;c<l;c+=1)if(((u=o[c]).instanceOf||u.predicate)&&(!u.instanceOf||"object"==typeof t&&t instanceof u.instanceOf)&&(!u.predicate||u.predicate(t))){if(r?u.multi&&u.representName?e.tag=u.representName(t):e.tag=u.tag:e.tag="?",u.represent){if(d=e.styleMap[u.tag]||u.defaultStyle,"[object Function]"===a.call(u.represent))n=u.represent(t,d);else{if(!i.call(u.represent,d))throw new s("!<"+u.tag+'> tag resolver accepts not "'+d+'" style');n=u.represent[d](t,d)}e.dump=n}return!0}return!1}function N(e,t,r,n,o,i,c){e.tag=null,e.dump=r,C(e,r,!1)||C(e,r,!0);var l,u=a.call(e.dump),d=n;n&&(n=e.flowLevel<0||e.flowLevel>t);var p,h,f="[object Object]"===u||"[object Array]"===u;if(f&&(h=-1!==(p=e.duplicates.indexOf(r))),(null!==e.tag&&"?"!==e.tag||h||2!==e.indent&&t>0)&&(o=!1),h&&e.usedDuplicates[p])e.dump="*ref_"+p;else{if(f&&h&&!e.usedDuplicates[p]&&(e.usedDuplicates[p]=!0),"[object Object]"===u)n&&0!==Object.keys(e.dump).length?(function(e,t,r,n){var o,a,i,c,l,u,d="",p=e.tag,h=Object.keys(r);if(!0===e.sortKeys)h.sort();else if("function"==typeof e.sortKeys)h.sort(e.sortKeys);else if(e.sortKeys)throw new s("sortKeys must be a boolean or a function");for(o=0,a=h.length;o<a;o+=1)u="",n&&""===d||(u+=m(e,t)),c=r[i=h[o]],e.replacer&&(c=e.replacer.call(r,i,c)),N(e,t+1,i,!0,!0,!0)&&((l=null!==e.tag&&"?"!==e.tag||e.dump&&e.dump.length>1024)&&(e.dump&&10===e.dump.charCodeAt(0)?u+="?":u+="? "),u+=e.dump,l&&(u+=m(e,t)),N(e,t+1,c,!0,l)&&(e.dump&&10===e.dump.charCodeAt(0)?u+=":":u+=": ",d+=u+=e.dump));e.tag=p,e.dump=d||"{}"}(e,t,e.dump,o),h&&(e.dump="&ref_"+p+e.dump)):(function(e,t,r){var n,s,o,a,i,c="",l=e.tag,u=Object.keys(r);for(n=0,s=u.length;n<s;n+=1)i="",""!==c&&(i+=", "),e.condenseFlow&&(i+='"'),a=r[o=u[n]],e.replacer&&(a=e.replacer.call(r,o,a)),N(e,t,o,!1,!1)&&(e.dump.length>1024&&(i+="? "),i+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),N(e,t,a,!1,!1)&&(c+=i+=e.dump));e.tag=l,e.dump="{"+c+"}"}(e,t,e.dump),h&&(e.dump="&ref_"+p+" "+e.dump));else if("[object Array]"===u)n&&0!==e.dump.length?(e.noArrayIndent&&!c&&t>0?O(e,t-1,e.dump,o):O(e,t,e.dump,o),h&&(e.dump="&ref_"+p+e.dump)):(function(e,t,r){var n,s,o,a="",i=e.tag;for(n=0,s=r.length;n<s;n+=1)o=r[n],e.replacer&&(o=e.replacer.call(r,String(n),o)),(N(e,t,o,!1,!1)||void 0===o&&N(e,t,null,!1,!1))&&(""!==a&&(a+=","+(e.condenseFlow?"":" ")),a+=e.dump);e.tag=i,e.dump="["+a+"]"}(e,t,e.dump),h&&(e.dump="&ref_"+p+" "+e.dump));else{if("[object String]"!==u){if("[object Undefined]"===u)return!1;if(e.skipInvalid)return!1;throw new s("unacceptable kind of an object to dump "+u)}"?"!==e.tag&&$(e,e.dump,t,i,d)}null!==e.tag&&"?"!==e.tag&&(l=encodeURI("!"===e.tag[0]?e.tag.slice(1):e.tag).replace(/!/g,"%21"),l="!"===e.tag[0]?"!"+l:"tag:yaml.org,2002:"===l.slice(0,18)?"!!"+l.slice(18):"!<"+l+">",e.dump=l+" "+e.dump)}return!0}function I(e,t){var r,n,s=[],o=[];for(T(e,s,o),r=0,n=o.length;r<n;r+=1)t.duplicates.push(s[o[r]]);t.usedDuplicates=new Array(n)}function T(e,t,r){var n,s,o;if(null!==e&&"object"==typeof e)if(-1!==(s=t.indexOf(e)))-1===r.indexOf(s)&&r.push(s);else if(t.push(e),Array.isArray(e))for(s=0,o=e.length;s<o;s+=1)T(e[s],t,r);else for(s=0,o=(n=Object.keys(e)).length;s<o;s+=1)T(e[n[s]],t,r)}e.exports.dump=function(e,t){var r=new h(t=t||{});r.noRefs||I(e,r);var n=e;return r.replacer&&(n=r.replacer.call({"":n},"",n)),N(r,0,n,!0,!0)?r.dump+"\n":""}},4824:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(344),s=r(2348),o=r(2610),a=r(2884),i=r(3929),c=[n.default,s.default,(0,o.default)(),a.default,i.metadataVocabulary,i.contentVocabulary];t.default=c},4834:e=>{"use strict";const t=RegExp.prototype.test.bind(/^[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}$/iu),r=RegExp.prototype.test.bind(/^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)$/u);function n(e){let t="",r=0,n=0;for(n=0;n<e.length;n++)if(r=e[n].charCodeAt(0),48!==r){if(!(r>=48&&r<=57||r>=65&&r<=70||r>=97&&r<=102))return"";t+=e[n];break}for(n+=1;n<e.length;n++){if(r=e[n].charCodeAt(0),!(r>=48&&r<=57||r>=65&&r<=70||r>=97&&r<=102))return"";t+=e[n]}return t}const s=RegExp.prototype.test.bind(/[^!"$&'()*+,\-.;=_`a-z{}~]/u);function o(e){return e.length=0,!0}function a(e,t,r){if(e.length){const s=n(e);if(""===s)return r.error=!0,!1;t.push(s),e.length=0}return!0}function i(e){if(function(e){let t=0;for(let r=0;r<e.length;r++)":"===e[r]&&t++;return t}(e)<2)return{host:e,isIPV6:!1};const t=function(e){let t=0;const r={error:!1,address:"",zone:""},s=[],i=[];let c=!1,l=!1,u=a;for(let n=0;n<e.length;n++){const a=e[n];if("["!==a&&"]"!==a)if(":"!==a)if("%"===a){if(!u(i,s,r))break;u=o}else i.push(a);else{if(!0===c&&(l=!0),!u(i,s,r))break;if(++t>7){r.error=!0;break}n>0&&":"===e[n-1]&&(c=!0),s.push(":")}}return i.length&&(u===o?r.zone=i.join(""):l?s.push(i.join("")):s.push(n(i))),r.address=s.join(""),r}(e);if(t.error)return{host:e,isIPV6:!1};{let e=t.address,r=t.address;return t.zone&&(e+="%"+t.zone,r+="%25"+t.zone),{host:e,isIPV6:!0,escapedHost:r}}}e.exports={nonSimpleDomain:s,recomposeAuthority:function(e){const t=[];if(void 0!==e.userinfo&&(t.push(e.userinfo),t.push("@")),void 0!==e.host){let n=unescape(e.host);if(!r(n)){const t=i(n);n=!0===t.isIPV6?`[${t.escapedHost}]`:e.host}t.push(n)}return"number"!=typeof e.port&&"string"!=typeof e.port||(t.push(":"),t.push(String(e.port))),t.length?t.join(""):void 0},normalizeComponentEncoding:function(e,t){const r=!0!==t?escape:unescape;return void 0!==e.scheme&&(e.scheme=r(e.scheme)),void 0!==e.userinfo&&(e.userinfo=r(e.userinfo)),void 0!==e.host&&(e.host=r(e.host)),void 0!==e.path&&(e.path=r(e.path)),void 0!==e.query&&(e.query=r(e.query)),void 0!==e.fragment&&(e.fragment=r(e.fragment)),e},removeDotSegments:function(e){let t=e;const r=[];let n=-1,s=0;for(;s=t.length;){if(1===s){if("."===t)break;if("/"===t){r.push("/");break}r.push(t);break}if(2===s){if("."===t[0]){if("."===t[1])break;if("/"===t[1]){t=t.slice(2);continue}}else if("/"===t[0]&&("."===t[1]||"/"===t[1])){r.push("/");break}}else if(3===s&&"/.."===t){0!==r.length&&r.pop(),r.push("/");break}if("."===t[0]){if("."===t[1]){if("/"===t[2]){t=t.slice(3);continue}}else if("/"===t[1]){t=t.slice(2);continue}}else if("/"===t[0]&&"."===t[1]){if("/"===t[2]){t=t.slice(2);continue}if("."===t[2]&&"/"===t[3]){t=t.slice(3),0!==r.length&&r.pop();continue}}if(-1===(n=t.indexOf("/",1))){r.push(t);break}r.push(t.slice(0,n)),t=t.slice(n)}return r.join("")},isIPv4:r,isUUID:t,normalizeIPv6:i,stringArrayToHexStripped:n}},4865:e=>{"use strict";const t=(e,t,n,s)=>{if("length"===n||"prototype"===n)return;if("arguments"===n||"caller"===n)return;const o=Object.getOwnPropertyDescriptor(e,n),a=Object.getOwnPropertyDescriptor(t,n);!r(o,a)&&s||Object.defineProperty(e,n,a)},r=function(e,t){return void 0===e||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},n=(e,t)=>`/* Wrapped ${e}*/\n${t}`,s=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),o=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name");e.exports=(e,r,{ignoreNonConfigurable:a=!1}={})=>{const{name:i}=e;for(const n of Reflect.ownKeys(r))t(e,r,n,a);return((e,t)=>{const r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)})(e,r),((e,t,r)=>{const a=""===r?"":`with ${r.trim()}() `,i=n.bind(null,a,t.toString());Object.defineProperty(i,"name",o),Object.defineProperty(e,"toString",{...s,value:i})})(e,r,i),e}},4920:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(8380),s={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,n.validateTuple)(e,"items")};t.default=s},4936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=n.operators,o={maximum:{okStr:"<=",ok:s.LTE,fail:s.GT},minimum:{okStr:">=",ok:s.GTE,fail:s.LT},exclusiveMaximum:{okStr:"<",ok:s.LT,fail:s.GTE},exclusiveMinimum:{okStr:">",ok:s.GT,fail:s.LTE}},a={message:({keyword:e,schemaCode:t})=>n.str`must be ${o[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>n._`{comparison: ${o[e].okStr}, limit: ${t}}`},i={keyword:Object.keys(o),type:"number",schemaType:"number",$data:!0,error:a,code(e){const{keyword:t,data:r,schemaCode:s}=e;e.fail$data(n._`${r} ${o[t].fail} ${s} || isNaN(${r})`)}};t.default=i},5021:(e,t,r)=>{"use strict";const n=r(9896);e.exports=e=>new Promise(t=>{n.access(e,e=>{t(!e)})}),e.exports.sync=e=>{try{return n.accessSync(e),!0}catch(e){return!1}}},5032:(e,t,r)=>{"use strict";const n=r(8311),s=r(3904),{ANY:o}=s,a=r(7638),i=r(560),c=[new s(">=0.0.0-0")],l=[new s(">=0.0.0")],u=(e,t,r)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===o){if(1===t.length&&t[0].semver===o)return!0;e=r.includePrerelease?c:l}if(1===t.length&&t[0].semver===o){if(r.includePrerelease)return!0;t=l}const n=new Set;let s,u,h,f,m,g,y;for(const t of e)">"===t.operator||">="===t.operator?s=d(s,t,r):"<"===t.operator||"<="===t.operator?u=p(u,t,r):n.add(t.semver);if(n.size>1)return null;if(s&&u){if(h=i(s.semver,u.semver,r),h>0)return null;if(0===h&&(">="!==s.operator||"<="!==u.operator))return null}for(const e of n){if(s&&!a(e,String(s),r))return null;if(u&&!a(e,String(u),r))return null;for(const n of t)if(!a(e,String(n),r))return!1;return!0}let v=!(!u||r.includePrerelease||!u.semver.prerelease.length)&&u.semver,w=!(!s||r.includePrerelease||!s.semver.prerelease.length)&&s.semver;v&&1===v.prerelease.length&&"<"===u.operator&&0===v.prerelease[0]&&(v=!1);for(const e of t){if(y=y||">"===e.operator||">="===e.operator,g=g||"<"===e.operator||"<="===e.operator,s)if(w&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===w.major&&e.semver.minor===w.minor&&e.semver.patch===w.patch&&(w=!1),">"===e.operator||">="===e.operator){if(f=d(s,e,r),f===e&&f!==s)return!1}else if(">="===s.operator&&!a(s.semver,String(e),r))return!1;if(u)if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),"<"===e.operator||"<="===e.operator){if(m=p(u,e,r),m===e&&m!==u)return!1}else if("<="===u.operator&&!a(u.semver,String(e),r))return!1;if(!e.operator&&(u||s)&&0!==h)return!1}return!(s&&g&&!u&&0!==h||u&&y&&!s&&0!==h||w||v)},d=(e,t,r)=>{if(!e)return t;const n=i(e.semver,t.semver,r);return n>0?e:n<0||">"===t.operator&&">="===e.operator?t:e},p=(e,t,r)=>{if(!e)return t;const n=i(e.semver,t.semver,r);return n<0?e:n>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let s=!1;e:for(const n of e.set){for(const e of t.set){const t=u(n,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},5200:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t,r)=>n(e,t,r)<=0},5219:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;const n=r(3789),s=r(9598),o=r(6735),a=r(3043),i=r(7083),c=r(9810);class l{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!==(t=e.baseId)&&void 0!==t?t:(0,a.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function u(e){const t=p.call(this,e);if(t)return t;const r=(0,a.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:i,lines:l}=this.opts.code,{ownProperties:u}=this.opts,d=new n.CodeGen(this.scope,{es5:i,lines:l,ownProperties:u});let h;e.$async&&(h=d.scopeValue("Error",{ref:s.default,code:n._`require("ajv/dist/runtime/validation_error").default`}));const f=d.scopeName("validate");e.validateName=f;const m={gen:d,allErrors:this.opts.allErrors,data:o.default.data,parentData:o.default.parentData,parentDataProperty:o.default.parentDataProperty,dataNames:[o.default.data],dataPathArr:[n.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:d.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,n.stringify)(e.schema)}:{ref:e.schema}),validateName:f,ValidationError:h,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:n.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:n._`""`,opts:this.opts,self:this};let g;try{this._compilations.add(e),(0,c.validateFunctionCode)(m),d.optimize(this.opts.code.optimize);const t=d.toString();g=`${d.scopeRefs(o.default.scope)}return ${t}`,this.opts.code.process&&(g=this.opts.code.process(g,e));const r=new Function(`${o.default.self}`,`${o.default.scope}`,g)(this,this.scope.get());if(this.scope.value(f,{ref:r}),r.errors=null,r.schema=e.schema,r.schemaEnv=e,e.$async&&(r.$async=!0),!0===this.opts.code.source&&(r.source={validateName:f,validateCode:t,scopeValues:d._values}),this.opts.unevaluated){const{props:e,items:t}=m;r.evaluated={props:e instanceof n.Name?void 0:e,items:t instanceof n.Name?void 0:t,dynamicProps:e instanceof n.Name,dynamicItems:t instanceof n.Name},r.source&&(r.source.evaluated=(0,n.stringify)(r.evaluated))}return e.validate=r,e}catch(t){throw delete e.validate,delete e.validateName,g&&this.logger.error("Error compiling schema, function code:",g),t}finally{this._compilations.delete(e)}}function d(e){return(0,a.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:u.call(this,e)}function p(e){for(const t of this._compilations)if(h(t,e))return t}function h(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function f(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||m.call(this,e,t)}function m(e,t){const r=this.opts.uriResolver.parse(t),n=(0,a._getFullPath)(this.opts.uriResolver,r);let s=(0,a.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===s)return y.call(this,r,e);const o=(0,a.normalizeId)(n),i=this.refs[o]||this.schemas[o];if("string"==typeof i){const t=m.call(this,e,i);if("object"!=typeof(null==t?void 0:t.schema))return;return y.call(this,r,t)}if("object"==typeof(null==i?void 0:i.schema)){if(i.validate||u.call(this,i),o===(0,a.normalizeId)(t)){const{schema:t}=i,{schemaId:r}=this.opts,n=t[r];return n&&(s=(0,a.resolveUrl)(this.opts.uriResolver,s,n)),new l({schema:t,schemaId:r,root:e,baseId:s})}return y.call(this,r,i)}}t.SchemaEnv=l,t.compileSchema=u,t.resolveRef=function(e,t,r){var n;r=(0,a.resolveUrl)(this.opts.uriResolver,t,r);const s=e.refs[r];if(s)return s;let o=f.call(this,e,r);if(void 0===o){const s=null===(n=e.localRefs)||void 0===n?void 0:n[r],{schemaId:a}=this.opts;s&&(o=new l({schema:s,schemaId:a,root:e,baseId:t}))}return void 0!==o?e.refs[r]=d.call(this,o):void 0},t.getCompilingSchema=p,t.resolveSchema=m;const g=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function y(e,{baseId:t,schema:r,root:n}){var s;if("/"!==(null===(s=e.fragment)||void 0===s?void 0:s[0]))return;for(const n of e.fragment.slice(1).split("/")){if("boolean"==typeof r)return;const e=r[(0,i.unescapeFragment)(n)];if(void 0===e)return;const s="object"==typeof(r=e)&&r[this.opts.schemaId];!g.has(n)&&s&&(t=(0,a.resolveUrl)(this.opts.uriResolver,t,s))}let o;if("boolean"!=typeof r&&r.$ref&&!(0,i.schemaHasRulesButRef)(r,this.RULES)){const e=(0,a.resolveUrl)(this.opts.uriResolver,t,r.$ref);o=m.call(this,n,e)}const{schemaId:c}=this.opts;return o=o||new l({schema:r,schemaId:c,root:n,baseId:t}),o.schema!==o.root.schema?o:void 0}},5273:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;const n=r(111),s=r(3186);function o(e,t=e.schema){const{opts:r,self:n}=e;if(!r.strictSchema)return;if("boolean"==typeof t)return;const s=n.RULES.keywords;for(const r in t)s[r]||f(e,`unknown keyword: "${r}"`)}function a(e,t){if("boolean"==typeof e)return!e;for(const r in e)if(t[r])return!0;return!1}function i(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function c(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function l({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:s}){return(o,a,i,c)=>{const l=void 0===i?a:i instanceof n.Name?(a instanceof n.Name?e(o,a,i):t(o,a,i),i):a instanceof n.Name?(t(o,i,a),a):r(a,i);return c!==n.Name||l instanceof n.Name?l:s(o,l)}}function u(e,t){if(!0===t)return e.var("props",!0);const r=e.var("props",n._`{}`);return void 0!==t&&d(e,r,t),r}function d(e,t,r){Object.keys(r).forEach(r=>e.assign(n._`${t}${(0,n.getProperty)(r)}`,!0))}t.toHash=function(e){const t={};for(const r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(o(e,t),!a(t,e.self.RULES.all))},t.checkUnknownRules=o,t.schemaHasRules=a,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(const r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,s,o){if(!o){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return n._`${r}`}return n._`${e}${t}${(0,n.getProperty)(s)}`},t.unescapeFragment=function(e){return c(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(i(e))},t.escapeJsonPointer=i,t.unescapeJsonPointer=c,t.eachItem=function(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)},t.mergeEvaluated={props:l({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,()=>{e.if(n._`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,n._`${r} || {}`).code(n._`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,()=>{!0===t?e.assign(r,!0):(e.assign(r,n._`${r} || {}`),d(e,r,t))}),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:u}),items:l({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,()=>e.assign(r,n._`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,()=>e.assign(r,!0===t||n._`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=u,t.setEvaluated=d;const p={};var h;function f(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw new Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:p[t.code]||(p[t.code]=new s._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(h||(t.Type=h={})),t.getErrorPath=function(e,t,r){if(e instanceof n.Name){const s=t===h.Num;return r?s?n._`"[" + ${e} + "]"`:n._`"['" + ${e} + "']"`:s?n._`"/" + ${e}`:n._`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,n.getProperty)(e).toString():"/"+i(e)},t.checkStrictMode=f},5317:e=>{"use strict";e.exports=require("child_process")},5335:(e,t)=>{"use strict";function r(e,t){return t.rules.some(t=>n(e,t))}function n(e,t){var r;return void 0!==e[t.keyword]||(null===(r=t.definition.implements)||void 0===r?void 0:r.some(t=>void 0!==e[t]))}Object.defineProperty(t,"__esModule",{value:!0}),t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0,t.schemaHasRulesForType=function({schema:e,self:t},n){const s=t.RULES.types[n];return s&&!0!==s&&r(e,s)},t.shouldUseGroup=r,t.shouldUseRule=n},5342:(e,t,r)=>{"use strict";const n=r(7075);e.exports=(e,t,r)=>n(e,t,"<",r)},5381:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o=r(2780),a={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>n._`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:a,schema:i,schemaCode:c,it:l}=e;if(!a&&0===i.length)throw new Error("enum must have non-empty array");const u=i.length>=l.opts.loopEnum;let d;const p=()=>null!=d?d:d=(0,s.useFunc)(t,o.default);let h;if(u||a)h=t.let("valid"),e.block$data(h,function(){t.assign(h,!1),t.forOf("v",c,e=>t.if(n._`${p()}(${r}, ${e})`,()=>t.assign(h,!0).break()))});else{if(!Array.isArray(i))throw new Error("ajv implementation error");const e=t.const("vSchema",c);h=(0,n.or)(...i.map((t,s)=>function(e,t){const s=i[t];return"object"==typeof s&&null!==s?n._`${p()}(${r}, ${e}[${t}])`:n._`${r} === ${s}`}(e,s)))}e.pass(h)}};t.default=a},5388:(e,t,r)=>{"use strict";var n=r(1231),s=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],o=["scalar","sequence","mapping"];e.exports=function(e,t){var r,a;if(t=t||{},Object.keys(t).forEach(function(t){if(-1===s.indexOf(t))throw new n('Unknown option "'+t+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(e){return e},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=(r=t.styleAliases||null,a={},null!==r&&Object.keys(r).forEach(function(e){r[e].forEach(function(t){a[String(t)]=e})}),a),-1===o.indexOf(this.kind))throw new n('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}},5465:(e,t,r)=>{"use strict";const n=r(6928),s=r(5021),o=r(2788);e.exports=(e,t)=>(t=Object.assign({cwd:process.cwd()},t),o(e,e=>s(n.resolve(t.cwd,e)),t)),e.exports.sync=(e,t)=>{t=Object.assign({cwd:process.cwd()},t);for(const r of e)if(s.sync(n.resolve(t.cwd,r)))return r}},5489:(e,t,r)=>{"use strict";e.exports=r(1769).extend({implicit:[r(127),r(1851)],explicit:[r(9342),r(6946),r(6942),r(6663)]})},5571:(e,t,r)=>{"use strict";const n=r(7075);e.exports=(e,t,r)=>n(e,t,">",r)},5580:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t,r)=>n(e,t,r)>0},5581:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9810),s=r(1085),o=r(7083),a=r(396),i={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:i,data:c,it:l}=e;"all"===l.opts.removeAdditional&&void 0===i.additionalProperties&&a.default.code(new n.KeywordCxt(l,a.default,"additionalProperties"));const u=(0,s.allSchemaProperties)(r);for(const e of u)l.definedProperties.add(e);l.opts.unevaluated&&u.length&&!0!==l.props&&(l.props=o.mergeEvaluated.props(t,(0,o.toHash)(u),l.props));const d=u.filter(e=>!(0,o.alwaysValidSchema)(l,r[e]));if(0===d.length)return;const p=t.name("valid");for(const r of d)h(r)?f(r):(t.if((0,s.propertyInData)(t,c,r,l.opts.ownProperties)),f(r),l.allErrors||t.else().var(p,!0),t.endIf()),e.it.definedProperties.add(r),e.ok(p);function h(e){return l.opts.useDefaults&&!l.compositeRule&&void 0!==r[e].default}function f(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},p)}}};t.default=i},5583:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const n=r(2796),s=r(3789),o=r(6735),a={message:"boolean schema is false"};function i(e,t){const{gen:r,data:s}=e,o={gen:r,keyword:"false schema",data:s,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,n.reportError)(o,a,void 0,t)}t.topBoolOrEmptySchema=function(e){const{gen:t,schema:r,validateName:n}=e;!1===r?i(e,!1):"object"==typeof r&&!0===r.$async?t.return(o.default.data):(t.assign(s._`${n}.errors`,null),t.return(!0))},t.boolOrEmptySchema=function(e,t){const{gen:r,schema:n}=e;!1===n?(r.var(t,!1),i(e)):r.var(t,!0)}},5590:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignDefaults=void 0;const n=r(3789),s=r(7083);function o(e,t,r){const{gen:o,compositeRule:a,data:i,opts:c}=e;if(void 0===r)return;const l=n._`${i}${(0,n.getProperty)(t)}`;if(a)return void(0,s.checkStrictMode)(e,`default is ignored for: ${l}`);let u=n._`${l} === undefined`;"empty"===c.useDefaults&&(u=n._`${u} || ${l} === null || ${l} === ""`),o.if(u,n._`${l} = ${(0,n.stringify)(r)}`)}t.assignDefaults=function(e,t){const{properties:r,items:n}=e.schema;if("object"===t&&r)for(const t in r)o(e,t,r[t].default);else"array"===t&&Array.isArray(n)&&n.forEach((t,r)=>o(e,r,t.default))}},5694:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(6054),s=r(8682),o=r(6624),a=r(5794),i=r(7939),c=[n.default,s.default,(0,o.default)(),a.default,i.metadataVocabulary,i.contentVocabulary];t.default=c},5753:(e,t,r)=>{"undefined"==typeof process||"renderer"===process.type||!0===process.browser||process.__nwjs?e.exports=r(7833):e.exports=r(6033)},5776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Provider=void 0,t.findFile=function(e,t,r){if(0===e.length)throw(0,n.newError)("No files provided","ERR_UPDATER_NO_FILES_PROVIDED");const s=e.find(e=>e.url.pathname.toLowerCase().endsWith(`.${t}`));return null!=s?s:null==r?e[0]:e.find(e=>!r.some(t=>e.url.pathname.toLowerCase().endsWith(`.${t}`)))},t.parseUpdateInfo=function(e,t,r){if(null==e)throw(0,n.newError)(`Cannot parse update info from ${t} in the latest release artifacts (${r}): rawData: null`,"ERR_UPDATER_INVALID_UPDATE_INFO");let o;try{o=(0,s.load)(e)}catch(s){throw(0,n.newError)(`Cannot parse update info from ${t} in the latest release artifacts (${r}): ${s.stack||s.message}, rawData: ${e}`,"ERR_UPDATER_INVALID_UPDATE_INFO")}return o},t.getFileList=a,t.resolveFiles=function(e,t,r=e=>e){const s=a(e).map(e=>{if(null==e.sha2&&null==e.sha512)throw(0,n.newError)(`Update info doesn't contain nor sha256 neither sha512 checksum: ${(0,n.safeStringifyJson)(e)}`,"ERR_UPDATER_NO_CHECKSUM");return{url:(0,o.newUrlFromBase)(r(e.url),t),info:e}}),i=e.packages,c=null==i?null:i[process.arch]||i.ia32;return null!=c&&(s[0].packageInfo={...c,path:(0,o.newUrlFromBase)(r(c.path),t).href}),s};const n=r(6551),s=r(7210),o=r(906);function a(e){const t=e.files;if(null!=t&&t.length>0)return t;if(null!=e.path)return[{url:e.path,sha2:e.sha2,sha512:e.sha512}];throw(0,n.newError)(`No files provided: ${(0,n.safeStringifyJson)(e)}`,"ERR_UPDATER_NO_FILES_PROVIDED")}t.Provider=class{constructor(e){this.runtimeOptions=e,this.requestHeaders=null,this.executor=e.executor}get isUseMultipleRangeRequest(){return!1!==this.runtimeOptions.isUseMultipleRangeRequest}getChannelFilePrefix(){if("linux"===this.runtimeOptions.platform){const e=process.env.TEST_UPDATER_ARCH||process.arch;return"-linux"+("x64"===e?"":`-${e}`)}return"darwin"===this.runtimeOptions.platform?"-mac":""}getDefaultChannelName(){return this.getCustomChannelName("latest")}getCustomChannelName(e){return`${e}${this.getChannelFilePrefix()}`}get fileExtraDownloadHeaders(){return null}setRequestHeaders(e){this.requestHeaders=e}httpRequest(e,t,r){return this.executor.request(this.createRequestOptions(e,t),r)}createRequestOptions(e,t){const r={};return null==this.requestHeaders?null!=t&&(r.headers=t):r.headers=null==t?this.requestHeaders:{...this.requestHeaders,...t},(0,n.configureRequestUrl)(e,r),r}}},5794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=[r(3491).default];t.default=n},5857:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;const n=r(111),s=r(2036),o=r(7193),a=r(4629),i=r(5273),c=r(8452);class l{constructor(e){var t;let r;this.refs={},this.dynamicAnchors={},"object"==typeof e.schema&&(r=e.schema),this.schema=e.schema,this.schemaId=e.schemaId,this.root=e.root||this,this.baseId=null!==(t=e.baseId)&&void 0!==t?t:(0,a.normalizeId)(null==r?void 0:r[e.schemaId||"$id"]),this.schemaPath=e.schemaPath,this.localRefs=e.localRefs,this.meta=e.meta,this.$async=null==r?void 0:r.$async,this.refs={}}}function u(e){const t=p.call(this,e);if(t)return t;const r=(0,a.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:i,lines:l}=this.opts.code,{ownProperties:u}=this.opts,d=new n.CodeGen(this.scope,{es5:i,lines:l,ownProperties:u});let h;e.$async&&(h=d.scopeValue("Error",{ref:s.default,code:n._`require("ajv/dist/runtime/validation_error").default`}));const f=d.scopeName("validate");e.validateName=f;const m={gen:d,allErrors:this.opts.allErrors,data:o.default.data,parentData:o.default.parentData,parentDataProperty:o.default.parentDataProperty,dataNames:[o.default.data],dataPathArr:[n.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:d.scopeValue("schema",!0===this.opts.code.source?{ref:e.schema,code:(0,n.stringify)(e.schema)}:{ref:e.schema}),validateName:f,ValidationError:h,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:n.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:n._`""`,opts:this.opts,self:this};let g;try{this._compilations.add(e),(0,c.validateFunctionCode)(m),d.optimize(this.opts.code.optimize);const t=d.toString();g=`${d.scopeRefs(o.default.scope)}return ${t}`,this.opts.code.process&&(g=this.opts.code.process(g,e));const r=new Function(`${o.default.self}`,`${o.default.scope}`,g)(this,this.scope.get());if(this.scope.value(f,{ref:r}),r.errors=null,r.schema=e.schema,r.schemaEnv=e,e.$async&&(r.$async=!0),!0===this.opts.code.source&&(r.source={validateName:f,validateCode:t,scopeValues:d._values}),this.opts.unevaluated){const{props:e,items:t}=m;r.evaluated={props:e instanceof n.Name?void 0:e,items:t instanceof n.Name?void 0:t,dynamicProps:e instanceof n.Name,dynamicItems:t instanceof n.Name},r.source&&(r.source.evaluated=(0,n.stringify)(r.evaluated))}return e.validate=r,e}catch(t){throw delete e.validate,delete e.validateName,g&&this.logger.error("Error compiling schema, function code:",g),t}finally{this._compilations.delete(e)}}function d(e){return(0,a.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:u.call(this,e)}function p(e){for(const t of this._compilations)if(h(t,e))return t}function h(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function f(e,t){let r;for(;"string"==typeof(r=this.refs[t]);)t=r;return r||this.schemas[t]||m.call(this,e,t)}function m(e,t){const r=this.opts.uriResolver.parse(t),n=(0,a._getFullPath)(this.opts.uriResolver,r);let s=(0,a.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===s)return y.call(this,r,e);const o=(0,a.normalizeId)(n),i=this.refs[o]||this.schemas[o];if("string"==typeof i){const t=m.call(this,e,i);if("object"!=typeof(null==t?void 0:t.schema))return;return y.call(this,r,t)}if("object"==typeof(null==i?void 0:i.schema)){if(i.validate||u.call(this,i),o===(0,a.normalizeId)(t)){const{schema:t}=i,{schemaId:r}=this.opts,n=t[r];return n&&(s=(0,a.resolveUrl)(this.opts.uriResolver,s,n)),new l({schema:t,schemaId:r,root:e,baseId:s})}return y.call(this,r,i)}}t.SchemaEnv=l,t.compileSchema=u,t.resolveRef=function(e,t,r){var n;r=(0,a.resolveUrl)(this.opts.uriResolver,t,r);const s=e.refs[r];if(s)return s;let o=f.call(this,e,r);if(void 0===o){const s=null===(n=e.localRefs)||void 0===n?void 0:n[r],{schemaId:a}=this.opts;s&&(o=new l({schema:s,schemaId:a,root:e,baseId:t}))}return void 0!==o?e.refs[r]=d.call(this,o):void 0},t.getCompilingSchema=p,t.resolveSchema=m;const g=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function y(e,{baseId:t,schema:r,root:n}){var s;if("/"!==(null===(s=e.fragment)||void 0===s?void 0:s[0]))return;for(const n of e.fragment.slice(1).split("/")){if("boolean"==typeof r)return;const e=r[(0,i.unescapeFragment)(n)];if(void 0===e)return;const s="object"==typeof(r=e)&&r[this.opts.schemaId];!g.has(n)&&s&&(t=(0,a.resolveUrl)(this.opts.uriResolver,t,s))}let o;if("boolean"!=typeof r&&r.$ref&&!(0,i.schemaHasRulesButRef)(r,this.RULES)){const e=(0,a.resolveUrl)(this.opts.uriResolver,t,r.$ref);o=m.call(this,n,e)}const{schemaId:c}=this.opts;return o=o||new l({schema:r,schemaId:c,root:n,baseId:t}),o.schema!==o.root.schema?o:void 0}},5879:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.NsisUpdater=t.MacUpdater=t.RpmUpdater=t.PacmanUpdater=t.DebUpdater=t.AppImageUpdater=t.Provider=t.NoOpLogger=t.AppUpdater=t.BaseUpdater=void 0;const o=r(4652),a=r(6928);var i=r(9322);Object.defineProperty(t,"BaseUpdater",{enumerable:!0,get:function(){return i.BaseUpdater}});var c=r(4718);Object.defineProperty(t,"AppUpdater",{enumerable:!0,get:function(){return c.AppUpdater}}),Object.defineProperty(t,"NoOpLogger",{enumerable:!0,get:function(){return c.NoOpLogger}});var l=r(5776);Object.defineProperty(t,"Provider",{enumerable:!0,get:function(){return l.Provider}});var u=r(6441);Object.defineProperty(t,"AppImageUpdater",{enumerable:!0,get:function(){return u.AppImageUpdater}});var d=r(3378);Object.defineProperty(t,"DebUpdater",{enumerable:!0,get:function(){return d.DebUpdater}});var p=r(1005);Object.defineProperty(t,"PacmanUpdater",{enumerable:!0,get:function(){return p.PacmanUpdater}});var h=r(3916);Object.defineProperty(t,"RpmUpdater",{enumerable:!0,get:function(){return h.RpmUpdater}});var f=r(2658);Object.defineProperty(t,"MacUpdater",{enumerable:!0,get:function(){return f.MacUpdater}});var m=r(776);let g;Object.defineProperty(t,"NsisUpdater",{enumerable:!0,get:function(){return m.NsisUpdater}}),s(r(3765),t),Object.defineProperty(t,"autoUpdater",{enumerable:!0,get:()=>g||function(){if("win32"===process.platform)g=new(r(776).NsisUpdater);else if("darwin"===process.platform)g=new(r(2658).MacUpdater);else{g=new(r(6441).AppImageUpdater);try{const e=a.join(process.resourcesPath,"package-type");if(!(0,o.existsSync)(e))return g;console.info("Checking for beta autoupdate feature for deb/rpm distributions");const t=(0,o.readFileSync)(e).toString().trim();switch(console.info("Found package-type:",t),t){case"deb":g=new(r(3378).DebUpdater);break;case"rpm":g=new(r(3916).RpmUpdater);break;case"pacman":g=new(r(1005).PacmanUpdater)}}catch(e){console.warn("Unable to detect 'package-type' for autoUpdater (beta rpm/deb support). If you'd like to expand support, please consider contributing to electron-builder",e.message)}}return g}()})},5884:e=>{"use strict";e.exports=(e,t=process.argv)=>{const r=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(r+e),s=t.indexOf("--");return-1!==n&&(-1===s||n<s)}},5885:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;const n=r(2104);class s extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`),this.value=e.value}}var o;!function(e){e[e.Started=0]="Started",e[e.Completed=1]="Completed"}(o||(t.UsedValueState=o={})),t.varKinds={const:new n.Name("const"),let:new n.Name("let"),var:new n.Name("var")};class a{constructor({prefixes:e,parent:t}={}){this._names={},this._prefixes=e,this._parent=t}toName(e){return e instanceof n.Name?e:this.name(e)}name(e){return new n.Name(this._newName(e))}_newName(e){return`${e}${(this._names[e]||this._nameGroup(e)).index++}`}_nameGroup(e){var t,r;if((null===(r=null===(t=this._parent)||void 0===t?void 0:t._prefixes)||void 0===r?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e))throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`);return this._names[e]={prefix:e,index:0}}}t.Scope=a;class i extends n.Name{constructor(e,t){super(t),this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e,this.scopePath=n._`.${new n.Name(t)}[${r}]`}}t.ValueScopeName=i;const c=n._`\n`;t.ValueScope=class extends a{constructor(e){super(e),this._values={},this._scope=e.scope,this.opts={...e,_n:e.lines?c:n.nil}}get(){return this._scope}name(e){return new i(e,this._newName(e))}value(e,t){var r;if(void 0===t.ref)throw new Error("CodeGen: ref must be passed in value");const n=this.toName(e),{prefix:s}=n,o=null!==(r=t.key)&&void 0!==r?r:t.ref;let a=this._values[s];if(a){const e=a.get(o);if(e)return e}else a=this._values[s]=new Map;a.set(o,n);const i=this._scope[s]||(this._scope[s]=[]),c=i.length;return i[c]=t.ref,n.setValue(t,{property:s,itemIndex:c}),n}getValue(e,t){const r=this._values[e];if(r)return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,t=>{if(void 0===t.scopePath)throw new Error(`CodeGen: name "${t}" has no value`);return n._`${e}${t.scopePath}`})}scopeCode(e=this._values,t,r){return this._reduceValues(e,e=>{if(void 0===e.value)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code},t,r)}_reduceValues(e,r,a={},i){let c=n.nil;for(const l in e){const u=e[l];if(!u)continue;const d=a[l]=a[l]||new Map;u.forEach(e=>{if(d.has(e))return;d.set(e,o.Started);let a=r(e);if(a){const r=this.opts.es5?t.varKinds.var:t.varKinds.const;c=n._`${c}${r} ${e} = ${a};${this.opts._n}`}else{if(!(a=null==i?void 0:i(e)))throw new s(e);c=n._`${c}${a}${this.opts._n}`}d.set(e,o.Completed)})}return c}}},5949:e=>{"use strict";e.exports={rE:"1.0.0"}},6033:(e,t,r)=>{const n=r(2018),s=r(9023);t.init=function(e){e.inspectOpts={};const r=Object.keys(t.inspectOpts);for(let n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(...e){return process.stderr.write(s.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(r){const{namespace:n,useColors:s}=this;if(s){const t=this.color,s="[3"+(t<8?t:"8;5;"+t),o=`  ${s};1m${n} [0m`;r[0]=o+r[0].split("\n").join("\n"+o),r.push(s+"m+"+e.exports.humanize(this.diff)+"[0m")}else r[0]=(t.inspectOpts.hideDate?"":(new Date).toISOString()+" ")+n+" "+r[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?Boolean(t.inspectOpts.colors):n.isatty(process.stderr.fd)},t.destroy=s.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{const e=r(7687);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{const r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase());let n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{}),e.exports=r(736)(t);const{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts)}},6034:(e,t,r)=>{"use strict";const n=r(8626),s=new WeakMap,o=(e,t={})=>{if("function"!=typeof e)throw new TypeError("Expected a function");let r,o=0;const a=e.displayName||e.name||"<anonymous>",i=function(...n){if(s.set(i,++o),1===o)r=e.apply(this,n),e=null;else if(!0===t.throw)throw new Error(`Function \`${a}\` can only be called once`);return r};return n(i,e),s.set(i,o),i};e.exports=o,e.exports.default=o,e.exports.callCount=e=>{if(!s.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return s.get(e)}},6054:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9741),s=r(9623),o=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",n.default,s.default];t.default=o},6056:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var n=r(8452);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return n.KeywordCxt}});var s=r(111);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return s._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return s.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return s.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return s.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return s.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return s.CodeGen}});const o=r(2036),a=r(2877),i=r(2054),c=r(5857),l=r(111),u=r(4629),d=r(2754),p=r(5273),h=r(4483),f=r(1390),m=(e,t)=>new RegExp(e,t);m.code="new RegExp";const g=["removeAdditional","useDefaults","coerceTypes"],y=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),v={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},w={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};function _(e){var t,r,n,s,o,a,i,c,l,u,d,p,h,g,y,v,w,_,b,$,E,S,P,O,C;const N=e.strict,I=null===(t=e.code)||void 0===t?void 0:t.optimize,T=!0===I||void 0===I?1:I||0,A=null!==(n=null===(r=e.code)||void 0===r?void 0:r.regExp)&&void 0!==n?n:m,k=null!==(s=e.uriResolver)&&void 0!==s?s:f.default;return{strictSchema:null===(a=null!==(o=e.strictSchema)&&void 0!==o?o:N)||void 0===a||a,strictNumbers:null===(c=null!==(i=e.strictNumbers)&&void 0!==i?i:N)||void 0===c||c,strictTypes:null!==(u=null!==(l=e.strictTypes)&&void 0!==l?l:N)&&void 0!==u?u:"log",strictTuples:null!==(p=null!==(d=e.strictTuples)&&void 0!==d?d:N)&&void 0!==p?p:"log",strictRequired:null!==(g=null!==(h=e.strictRequired)&&void 0!==h?h:N)&&void 0!==g&&g,code:e.code?{...e.code,optimize:T,regExp:A}:{optimize:T,regExp:A},loopRequired:null!==(y=e.loopRequired)&&void 0!==y?y:200,loopEnum:null!==(v=e.loopEnum)&&void 0!==v?v:200,meta:null===(w=e.meta)||void 0===w||w,messages:null===(_=e.messages)||void 0===_||_,inlineRefs:null===(b=e.inlineRefs)||void 0===b||b,schemaId:null!==($=e.schemaId)&&void 0!==$?$:"$id",addUsedSchema:null===(E=e.addUsedSchema)||void 0===E||E,validateSchema:null===(S=e.validateSchema)||void 0===S||S,validateFormats:null===(P=e.validateFormats)||void 0===P||P,unicodeRegExp:null===(O=e.unicodeRegExp)||void 0===O||O,int32range:null===(C=e.int32range)||void 0===C||C,uriResolver:k}}class b{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,..._(e)};const{es5:t,lines:r}=this.opts.code;this.scope=new l.ValueScope({scope:{},prefixes:y,es5:t,lines:r}),this.logger=function(e){if(!1===e)return N;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}(e.logger);const n=e.validateFormats;e.validateFormats=!1,this.RULES=(0,i.getRules)(),$.call(this,v,e,"NOT SUPPORTED"),$.call(this,w,e,"DEPRECATED","warn"),this._metaOpts=C.call(this),e.formats&&P.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&O.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),S.call(this),e.validateFormats=n}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let n=h;"id"===r&&(n={...h},n.id=n.$id,delete n.$id),t&&e&&this.addMetaSchema(n,n[r],!1)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(r=this.getSchema(e),!r)throw new Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);const n=r(t);return"$async"in r||(this.errors=r.errors),n}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw new Error("options.loadSchema should be a function");const{loadSchema:r}=this.opts;return n.call(this,e,t);async function n(e,t){await s.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||o.call(this,r)}async function s(e){e&&!this.getSchema(e)&&await n.call(this,{$ref:e},!0)}async function o(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof a.default))throw t;return i.call(this,t),await c.call(this,t.missingSchema),o.call(this,e)}}function i({missingSchema:e,missingRef:t}){if(this.refs[e])throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function c(e){const r=await l.call(this,e);this.refs[e]||await s.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function l(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,n=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,void 0,r,n);return this}let s;if("object"==typeof e){const{schemaId:t}=this.opts;if(s=e[t],void 0!==s&&"string"!=typeof s)throw new Error(`schema ${t} must be string`)}return t=(0,u.normalizeId)(t||s),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,n,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){if("boolean"==typeof e)return!0;let r;if(r=e.$schema,void 0!==r&&"string"!=typeof r)throw new Error("$schema must be a string");if(r=r||this.opts.defaultMeta||this.defaultMeta(),!r)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const n=this.validate(r,e);if(!n&&t){const e="schema is invalid: "+this.errorsText();if("log"!==this.opts.validateSchema)throw new Error(e);this.logger.error(e)}return n}getSchema(e){let t;for(;"string"==typeof(t=E.call(this,e));)e=t;if(void 0===t){const{schemaId:r}=this.opts,n=new c.SchemaEnv({schema:{},schemaId:r});if(t=c.resolveSchema.call(this,n,e),!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const t=E.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{const t=e;this._cache.delete(t);let r=e[this.opts.schemaId];return r&&(r=(0,u.normalizeId)(r),delete this.schemas[r],delete this.refs[r]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else{if("object"!=typeof e||void 0!==t)throw new Error("invalid addKeywords parameters");if(r=(t=e).keyword,Array.isArray(r)&&!r.length)throw new Error("addKeywords: keyword must be string or non-empty array")}if(T.call(this,r,t),!t)return(0,p.eachItem)(r,e=>A.call(this,e)),this;R.call(this,t);const n={...t,type:(0,d.getJSONTypes)(t.type),schemaType:(0,d.getJSONTypes)(t.schemaType)};return(0,p.eachItem)(r,0===n.type.length?e=>A.call(this,e,n):e=>n.type.forEach(t=>A.call(this,e,n,t))),this}getKeyword(e){const t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e],delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex(t=>t.keyword===e);t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map(e=>`${r}${e.instancePath} ${e.message}`).reduce((e,r)=>e+t+r):"No errors"}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const n of t){const t=n.split("/").slice(1);let s=e;for(const e of t)s=s[e];for(const e in r){const t=r[e];if("object"!=typeof t)continue;const{$data:n}=t.definition,o=s[e];n&&o&&(s[e]=D(o))}}return e}_removeAllSchemas(e,t){for(const r in e){const n=e[r];t&&!t.test(r)||("string"==typeof n?delete e[r]:n&&!n.meta&&(this._cache.delete(n.schema),delete e[r]))}}_addSchema(e,t,r,n=this.opts.validateSchema,s=this.opts.addUsedSchema){let o;const{schemaId:a}=this.opts;if("object"==typeof e)o=e[a];else{if(this.opts.jtd)throw new Error("schema must be object");if("boolean"!=typeof e)throw new Error("schema must be object or boolean")}let i=this._cache.get(e);if(void 0!==i)return i;r=(0,u.normalizeId)(o||r);const l=u.getSchemaRefs.call(this,e,r);return i=new c.SchemaEnv({schema:e,schemaId:a,meta:t,baseId:r,localRefs:l}),this._cache.set(i.schema,i),s&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=i),n&&this.validateSchema(e,!0),i}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw new Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):c.compileSchema.call(this,e),!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{c.compileSchema.call(this,e)}finally{this.opts=t}}}function $(e,t,r,n="error"){for(const s in e){const o=s;o in t&&this.logger[n](`${r}: option ${s}. ${e[o]}`)}}function E(e){return e=(0,u.normalizeId)(e),this.schemas[e]||this.refs[e]}function S(){const e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function P(){for(const e in this.opts.formats){const t=this.opts.formats[e];t&&this.addFormat(e,t)}}function O(e){if(Array.isArray(e))this.addVocabulary(e);else{this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}}function C(){const e={...this.opts};for(const t of g)delete e[t];return e}b.ValidationError=o.default,b.MissingRefError=a.default,t.default=b;const N={log(){},warn(){},error(){}},I=/^[a-z_$][a-z0-9_$:-]*$/i;function T(e,t){const{RULES:r}=this;if((0,p.eachItem)(e,e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!I.test(e))throw new Error(`Keyword ${e} has invalid name`)}),t&&t.$data&&!("code"in t)&&!("validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function A(e,t,r){var n;const s=null==t?void 0:t.post;if(r&&s)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:o}=this;let a=s?o.post:o.rules.find(({type:e})=>e===r);if(a||(a={type:r,rules:[]},o.rules.push(a)),o.keywords[e]=!0,!t)return;const i={keyword:e,definition:{...t,type:(0,d.getJSONTypes)(t.type),schemaType:(0,d.getJSONTypes)(t.schemaType)}};t.before?k.call(this,a,i,t.before):a.rules.push(i),o.all[e]=i,null===(n=t.implements)||void 0===n||n.forEach(e=>this.addKeyword(e))}function k(e,t,r){const n=e.rules.findIndex(e=>e.keyword===r);n>=0?e.rules.splice(n,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function R(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=D(t)),e.validateSchema=this.compile(t,!0))}const x={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function D(e){return{anyOf:[e,x]}}},6146:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DigestTransform=t.HttpExecutor=t.HttpError=void 0,t.createHttpError=p,t.parseJson=function(e){return e.then(e=>null==e||0===e.length?null:JSON.parse(e))},t.configureRequestOptionsFromUrl=g,t.configureRequestUrl=y,t.safeGetHeader=w,t.configureRequestOptions=_,t.safeStringifyJson=b;const n=r(6982),s=r(5753),o=r(9896),a=r(2203),i=r(7016),c=r(3411),l=r(4261),u=r(9067),d=(0,s.default)("electron-builder");function p(e,t=null){return new f(e.statusCode||-1,`${e.statusCode} ${e.statusMessage}`+(null==t?"":"\n"+JSON.stringify(t,null,"  "))+"\nHeaders: "+b(e.headers),t)}const h=new Map([[429,"Too many requests"],[400,"Bad request"],[403,"Forbidden"],[404,"Not found"],[405,"Method not allowed"],[406,"Not acceptable"],[408,"Request timeout"],[413,"Request entity too large"],[500,"Internal server error"],[502,"Bad gateway"],[503,"Service unavailable"],[504,"Gateway timeout"],[505,"HTTP version not supported"]]);class f extends Error{constructor(e,t=`HTTP error: ${h.get(e)||e}`,r=null){super(t),this.statusCode=e,this.description=r,this.name="HttpError",this.code=`HTTP_ERROR_${e}`}isServerError(){return this.statusCode>=500&&this.statusCode<=599}}t.HttpError=f;class m{constructor(){this.maxRedirects=10}request(e,t=new c.CancellationToken,r){_(e);const n=null==r?void 0:JSON.stringify(r),s=n?Buffer.from(n):void 0;if(null!=s){d(n);const{headers:t,...r}=e;e={method:"post",headers:{"Content-Type":"application/json","Content-Length":s.length,...t},...r}}return this.doApiRequest(e,t,e=>e.end(s))}doApiRequest(e,t,r,n=0){return d.enabled&&d(`Request: ${b(e)}`),t.createPromise((s,o,a)=>{const i=this.createRequest(e,a=>{try{this.handleResponse(a,e,t,s,o,n,r)}catch(e){o(e)}});this.addErrorAndTimeoutHandlers(i,o,e.timeout),this.addRedirectHandlers(i,e,o,n,e=>{this.doApiRequest(e,t,r,n).then(s).catch(o)}),r(i,o),a(()=>i.abort())})}addRedirectHandlers(e,t,r,n,s){}addErrorAndTimeoutHandlers(e,t,r=6e4){this.addTimeOutHandler(e,t,r),e.on("error",t),e.on("aborted",()=>{t(new Error("Request has been aborted by the server"))})}handleResponse(e,t,r,n,s,o,a){var i;if(d.enabled&&d(`Response: ${e.statusCode} ${e.statusMessage}, request options: ${b(t)}`),404===e.statusCode)return void s(p(e,`method: ${t.method||"GET"} url: ${t.protocol||"https:"}//${t.hostname}${t.port?`:${t.port}`:""}${t.path}\n\nPlease double check that your authentication token is correct. Due to security reasons, actual status maybe not reported, but 404.\n`));if(204===e.statusCode)return void n();const c=null!==(i=e.statusCode)&&void 0!==i?i:0,l=c>=300&&c<400,u=w(e,"location");if(l&&null!=u)return o>this.maxRedirects?void s(this.createMaxRedirectError()):void this.doApiRequest(m.prepareRedirectUrlOptions(u,t),r,a,o).then(n).catch(s);e.setEncoding("utf8");let h="";e.on("error",s),e.on("data",e=>h+=e),e.on("end",()=>{try{if(null!=e.statusCode&&e.statusCode>=400){const r=w(e,"content-type"),n=null!=r&&(Array.isArray(r)?null!=r.find(e=>e.includes("json")):r.includes("json"));s(p(e,`method: ${t.method||"GET"} url: ${t.protocol||"https:"}//${t.hostname}${t.port?`:${t.port}`:""}${t.path}\n\n          Data:\n          ${n?JSON.stringify(JSON.parse(h)):h}\n          `))}else n(0===h.length?null:h)}catch(e){s(e)}})}async downloadToBuffer(e,t){return await t.cancellationToken.createPromise((r,n,s)=>{const o=[],a={headers:t.headers||void 0,redirect:"manual"};y(e,a),_(a),this.doDownload(a,{destination:null,options:t,onCancel:s,callback:e=>{null==e?r(Buffer.concat(o)):n(e)},responseHandler:(e,t)=>{let r=0;e.on("data",e=>{r+=e.length,r>524288e3?t(new Error("Maximum allowed size is 500 MB")):o.push(e)}),e.on("end",()=>{t(null)})}},0)})}doDownload(e,t,r){const n=this.createRequest(e,n=>{if(n.statusCode>=400)return void t.callback(new Error(`Cannot download "${e.protocol||"https:"}//${e.hostname}${e.path}", status ${n.statusCode}: ${n.statusMessage}`));n.on("error",t.callback);const s=w(n,"location");null==s?null==t.responseHandler?function(e,t){if(r=w(t,"X-Checksum-Sha2"),n=e.options.sha2,s=e.callback,null!=r&&null!=n&&r!==n&&(s(new Error(`checksum mismatch: expected ${n} but got ${r} (X-Checksum-Sha2 header)`)),1))return;var r,n,s;const a=[];if(null!=e.options.onProgress){const r=w(t,"content-length");null!=r&&a.push(new u.ProgressCallbackTransform(parseInt(r,10),e.options.cancellationToken,e.options.onProgress))}const i=e.options.sha512;null!=i?a.push(new v(i,"sha512",128!==i.length||i.includes("+")||i.includes("Z")||i.includes("=")?"base64":"hex")):null!=e.options.sha2&&a.push(new v(e.options.sha2,"sha256","hex"));const c=(0,o.createWriteStream)(e.destination);a.push(c);let l=t;for(const t of a)t.on("error",t=>{c.close(),e.options.cancellationToken.cancelled||e.callback(t)}),l=l.pipe(t);c.on("finish",()=>{c.close(e.callback)})}(t,n):t.responseHandler(n,t.callback):r<this.maxRedirects?this.doDownload(m.prepareRedirectUrlOptions(s,e),t,r++):t.callback(this.createMaxRedirectError())});this.addErrorAndTimeoutHandlers(n,t.callback,e.timeout),this.addRedirectHandlers(n,e,t.callback,r,e=>{this.doDownload(e,t,r++)}),n.end()}createMaxRedirectError(){return new Error(`Too many redirects (> ${this.maxRedirects})`)}addTimeOutHandler(e,t,r){e.on("socket",n=>{n.setTimeout(r,()=>{e.abort(),t(new Error("Request timed out"))})})}static prepareRedirectUrlOptions(e,t){const r=g(e,{...t}),n=r.headers;if(null==n?void 0:n.authorization){const t=new i.URL(e);(t.hostname.endsWith(".amazonaws.com")||t.searchParams.has("X-Amz-Credential"))&&delete n.authorization}return r}static retryOnServerError(e,t=3){for(let r=0;;r++)try{return e()}catch(e){if(r<t&&(e instanceof f&&e.isServerError()||"EPIPE"===e.code))continue;throw e}}}function g(e,t){const r=_(t);return y(new i.URL(e),r),r}function y(e,t){t.protocol=e.protocol,t.hostname=e.hostname,e.port?t.port=e.port:t.port&&delete t.port,t.path=e.pathname+e.search}t.HttpExecutor=m;class v extends a.Transform{get actual(){return this._actual}constructor(e,t="sha512",r="base64"){super(),this.expected=e,this.algorithm=t,this.encoding=r,this._actual=null,this.isValidateOnEnd=!0,this.digester=(0,n.createHash)(t)}_transform(e,t,r){this.digester.update(e),r(null,e)}_flush(e){if(this._actual=this.digester.digest(this.encoding),this.isValidateOnEnd)try{this.validate()}catch(t){return void e(t)}e(null)}validate(){if(null==this._actual)throw(0,l.newError)("Not finished yet","ERR_STREAM_NOT_FINISHED");if(this._actual!==this.expected)throw(0,l.newError)(`${this.algorithm} checksum mismatch, expected ${this.expected}, got ${this._actual}`,"ERR_CHECKSUM_MISMATCH");return null}}function w(e,t){const r=e.headers[t];return null==r?null:Array.isArray(r)?0===r.length?null:r[r.length-1]:r}function _(e,t,r){null!=r&&(e.method=r),e.headers={...e.headers};const n=e.headers;return null!=t&&(n.authorization=t.startsWith("Basic")||t.startsWith("Bearer")?t:`token ${t}`),null==n["User-Agent"]&&(n["User-Agent"]="electron-builder"),null!=r&&"GET"!==r&&null!=n["Cache-Control"]||(n["Cache-Control"]="no-cache"),null==e.protocol&&null!=process.versions.electron&&(e.protocol="https:"),e}function b(e,t){return JSON.stringify(e,(e,r)=>e.endsWith("Authorization")||e.endsWith("authorization")||e.endsWith("Password")||e.endsWith("PASSWORD")||e.endsWith("Token")||e.includes("password")||e.includes("token")||null!=t&&t.has(e)?"<stripped sensitive data>":r,2)}t.DigestTransform=v},6166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o=r(1085),a=r(7065),i={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:n}=e,{prefixItems:i}=r;n.items=!0,(0,s.alwaysValidSchema)(n,t)||(i?(0,a.validateAdditionalItems)(e,i):e.ok((0,o.validateArray)(e)))}};t.default=i},6170:(e,t,r)=>{"use strict";const n=r(3908),s=r(144),{safeRe:o,t:a}=r(9718);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){const n=t.includePrerelease?o[a.COERCERTLFULL]:o[a.COERCERTL];let s;for(;(s=n.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&s.index+s[0].length===r.index+r[0].length||(r=s),n.lastIndex=s.index+s[1].length+s[2].length;n.lastIndex=-1}else r=e.match(t.includePrerelease?o[a.COERCEFULL]:o[a.COERCE]);if(null===r)return null;const i=r[2],c=r[3]||"0",l=r[4]||"0",u=t.includePrerelease&&r[5]?`-${r[5]}`:"",d=t.includePrerelease&&r[6]?`+${r[6]}`:"";return s(`${i}.${c}.${l}${u}${d}`,t)}},6176:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getAppCacheDir=function(){const e=(0,s.homedir)();let t;return t="win32"===process.platform?process.env.LOCALAPPDATA||n.join(e,"AppData","Local"):"darwin"===process.platform?n.join(e,"Library","Caches"):process.env.XDG_CACHE_HOME||n.join(e,".cache"),t};const n=r(6928),s=r(857)},6184:(e,t,r)=>{"use strict";e.exports=r(7759).extend({implicit:[r(9198),r(6199),r(4466),r(1461)]})},6199:(e,t,r)=>{"use strict";var n=r(5388);e.exports=new n("tag:yaml.org,2002:bool",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t=e.length;return 4===t&&("true"===e||"True"===e||"TRUE"===e)||5===t&&("false"===e||"False"===e||"FALSE"===e)},construct:function(e){return"true"===e||"True"===e||"TRUE"===e},predicate:function(e){return"[object Boolean]"===Object.prototype.toString.call(e)},represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"})},6254:(e,t,r)=>{"use strict";const n=r(3908);e.exports=(e,t)=>new n(e,t).minor},6284:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(4763),s={isChangeErrorOk:e=>{const{code:t}=e;return"ENOSYS"===t||!(n.IS_USER_ROOT||"EINVAL"!==t&&"EPERM"!==t)},isRetriableError:e=>{const{code:t}=e;return"EMFILE"===t||"ENFILE"===t||"EAGAIN"===t||"EBUSY"===t||"EACCESS"===t||"EACCS"===t||"EPERM"===t},onChangeError:e=>{if(!s.isChangeErrorOk(e))throw e}};t.default=s},6441:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AppImageUpdater=void 0;const n=r(6551),s=r(5317),o=r(4652),a=r(9896),i=r(6928),c=r(9322),l=r(1972),u=r(5776),d=r(3765);class p extends c.BaseUpdater{constructor(e,t){super(e,t)}isUpdaterActive(){return null==process.env.APPIMAGE?(null==process.env.SNAP?this._logger.warn("APPIMAGE env is not defined, current application is not an AppImage"):this._logger.info("SNAP env is defined, updater is disabled"),!1):super.isUpdaterActive()}doDownloadUpdate(e){const t=e.updateInfoAndProvider.provider,r=(0,u.findFile)(t.resolveFiles(e.updateInfoAndProvider.info),"AppImage",["rpm","deb","pacman"]);return this.executeDownload({fileExtension:"AppImage",fileInfo:r,downloadUpdateOptions:e,task:async(s,a)=>{const i=process.env.APPIMAGE;if(null==i)throw(0,n.newError)("APPIMAGE env is not defined","ERR_UPDATER_OLD_FILE_NOT_FOUND");(e.disableDifferentialDownload||await this.downloadDifferential(r,i,s,t,e))&&await this.httpExecutor.download(r.url,s,a),await(0,o.chmod)(s,493)}})}async downloadDifferential(e,t,r,n,s){try{const o={newUrl:e.url,oldFile:t,logger:this._logger,newFile:r,isUseMultipleRangeRequest:n.isUseMultipleRangeRequest,requestHeaders:s.requestHeaders,cancellationToken:s.cancellationToken};return this.listenerCount(d.DOWNLOAD_PROGRESS)>0&&(o.onProgress=e=>this.emit(d.DOWNLOAD_PROGRESS,e)),await new l.FileWithEmbeddedBlockMapDifferentialDownloader(e.info,this.httpExecutor,o).download(),!1}catch(e){return this._logger.error(`Cannot download differentially, fallback to full download: ${e.stack||e}`),"linux"===process.platform}}doInstall(e){const t=process.env.APPIMAGE;if(null==t)throw(0,n.newError)("APPIMAGE env is not defined","ERR_UPDATER_OLD_FILE_NOT_FOUND");let r;(0,a.unlinkSync)(t);const o=i.basename(t),c=this.installerPath;if(null==c)return this.dispatchError(new Error("No valid update available, can't quit and install")),!1;r=i.basename(c)!==o&&/\d+\.\d+\.\d+/.test(o)?i.join(i.dirname(t),i.basename(c)):t,(0,s.execFileSync)("mv",["-f",c,r]),r!==t&&this.emit("appimage-filename-updated",r);const l={...process.env,APPIMAGE_SILENT_INSTALL:"true"};return e.isForceRunAfter?this.spawnLog(r,[],l):(l.APPIMAGE_EXIT_AFTER_INSTALL="true",(0,s.execFileSync)(r,[],{env:l})),!0}}t.AppImageUpdater=p},6461:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatLimitDefinition=void 0;const n=r(2394),s=r(3789),o=s.operators,a={formatMaximum:{okStr:"<=",ok:o.LTE,fail:o.GT},formatMinimum:{okStr:">=",ok:o.GTE,fail:o.LT},formatExclusiveMaximum:{okStr:"<",ok:o.LT,fail:o.GTE},formatExclusiveMinimum:{okStr:">",ok:o.GT,fail:o.LTE}},i={message:({keyword:e,schemaCode:t})=>s.str`should be ${a[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>s._`{comparison: ${a[e].okStr}, limit: ${t}}`};t.formatLimitDefinition={keyword:Object.keys(a),type:"string",schemaType:"string",$data:!0,error:i,code(e){const{gen:t,data:r,schemaCode:o,keyword:i,it:c}=e,{opts:l,self:u}=c;if(!l.validateFormats)return;const d=new n.KeywordCxt(c,u.RULES.all.format.definition,"format");function p(e){return s._`${e}.compare(${r}, ${o}) ${a[i].fail} 0`}d.$data?function(){const r=t.scopeValue("formats",{ref:u.formats,code:l.code.formats}),n=t.const("fmt",s._`${r}[${d.schemaCode}]`);e.fail$data(s.or(s._`typeof ${n} != "object"`,s._`${n} instanceof RegExp`,s._`typeof ${n}.compare != "function"`,p(n)))}():function(){const r=d.schema,n=u.formats[r];if(!n||!0===n)return;if("object"!=typeof n||n instanceof RegExp||"function"!=typeof n.compare)throw new Error(`"${i}": format "${r}" does not define "compare" function`);const o=t.scopeValue("formats",{key:r,ref:n,code:l.code.formats?s._`${l.code.formats}${s.getProperty(r)}`:void 0});e.fail$data(p(o))}()},dependencies:["format"]},t.default=e=>(e.addKeyword(t.formatLimitDefinition),e)},6492:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o=r(1039),a=r(8831),i={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:n}=e,{prefixItems:i}=r;n.items=!0,(0,s.alwaysValidSchema)(n,t)||(i?(0,a.validateAdditionalItems)(e,i):e.ok((0,o.validateArray)(e)))}};t.default=i},6551:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CURRENT_APP_PACKAGE_FILE_NAME=t.CURRENT_APP_INSTALLER_FILE_NAME=t.XElement=t.parseXml=t.UUID=t.parseDn=t.retry=t.githubUrl=t.getS3LikeProviderBaseUrl=t.ProgressCallbackTransform=t.MemoLazy=t.safeStringifyJson=t.safeGetHeader=t.parseJson=t.HttpExecutor=t.HttpError=t.DigestTransform=t.createHttpError=t.configureRequestUrl=t.configureRequestOptionsFromUrl=t.configureRequestOptions=t.newError=t.CancellationToken=t.CancellationError=void 0,t.asArray=function(e){return null==e?[]:Array.isArray(e)?e:[e]};var n=r(3411);Object.defineProperty(t,"CancellationError",{enumerable:!0,get:function(){return n.CancellationError}}),Object.defineProperty(t,"CancellationToken",{enumerable:!0,get:function(){return n.CancellationToken}});var s=r(4261);Object.defineProperty(t,"newError",{enumerable:!0,get:function(){return s.newError}});var o=r(6146);Object.defineProperty(t,"configureRequestOptions",{enumerable:!0,get:function(){return o.configureRequestOptions}}),Object.defineProperty(t,"configureRequestOptionsFromUrl",{enumerable:!0,get:function(){return o.configureRequestOptionsFromUrl}}),Object.defineProperty(t,"configureRequestUrl",{enumerable:!0,get:function(){return o.configureRequestUrl}}),Object.defineProperty(t,"createHttpError",{enumerable:!0,get:function(){return o.createHttpError}}),Object.defineProperty(t,"DigestTransform",{enumerable:!0,get:function(){return o.DigestTransform}}),Object.defineProperty(t,"HttpError",{enumerable:!0,get:function(){return o.HttpError}}),Object.defineProperty(t,"HttpExecutor",{enumerable:!0,get:function(){return o.HttpExecutor}}),Object.defineProperty(t,"parseJson",{enumerable:!0,get:function(){return o.parseJson}}),Object.defineProperty(t,"safeGetHeader",{enumerable:!0,get:function(){return o.safeGetHeader}}),Object.defineProperty(t,"safeStringifyJson",{enumerable:!0,get:function(){return o.safeStringifyJson}});var a=r(3619);Object.defineProperty(t,"MemoLazy",{enumerable:!0,get:function(){return a.MemoLazy}});var i=r(9067);Object.defineProperty(t,"ProgressCallbackTransform",{enumerable:!0,get:function(){return i.ProgressCallbackTransform}});var c=r(2128);Object.defineProperty(t,"getS3LikeProviderBaseUrl",{enumerable:!0,get:function(){return c.getS3LikeProviderBaseUrl}}),Object.defineProperty(t,"githubUrl",{enumerable:!0,get:function(){return c.githubUrl}});var l=r(1081);Object.defineProperty(t,"retry",{enumerable:!0,get:function(){return l.retry}});var u=r(9345);Object.defineProperty(t,"parseDn",{enumerable:!0,get:function(){return u.parseDn}});var d=r(1868);Object.defineProperty(t,"UUID",{enumerable:!0,get:function(){return d.UUID}});var p=r(9318);Object.defineProperty(t,"parseXml",{enumerable:!0,get:function(){return p.parseXml}}),Object.defineProperty(t,"XElement",{enumerable:!0,get:function(){return p.XElement}}),t.CURRENT_APP_INSTALLER_FILE_NAME="installer.exe",t.CURRENT_APP_PACKAGE_FILE_NAME="package.7z"},6578:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.verifySignature=function(e,t,r){return new Promise((o,c)=>{const l=t.replace(/'/g,"''");r.info(`Verifying signature ${l}`),(0,s.execFile)('set "PSModulePath=" & chcp 65001 >NUL & powershell.exe',["-NoProfile","-NonInteractive","-InputFormat","None","-Command",`"Get-AuthenticodeSignature -LiteralPath '${l}' | ConvertTo-Json -Compress"`],{shell:!0,timeout:2e4},(s,l,u)=>{var d;try{if(null!=s||u)return i(r,s,u,c),void o(null);const p=function(e){const t=JSON.parse(e);delete t.PrivateKey,delete t.IsOSBinary,delete t.SignatureType;const r=t.SignerCertificate;return null!=r&&(delete r.Archived,delete r.Extensions,delete r.Handle,delete r.HasPrivateKey,delete r.SubjectName),t}(l);if(0===p.Status){try{const e=a.normalize(p.Path),n=a.normalize(t);if(r.info(`LiteralPath: ${e}. Update Path: ${n}`),e!==n)return i(r,new Error(`LiteralPath of ${e} is different than ${n}`),u,c),void o(null)}catch(s){r.warn(`Unable to verify LiteralPath of update asset due to missing data.Path. Skipping this step of validation. Message: ${null!==(d=s.message)&&void 0!==d?d:s.stack}`)}const l=(0,n.parseDn)(p.SignerCertificate.Subject);let h=!1;for(const t of e){const e=(0,n.parseDn)(t);if(e.size?h=Array.from(e.keys()).every(t=>e.get(t)===l.get(t)):t===l.get("CN")&&(r.warn(`Signature validated using only CN ${t}. Please add your full Distinguished Name (DN) to publisherNames configuration`),h=!0),h)return void o(null)}}const h=`publisherNames: ${e.join(" | ")}, raw info: `+JSON.stringify(p,(e,t)=>"RawData"===e?void 0:t,2);r.warn(`Sign verification failed, installer signed with incorrect certificate: ${h}`),o(h)}catch(e){return i(r,e,null,c),void o(null)}})})};const n=r(6551),s=r(5317),o=r(857),a=r(6928);function i(e,t,r,n){if(function(){const e=o.release();return e.startsWith("6.")&&!e.startsWith("6.3")}())e.warn(`Cannot execute Get-AuthenticodeSignature: ${t||r}. Ignoring signature validation due to unsupported powershell version. Please upgrade to powershell 3 or higher.`);else{try{(0,s.execFileSync)("powershell.exe",["-NoProfile","-NonInteractive","-Command","ConvertTo-Json test"],{timeout:1e4})}catch(t){return void e.warn(`Cannot execute ConvertTo-Json: ${t.message}. Ignoring signature validation due to unsupported powershell version. Please upgrade to powershell 3 or higher.`)}null!=t&&n(t),r&&n(new Error(`Cannot execute Get-AuthenticodeSignature, stderr: ${r}. Failing signature validation due to unknown stderr.`))}}},6585:e=>{var t=1e3,r=60*t,n=60*r,s=24*n,o=7*s;function a(e,t,r,n){var s=t>=1.5*r;return Math.round(e/r)+" "+n+(s?"s":"")}e.exports=function(e,i){i=i||{};var c,l,u=typeof e;if("string"===u&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(a){var i=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*i;case"weeks":case"week":case"w":return i*o;case"days":case"day":case"d":return i*s;case"hours":case"hour":case"hrs":case"hr":case"h":return i*n;case"minutes":case"minute":case"mins":case"min":case"m":return i*r;case"seconds":case"second":case"secs":case"sec":case"s":return i*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}(e);if("number"===u&&isFinite(e))return i.long?(c=e,(l=Math.abs(c))>=s?a(c,l,s,"day"):l>=n?a(c,l,n,"hour"):l>=r?a(c,l,r,"minute"):l>=t?a(c,l,t,"second"):c+" ms"):function(e){var o=Math.abs(e);return o>=s?Math.round(e/s)+"d":o>=n?Math.round(e/n)+"h":o>=r?Math.round(e/r)+"m":o>=t?Math.round(e/t)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},6624:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(8831),s=r(4920),o=r(8380),a=r(6492),i=r(8999),c=r(1119),l=r(4063),u=r(8330),d=r(4051),p=r(2407),h=r(9277),f=r(4287),m=r(3189),g=r(3518),y=r(9574),v=r(6696);t.default=function(e=!1){const t=[h.default,f.default,m.default,g.default,y.default,v.default,l.default,u.default,c.default,d.default,p.default];return e?t.push(s.default,a.default):t.push(n.default,o.default),t.push(i.default),t}},6636:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(7083),s={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const o=t.name("valid");r.forEach((t,r)=>{if((0,n.alwaysValidSchema)(s,t))return;const a=e.subschema({keyword:"allOf",schemaProp:r},o);e.ok(o),e.mergeEvaluated(a)})}};t.default=s},6663:(e,t,r)=>{"use strict";var n=r(5388),s=Object.prototype.hasOwnProperty;e.exports=new n("tag:yaml.org,2002:set",{kind:"mapping",resolve:function(e){if(null===e)return!0;var t,r=e;for(t in r)if(s.call(r,t)&&null!==r[t])return!1;return!0},construct:function(e){return null!==e?e:{}}})},6696:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5273),s={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){void 0===t.if&&(0,n.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};t.default=s},6711:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>n.str`must be multiple of ${e}`,params:({schemaCode:e})=>n._`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:s,it:o}=e,a=o.opts.multipleOfPrecision,i=t.let("res"),c=a?n._`Math.abs(Math.round(${i}) - ${i}) > 1e-${a}`:n._`${i} !== parseInt(${i})`;e.fail$data(n._`(${s} === 0 || (${i} = ${r}/${s}, ${c}))`)}};t.default=s},6735:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s={data:new n.Name("data"),valCxt:new n.Name("valCxt"),instancePath:new n.Name("instancePath"),parentData:new n.Name("parentData"),parentDataProperty:new n.Name("parentDataProperty"),rootData:new n.Name("rootData"),dynamicAnchors:new n.Name("dynamicAnchors"),vErrors:new n.Name("vErrors"),errors:new n.Name("errors"),this:new n.Name("this"),self:new n.Name("self"),scope:new n.Name("scope"),json:new n.Name("json"),jsonPos:new n.Name("jsonPos"),jsonLen:new n.Name("jsonLen"),jsonPart:new n.Name("jsonPart")};t.default=s},6780:(e,t,r)=>{"use strict";const n=r(8311);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t,r))},6874:e=>{"use strict";const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},6925:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?n.str`must contain at least ${e} valid item(s)`:n.str`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?n._`{minContains: ${e}}`:n._`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:o,data:a,it:i}=e;let c,l;const{minContains:u,maxContains:d}=o;i.opts.next?(c=void 0===u?1:u,l=d):c=1;const p=t.const("len",n._`${a}.length`);if(e.setParams({min:c,max:l}),void 0===l&&0===c)return void(0,s.checkStrictMode)(i,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==l&&c>l)return(0,s.checkStrictMode)(i,'"minContains" > "maxContains" is always invalid'),void e.fail();if((0,s.alwaysValidSchema)(i,r)){let t=n._`${p} >= ${c}`;return void 0!==l&&(t=n._`${t} && ${p} <= ${l}`),void e.pass(t)}i.items=!0;const h=t.name("valid");function f(){const e=t.name("_valid"),r=t.let("count",0);m(e,()=>t.if(e,()=>function(e){t.code(n._`${e}++`),void 0===l?t.if(n._`${e} >= ${c}`,()=>t.assign(h,!0).break()):(t.if(n._`${e} > ${l}`,()=>t.assign(h,!1).break()),1===c?t.assign(h,!0):t.if(n._`${e} >= ${c}`,()=>t.assign(h,!0)))}(r)))}function m(r,n){t.forRange("i",0,p,t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:s.Type.Num,compositeRule:!0},r),n()})}void 0===l&&1===c?m(h,()=>t.if(h,()=>t.break())):0===c?(t.let(h,!0),void 0!==l&&t.if(n._`${a}.length > 0`,f)):(t.let(h,!1),f()),e.result(h,()=>e.reset())}};t.default=o},6928:e=>{"use strict";e.exports=require("path")},6939:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(7083),s={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:s}=e;if((0,n.alwaysValidSchema)(s,r))return void e.fail();const o=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},o),e.failResult(o,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}};t.default=s},6942:(e,t,r)=>{"use strict";var n=r(5388),s=Object.prototype.toString;e.exports=new n("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,r,n,o,a,i=e;for(a=new Array(i.length),t=0,r=i.length;t<r;t+=1){if(n=i[t],"[object Object]"!==s.call(n))return!1;if(1!==(o=Object.keys(n)).length)return!1;a[t]=[o[0],n[o[0]]]}return!0},construct:function(e){if(null===e)return[];var t,r,n,s,o,a=e;for(o=new Array(a.length),t=0,r=a.length;t<r;t+=1)n=a[t],s=Object.keys(n),o[t]=[s[0],n[s[0]]];return o}})},6946:(e,t,r)=>{"use strict";var n=r(5388),s=Object.prototype.hasOwnProperty,o=Object.prototype.toString;e.exports=new n("tag:yaml.org,2002:omap",{kind:"sequence",resolve:function(e){if(null===e)return!0;var t,r,n,a,i,c=[],l=e;for(t=0,r=l.length;t<r;t+=1){if(n=l[t],i=!1,"[object Object]"!==o.call(n))return!1;for(a in n)if(s.call(n,a)){if(i)return!1;i=!0}if(!i)return!1;if(-1!==c.indexOf(a))return!1;c.push(a)}return!0},construct:function(e){return null!==e?e:[]}})},6953:(e,t,r)=>{"use strict";const n=r(144);e.exports=(e,t)=>{const r=n(e,t);return r?r.version:null}},6976:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(8343);n.code='require("ajv/dist/runtime/uri").default',t.default=n},6982:e=>{"use strict";e.exports=require("crypto")},6983:(e,t)=>{"use strict";function r(e){const t=e.length;let r,n=0,s=0;for(;s<t;)n++,r=e.charCodeAt(s++),r>=55296&&r<=56319&&s<t&&(r=e.charCodeAt(s),56320==(64512&r)&&s++);return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,r.code='require("ajv/dist/runtime/ucs2length").default'},7013:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7420),o=r(5219),a=r(303),i=r(7083),c={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===s.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>n._`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:c,parentSchema:l,it:u}=e,{oneOf:d}=l;if(!u.opts.discriminator)throw new Error("discriminator: requires discriminator option");const p=c.propertyName;if("string"!=typeof p)throw new Error("discriminator: requires propertyName");if(c.mapping)throw new Error("discriminator: mapping is not supported");if(!d)throw new Error("discriminator: requires oneOf keyword");const h=t.let("valid",!1),f=t.const("tag",n._`${r}${(0,n.getProperty)(p)}`);function m(r){const s=t.name("valid"),o=e.subschema({keyword:"oneOf",schemaProp:r},s);return e.mergeEvaluated(o,n.Name),s}t.if(n._`typeof ${f} == "string"`,()=>function(){const r=function(){var e;const t={},r=s(l);let n=!0;for(let t=0;t<d.length;t++){let l=d[t];if((null==l?void 0:l.$ref)&&!(0,i.schemaHasRulesButRef)(l,u.self.RULES)){const e=l.$ref;if(l=o.resolveRef.call(u.self,u.schemaEnv.root,u.baseId,e),l instanceof o.SchemaEnv&&(l=l.schema),void 0===l)throw new a.default(u.opts.uriResolver,u.baseId,e)}const h=null===(e=null==l?void 0:l.properties)||void 0===e?void 0:e[p];if("object"!=typeof h)throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${p}"`);n=n&&(r||s(l)),c(h,t)}if(!n)throw new Error(`discriminator: "${p}" must be required`);return t;function s({required:e}){return Array.isArray(e)&&e.includes(p)}function c(e,t){if(e.const)h(e.const,t);else{if(!e.enum)throw new Error(`discriminator: "properties/${p}" must have "const" or "enum"`);for(const r of e.enum)h(r,t)}}function h(e,r){if("string"!=typeof e||e in t)throw new Error(`discriminator: "${p}" values must be unique strings`);t[e]=r}}();t.if(!1);for(const e in r)t.elseIf(n._`${f} === ${e}`),t.assign(h,m(r[e]));t.else(),e.error(!1,{discrError:s.DiscrError.Mapping,tag:f,tagName:p}),t.endIf()}(),()=>e.error(!1,{discrError:s.DiscrError.Tag,tag:f,tagName:p})),e.ok(h)}};t.default=c},7016:e=>{"use strict";e.exports=require("url")},7059:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t,r)=>n(e,t,r)<0},7065:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;const n=r(3789),s=r(7083),o={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:n}=t;Array.isArray(n)?a(e,n):(0,s.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas')}};function a(e,t){const{gen:r,schema:o,data:a,keyword:i,it:c}=e;c.items=!0;const l=r.const("len",n._`${a}.length`);if(!1===o)e.setParams({len:t.length}),e.pass(n._`${l} <= ${t.length}`);else if("object"==typeof o&&!(0,s.alwaysValidSchema)(c,o)){const o=r.var("valid",n._`${l} <= ${t.length}`);r.if((0,n.not)(o),()=>function(o){r.forRange("i",t.length,l,t=>{e.subschema({keyword:i,dataProp:t,dataPropType:s.Type.Num},o),c.allErrors||r.if((0,n.not)(o),()=>r.break())})}(o)),e.ok(o)}}t.validateAdditionalItems=a,t.default=o},7075:(e,t,r)=>{"use strict";const n=r(3908),s=r(3904),{ANY:o}=s,a=r(8311),i=r(7638),c=r(5580),l=r(7059),u=r(5200),d=r(4089);e.exports=(e,t,r,p)=>{let h,f,m,g,y;switch(e=new n(e,p),t=new a(t,p),r){case">":h=c,f=u,m=l,g=">",y=">=";break;case"<":h=l,f=d,m=c,g="<",y="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(i(e,t,p))return!1;for(let r=0;r<t.set.length;++r){const n=t.set[r];let a=null,i=null;if(n.forEach(e=>{e.semver===o&&(e=new s(">=0.0.0")),a=a||e,i=i||e,h(e.semver,a.semver,p)?a=e:m(e.semver,i.semver,p)&&(i=e)}),a.operator===g||a.operator===y)return!1;if((!i.operator||i.operator===g)&&f(e,i.semver))return!1;if(i.operator===y&&m(e,i.semver))return!1}return!0}},7083:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;const n=r(3789),s=r(2104);function o(e,t=e.schema){const{opts:r,self:n}=e;if(!r.strictSchema)return;if("boolean"==typeof t)return;const s=n.RULES.keywords;for(const r in t)s[r]||f(e,`unknown keyword: "${r}"`)}function a(e,t){if("boolean"==typeof e)return!e;for(const r in e)if(t[r])return!0;return!1}function i(e){return"number"==typeof e?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}function c(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function l({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:s}){return(o,a,i,c)=>{const l=void 0===i?a:i instanceof n.Name?(a instanceof n.Name?e(o,a,i):t(o,a,i),i):a instanceof n.Name?(t(o,i,a),a):r(a,i);return c!==n.Name||l instanceof n.Name?l:s(o,l)}}function u(e,t){if(!0===t)return e.var("props",!0);const r=e.var("props",n._`{}`);return void 0!==t&&d(e,r,t),r}function d(e,t,r){Object.keys(r).forEach(r=>e.assign(n._`${t}${(0,n.getProperty)(r)}`,!0))}t.toHash=function(e){const t={};for(const r of e)t[r]=!0;return t},t.alwaysValidSchema=function(e,t){return"boolean"==typeof t?t:0===Object.keys(t).length||(o(e,t),!a(t,e.self.RULES.all))},t.checkUnknownRules=o,t.schemaHasRules=a,t.schemaHasRulesButRef=function(e,t){if("boolean"==typeof e)return!e;for(const r in e)if("$ref"!==r&&t.all[r])return!0;return!1},t.schemaRefOrVal=function({topSchemaRef:e,schemaPath:t},r,s,o){if(!o){if("number"==typeof r||"boolean"==typeof r)return r;if("string"==typeof r)return n._`${r}`}return n._`${e}${t}${(0,n.getProperty)(s)}`},t.unescapeFragment=function(e){return c(decodeURIComponent(e))},t.escapeFragment=function(e){return encodeURIComponent(i(e))},t.escapeJsonPointer=i,t.unescapeJsonPointer=c,t.eachItem=function(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)},t.mergeEvaluated={props:l({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,()=>{e.if(n._`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,n._`${r} || {}`).code(n._`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,()=>{!0===t?e.assign(r,!0):(e.assign(r,n._`${r} || {}`),d(e,r,t))}),mergeValues:(e,t)=>!0===e||{...e,...t},resultToName:u}),items:l({mergeNames:(e,t,r)=>e.if(n._`${r} !== true && ${t} !== undefined`,()=>e.assign(r,n._`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if(n._`${r} !== true`,()=>e.assign(r,!0===t||n._`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>!0===e||Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})},t.evaluatedPropsToName=u,t.setEvaluated=d;const p={};var h;function f(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,!0===r)throw new Error(t);e.self.logger.warn(t)}}t.useFunc=function(e,t){return e.scopeValue("func",{ref:t,code:p[t.code]||(p[t.code]=new s._Code(t.code))})},function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"}(h||(t.Type=h={})),t.getErrorPath=function(e,t,r){if(e instanceof n.Name){const s=t===h.Num;return r?s?n._`"[" + ${e} + "]"`:n._`"['" + ${e} + "']"`:s?n._`"/" + ${e}`:n._`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,n.getProperty)(e).toString():"/"+i(e)},t.checkStrictMode=f},7103:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};t.default=r},7193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s={data:new n.Name("data"),valCxt:new n.Name("valCxt"),instancePath:new n.Name("instancePath"),parentData:new n.Name("parentData"),parentDataProperty:new n.Name("parentDataProperty"),rootData:new n.Name("rootData"),dynamicAnchors:new n.Name("dynamicAnchors"),vErrors:new n.Name("vErrors"),errors:new n.Name("errors"),this:new n.Name("this"),self:new n.Name("self"),scope:new n.Name("scope"),json:new n.Name("json"),jsonPos:new n.Name("jsonPos"),jsonLen:new n.Name("jsonLen"),jsonPart:new n.Name("jsonPart")};t.default=s},7210:(e,t,r)=>{"use strict";var n=r(9243),s=r(4781);function o(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}e.exports.Type=r(5388),e.exports.Schema=r(2119),e.exports.FAILSAFE_SCHEMA=r(7759),e.exports.JSON_SCHEMA=r(6184),e.exports.CORE_SCHEMA=r(1769),e.exports.DEFAULT_SCHEMA=r(5489),e.exports.load=n.load,e.exports.loadAll=n.loadAll,e.exports.dump=s.dump,e.exports.YAMLException=r(1231),e.exports.types={binary:r(9342),float:r(1461),map:r(2369),null:r(9198),pairs:r(6942),set:r(6663),timestamp:r(127),bool:r(6199),int:r(4466),merge:r(1851),omap:r(6946),seq:r(8636),str:r(7212)},e.exports.safeLoad=o("safeLoad","load"),e.exports.safeLoadAll=o("safeLoadAll","loadAll"),e.exports.safeDump=o("safeDump","dump")},7212:(e,t,r)=>{"use strict";var n=r(5388);e.exports=new n("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return null!==e?e:""}})},7242:e=>{"use strict";var t=e.exports=function(e,t,n){"function"==typeof t&&(n=t,t={}),r(t,"function"==typeof(n=t.cb||n)?n:n.pre||function(){},n.post||function(){},e,"",e)};function r(e,s,o,a,i,c,l,u,d,p){if(a&&"object"==typeof a&&!Array.isArray(a)){for(var h in s(a,i,c,l,u,d,p),a){var f=a[h];if(Array.isArray(f)){if(h in t.arrayKeywords)for(var m=0;m<f.length;m++)r(e,s,o,f[m],i+"/"+h+"/"+m,c,i,h,a,m)}else if(h in t.propsKeywords){if(f&&"object"==typeof f)for(var g in f)r(e,s,o,f[g],i+"/"+h+"/"+n(g),c,i,h,a,g)}else(h in t.keywords||e.allKeys&&!(h in t.skipKeywords))&&r(e,s,o,f,i+"/"+h,c,i,h,a)}o(a,i,c,l,u,d,p)}}function n(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}t.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},t.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},t.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},t.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},7272:e=>{"use strict";const t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},7281:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1039),s=r(111),o=r(5273),a={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>s.str`must have required property '${e}'`,params:({params:{missingProperty:e}})=>s._`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:a,data:i,$data:c,it:l}=e,{opts:u}=l;if(!c&&0===r.length)return;const d=r.length>=u.loopRequired;if(l.allErrors?function(){if(d||c)e.block$data(s.nil,p);else for(const t of r)(0,n.checkReportMissingProp)(e,t)}():function(){const o=t.let("missing");if(d||c){const r=t.let("valid",!0);e.block$data(r,()=>function(r,o){e.setParams({missingProperty:r}),t.forOf(r,a,()=>{t.assign(o,(0,n.propertyInData)(t,i,r,u.ownProperties)),t.if((0,s.not)(o),()=>{e.error(),t.break()})},s.nil)}(o,r)),e.ok(r)}else t.if((0,n.checkMissingProp)(e,r,o)),(0,n.reportMissingProp)(e,o),t.else()}(),u.strictRequired){const t=e.parentSchema.properties,{definedProperties:n}=e.it;for(const e of r)if(void 0===(null==t?void 0:t[e])&&!n.has(e)){const t=`required property "${e}" is not defined at "${l.schemaEnv.baseId+l.errSchemaPath}" (strictRequired)`;(0,o.checkStrictMode)(l,t,l.opts.strictRequired)}}function p(){t.forOf("prop",a,r=>{e.setParams({missingProperty:r}),t.if((0,n.noPropertyInData)(t,i,r,u.ownProperties),()=>e.error())})}}};t.default=a},7301:e=>{"use strict";e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},7366:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;const n=r(3789),s=r(7083),o=r(1085),a={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return i(e,"additionalItems",t);r.items=!0,(0,s.alwaysValidSchema)(r,t)||e.ok((0,o.validateArray)(e))}};function i(e,t,r=e.schema){const{gen:o,parentSchema:a,data:i,keyword:c,it:l}=e;!function(e){const{opts:n,errSchemaPath:o}=l,a=r.length,i=a===e.minItems&&(a===e.maxItems||!1===e[t]);if(n.strictTuples&&!i){const e=`"${c}" is ${a}-tuple, but minItems or maxItems/${t} are not specified or different at path "${o}"`;(0,s.checkStrictMode)(l,e,n.strictTuples)}}(a),l.opts.unevaluated&&r.length&&!0!==l.items&&(l.items=s.mergeEvaluated.items(o,r.length,l.items));const u=o.name("valid"),d=o.const("len",n._`${i}.length`);r.forEach((t,r)=>{(0,s.alwaysValidSchema)(l,t)||(o.if(n._`${d} > ${r}`,()=>e.subschema({keyword:c,schemaProp:r,dataProp:r},u)),e.ok(u))})}t.validateTuple=i,t.default=a},7394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(7366),s={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,n.validateTuple)(e,"items")};t.default=s},7414:(e,t,r)=>{"use strict";const n=r(144);e.exports=(e,t)=>{const r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},7420:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DiscrError=void 0,function(e){e.Tag="tag",e.Mapping="mapping"}(r||(t.DiscrError=r={}))},7476:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRules=t.isJSONType=void 0;const r=new Set(["string","number","integer","boolean","null","object","array"]);t.isJSONType=function(e){return"string"==typeof e&&r.has(e)},t.getRules=function(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}},7567:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>n.str`must match "${e.ifClause}" schema`,params:({params:e})=>n._`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:o}=e;void 0===r.then&&void 0===r.else&&(0,s.checkStrictMode)(o,'"if" without "then" and "else" is ignored');const i=a(o,"then"),c=a(o,"else");if(!i&&!c)return;const l=t.let("valid",!0),u=t.name("_valid");if(function(){const t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},u);e.mergeEvaluated(t)}(),e.reset(),i&&c){const r=t.let("ifClause");e.setParams({ifClause:r}),t.if(u,d("then",r),d("else",r))}else i?t.if(u,d("then")):t.if((0,n.not)(u),d("else"));function d(r,s){return()=>{const o=e.subschema({keyword:r},u);t.assign(l,u),e.mergeValidEvaluated(o,l),s?t.assign(s,n._`${r}`):e.setParams({ifClause:r})}}e.pass(l,()=>e.error(!0))}};function a(e,t){const r=e.schema[t];return void 0!==r&&!(0,s.alwaysValidSchema)(e,r)}t.default=o},7619:e=>{"use strict";const t=(e,...t)=>new Promise(r=>{r(e(...t))});e.exports=t,e.exports.default=t},7631:(e,t,r)=>{"use strict";const n=r(8311);e.exports=(e,t)=>new n(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},7638:(e,t,r)=>{"use strict";const n=r(8311);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},7666:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var n=r(9810);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return n.KeywordCxt}});var s=r(3789);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return s._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return s.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return s.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return s.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return s.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return s.CodeGen}});const o=r(9598),a=r(303),i=r(7476),c=r(5219),l=r(3789),u=r(3043),d=r(8840),p=r(7083),h=r(7301),f=r(6976),m=(e,t)=>new RegExp(e,t);m.code="new RegExp";const g=["removeAdditional","useDefaults","coerceTypes"],y=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),v={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},w={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};function _(e){var t,r,n,s,o,a,i,c,l,u,d,p,h,g,y,v,w,_,b,$,E,S,P,O,C;const N=e.strict,I=null===(t=e.code)||void 0===t?void 0:t.optimize,T=!0===I||void 0===I?1:I||0,A=null!==(n=null===(r=e.code)||void 0===r?void 0:r.regExp)&&void 0!==n?n:m,k=null!==(s=e.uriResolver)&&void 0!==s?s:f.default;return{strictSchema:null===(a=null!==(o=e.strictSchema)&&void 0!==o?o:N)||void 0===a||a,strictNumbers:null===(c=null!==(i=e.strictNumbers)&&void 0!==i?i:N)||void 0===c||c,strictTypes:null!==(u=null!==(l=e.strictTypes)&&void 0!==l?l:N)&&void 0!==u?u:"log",strictTuples:null!==(p=null!==(d=e.strictTuples)&&void 0!==d?d:N)&&void 0!==p?p:"log",strictRequired:null!==(g=null!==(h=e.strictRequired)&&void 0!==h?h:N)&&void 0!==g&&g,code:e.code?{...e.code,optimize:T,regExp:A}:{optimize:T,regExp:A},loopRequired:null!==(y=e.loopRequired)&&void 0!==y?y:200,loopEnum:null!==(v=e.loopEnum)&&void 0!==v?v:200,meta:null===(w=e.meta)||void 0===w||w,messages:null===(_=e.messages)||void 0===_||_,inlineRefs:null===(b=e.inlineRefs)||void 0===b||b,schemaId:null!==($=e.schemaId)&&void 0!==$?$:"$id",addUsedSchema:null===(E=e.addUsedSchema)||void 0===E||E,validateSchema:null===(S=e.validateSchema)||void 0===S||S,validateFormats:null===(P=e.validateFormats)||void 0===P||P,unicodeRegExp:null===(O=e.unicodeRegExp)||void 0===O||O,int32range:null===(C=e.int32range)||void 0===C||C,uriResolver:k}}class b{constructor(e={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,e=this.opts={...e,..._(e)};const{es5:t,lines:r}=this.opts.code;this.scope=new l.ValueScope({scope:{},prefixes:y,es5:t,lines:r}),this.logger=function(e){if(!1===e)return N;if(void 0===e)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}(e.logger);const n=e.validateFormats;e.validateFormats=!1,this.RULES=(0,i.getRules)(),$.call(this,v,e,"NOT SUPPORTED"),$.call(this,w,e,"DEPRECATED","warn"),this._metaOpts=C.call(this),e.formats&&P.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),e.keywords&&O.call(this,e.keywords),"object"==typeof e.meta&&this.addMetaSchema(e.meta),S.call(this),e.validateFormats=n}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let n=h;"id"===r&&(n={...h},n.id=n.$id,delete n.$id),t&&e&&this.addMetaSchema(n,n[r],!1)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta="object"==typeof e?e[t]||e:void 0}validate(e,t){let r;if("string"==typeof e){if(r=this.getSchema(e),!r)throw new Error(`no schema with key or ref "${e}"`)}else r=this.compile(e);const n=r(t);return"$async"in r||(this.errors=r.errors),n}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if("function"!=typeof this.opts.loadSchema)throw new Error("options.loadSchema should be a function");const{loadSchema:r}=this.opts;return n.call(this,e,t);async function n(e,t){await s.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||o.call(this,r)}async function s(e){e&&!this.getSchema(e)&&await n.call(this,{$ref:e},!0)}async function o(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof a.default))throw t;return i.call(this,t),await c.call(this,t.missingSchema),o.call(this,e)}}function i({missingSchema:e,missingRef:t}){if(this.refs[e])throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}async function c(e){const r=await l.call(this,e);this.refs[e]||await s.call(this,r.$schema),this.refs[e]||this.addSchema(r,e,t)}async function l(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,n=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,void 0,r,n);return this}let s;if("object"==typeof e){const{schemaId:t}=this.opts;if(s=e[t],void 0!==s&&"string"!=typeof s)throw new Error(`schema ${t} must be string`)}return t=(0,u.normalizeId)(t||s),this._checkUnique(t),this.schemas[t]=this._addSchema(e,r,t,n,!0),this}addMetaSchema(e,t,r=this.opts.validateSchema){return this.addSchema(e,t,!0,r),this}validateSchema(e,t){if("boolean"==typeof e)return!0;let r;if(r=e.$schema,void 0!==r&&"string"!=typeof r)throw new Error("$schema must be a string");if(r=r||this.opts.defaultMeta||this.defaultMeta(),!r)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const n=this.validate(r,e);if(!n&&t){const e="schema is invalid: "+this.errorsText();if("log"!==this.opts.validateSchema)throw new Error(e);this.logger.error(e)}return n}getSchema(e){let t;for(;"string"==typeof(t=E.call(this,e));)e=t;if(void 0===t){const{schemaId:r}=this.opts,n=new c.SchemaEnv({schema:{},schemaId:r});if(t=c.resolveSchema.call(this,n,e),!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp)return this._removeAllSchemas(this.schemas,e),this._removeAllSchemas(this.refs,e),this;switch(typeof e){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const t=E.call(this,e);return"object"==typeof t&&this._cache.delete(t.schema),delete this.schemas[e],delete this.refs[e],this}case"object":{const t=e;this._cache.delete(t);let r=e[this.opts.schemaId];return r&&(r=(0,u.normalizeId)(r),delete this.schemas[r],delete this.refs[r]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if("string"==typeof e)r=e,"object"==typeof t&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),t.keyword=r);else{if("object"!=typeof e||void 0!==t)throw new Error("invalid addKeywords parameters");if(r=(t=e).keyword,Array.isArray(r)&&!r.length)throw new Error("addKeywords: keyword must be string or non-empty array")}if(T.call(this,r,t),!t)return(0,p.eachItem)(r,e=>A.call(this,e)),this;R.call(this,t);const n={...t,type:(0,d.getJSONTypes)(t.type),schemaType:(0,d.getJSONTypes)(t.schemaType)};return(0,p.eachItem)(r,0===n.type.length?e=>A.call(this,e,n):e=>n.type.forEach(t=>A.call(this,e,n,t))),this}getKeyword(e){const t=this.RULES.all[e];return"object"==typeof t?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e],delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex(t=>t.keyword===e);t>=0&&r.rules.splice(t,1)}return this}addFormat(e,t){return"string"==typeof t&&(t=new RegExp(t)),this.formats[e]=t,this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){return e&&0!==e.length?e.map(e=>`${r}${e.instancePath} ${e.message}`).reduce((e,r)=>e+t+r):"No errors"}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const n of t){const t=n.split("/").slice(1);let s=e;for(const e of t)s=s[e];for(const e in r){const t=r[e];if("object"!=typeof t)continue;const{$data:n}=t.definition,o=s[e];n&&o&&(s[e]=D(o))}}return e}_removeAllSchemas(e,t){for(const r in e){const n=e[r];t&&!t.test(r)||("string"==typeof n?delete e[r]:n&&!n.meta&&(this._cache.delete(n.schema),delete e[r]))}}_addSchema(e,t,r,n=this.opts.validateSchema,s=this.opts.addUsedSchema){let o;const{schemaId:a}=this.opts;if("object"==typeof e)o=e[a];else{if(this.opts.jtd)throw new Error("schema must be object");if("boolean"!=typeof e)throw new Error("schema must be object or boolean")}let i=this._cache.get(e);if(void 0!==i)return i;r=(0,u.normalizeId)(o||r);const l=u.getSchemaRefs.call(this,e,r);return i=new c.SchemaEnv({schema:e,schemaId:a,meta:t,baseId:r,localRefs:l}),this._cache.set(i.schema,i),s&&!r.startsWith("#")&&(r&&this._checkUnique(r),this.refs[r]=i),n&&this.validateSchema(e,!0),i}_checkUnique(e){if(this.schemas[e]||this.refs[e])throw new Error(`schema with key or id "${e}" already exists`)}_compileSchemaEnv(e){if(e.meta?this._compileMetaSchema(e):c.compileSchema.call(this,e),!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{c.compileSchema.call(this,e)}finally{this.opts=t}}}function $(e,t,r,n="error"){for(const s in e){const o=s;o in t&&this.logger[n](`${r}: option ${s}. ${e[o]}`)}}function E(e){return e=(0,u.normalizeId)(e),this.schemas[e]||this.refs[e]}function S(){const e=this.opts.schemas;if(e)if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function P(){for(const e in this.opts.formats){const t=this.opts.formats[e];t&&this.addFormat(e,t)}}function O(e){if(Array.isArray(e))this.addVocabulary(e);else{this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];r.keyword||(r.keyword=t),this.addKeyword(r)}}}function C(){const e={...this.opts};for(const t of g)delete e[t];return e}b.ValidationError=o.default,b.MissingRefError=a.default,t.default=b;const N={log(){},warn(){},error(){}},I=/^[a-z_$][a-z0-9_$:-]*$/i;function T(e,t){const{RULES:r}=this;if((0,p.eachItem)(e,e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!I.test(e))throw new Error(`Keyword ${e} has invalid name`)}),t&&t.$data&&!("code"in t)&&!("validate"in t))throw new Error('$data keyword must have "code" or "validate" function')}function A(e,t,r){var n;const s=null==t?void 0:t.post;if(r&&s)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:o}=this;let a=s?o.post:o.rules.find(({type:e})=>e===r);if(a||(a={type:r,rules:[]},o.rules.push(a)),o.keywords[e]=!0,!t)return;const i={keyword:e,definition:{...t,type:(0,d.getJSONTypes)(t.type),schemaType:(0,d.getJSONTypes)(t.schemaType)}};t.before?k.call(this,a,i,t.before):a.rules.push(i),o.all[e]=i,null===(n=t.implements)||void 0===n||n.forEach(e=>this.addKeyword(e))}function k(e,t,r){const n=e.rules.findIndex(e=>e.keyword===r);n>=0?e.rules.splice(n,0,t):(e.rules.push(t),this.logger.warn(`rule ${r} is not defined`))}function R(e){let{metaSchema:t}=e;void 0!==t&&(e.$data&&this.opts.$data&&(t=D(t)),e.validateSchema=this.compile(t,!0))}const x={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function D(e){return{anyOf:[e,x]}}},7687:(e,t,r)=>{"use strict";const n=r(857),s=r(2018),o=r(5884),{env:a}=process;let i;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function l(e,t){if(0===i)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===i)return 0;const r=i||0;if("dumb"===a.TERM)return r;if("win32"===process.platform){const e=n.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:r;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){const e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:r}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?i=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(i=1),"FORCE_COLOR"in a&&(i="true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(parseInt(a.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(l(e,e&&e.isTTY))},stdout:c(l(!0,s.isatty(1))),stderr:c(l(!0,s.isatty(2)))}},7705:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>n._`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:o,it:a}=e;if((0,s.alwaysValidSchema)(a,r))return;const i=t.name("valid");t.forIn("key",o,r=>{e.setParams({propertyName:r}),e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:!0},i),t.if((0,n.not)(i),()=>{e.error(!0),a.allErrors||t.break()})}),e.ok(i)}};t.default=o},7759:(e,t,r)=>{"use strict";var n=r(2119);e.exports=new n({explicit:[r(7212),r(8636),r(2369)]})},7833:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,s=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(s=n))}),t.splice(s,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(736)(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},7939:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.contentVocabulary=t.metadataVocabulary=void 0,t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},7981:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isUrlProbablySupportMultiRangeRequests=l,t.createClient=function(e,t,r){if("string"==typeof e)throw(0,n.newError)("Please pass PublishConfiguration object","ERR_UPDATER_INVALID_PROVIDER_CONFIGURATION");const u=e.provider;switch(u){case"github":{const n=e,s=(n.private?process.env.GH_TOKEN||process.env.GITHUB_TOKEN:null)||n.token;return null==s?new a.GitHubProvider(n,t,r):new c.PrivateGitHubProvider(n,t,s,r)}case"bitbucket":return new s.BitbucketProvider(e,t,r);case"keygen":return new i.KeygenProvider(e,t,r);case"s3":case"spaces":return new o.GenericProvider({provider:"generic",url:(0,n.getS3LikeProviderBaseUrl)(e),channel:e.channel||null},t,{...r,isUseMultipleRangeRequest:!1});case"generic":{const n=e;return new o.GenericProvider(n,t,{...r,isUseMultipleRangeRequest:!1!==n.useMultipleRangeRequest&&l(n.url)})}case"custom":{const s=e,o=s.updateProvider;if(!o)throw(0,n.newError)("Custom provider not specified","ERR_UPDATER_INVALID_PROVIDER_CONFIGURATION");return new o(s,t,r)}default:throw(0,n.newError)(`Unsupported provider: ${u}`,"ERR_UPDATER_UNSUPPORTED_PROVIDER")}};const n=r(6551),s=r(1325),o=r(2705),a=r(1203),i=r(8067),c=r(4572);function l(e){return!e.includes("s3.amazonaws.com")}},8013:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1085),s=r(3789),o=r(7083),a=r(7083),i={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:i,parentSchema:c,it:l}=e,{opts:u}=l,d=(0,n.allSchemaProperties)(r),p=d.filter(e=>(0,o.alwaysValidSchema)(l,r[e]));if(0===d.length||p.length===d.length&&(!l.opts.unevaluated||!0===l.props))return;const h=u.strictSchema&&!u.allowMatchingProperties&&c.properties,f=t.name("valid");!0===l.props||l.props instanceof s.Name||(l.props=(0,a.evaluatedPropsToName)(t,l.props));const{props:m}=l;function g(e){for(const t in h)new RegExp(e).test(t)&&(0,o.checkStrictMode)(l,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}function y(r){t.forIn("key",i,o=>{t.if(s._`${(0,n.usePattern)(e,r)}.test(${o})`,()=>{const n=p.includes(r);n||e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:o,dataPropType:a.Type.Str},f),l.opts.unevaluated&&!0!==m?t.assign(s._`${m}[${o}]`,!0):n||l.allErrors||t.if((0,s.not)(f),()=>t.break())})})}!function(){for(const e of d)h&&g(e),l.allErrors?y(e):(t.var(f,!0),y(e),t.if(f))}()}};t.default=i},8067:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.KeygenProvider=void 0;const n=r(6551),s=r(906),o=r(5776);class a extends o.Provider{constructor(e,t,r){super({...r,isUseMultipleRangeRequest:!1}),this.configuration=e,this.updater=t,this.defaultHostname="api.keygen.sh";const n=this.configuration.host||this.defaultHostname;this.baseUrl=(0,s.newBaseUrl)(`https://${n}/v1/accounts/${this.configuration.account}/artifacts?product=${this.configuration.product}`)}get channel(){return this.updater.channel||this.configuration.channel||"stable"}async getLatestVersion(){const e=new n.CancellationToken,t=(0,s.getChannelFilename)(this.getCustomChannelName(this.channel)),r=(0,s.newUrlFromBase)(t,this.baseUrl,this.updater.isAddNoCacheQuery);try{const n=await this.httpRequest(r,{Accept:"application/vnd.api+json","Keygen-Version":"1.1"},e);return(0,o.parseUpdateInfo)(n,t,r)}catch(e){throw(0,n.newError)(`Unable to find latest version on ${this.toString()}, please ensure release exists: ${e.stack||e.message}`,"ERR_UPDATER_LATEST_VERSION_NOT_FOUND")}}resolveFiles(e){return(0,o.resolveFiles)(e,this.baseUrl)}toString(){const{account:e,product:t,platform:r}=this.configuration;return`Keygen (account: ${e}, product: ${t}, platform: ${r}, channel: ${this.channel})`}}t.KeygenProvider=a},8083:(e,t,r)=>{"use strict";var n=r(8433);function s(e,t,r,n,s){var o="",a="",i=Math.floor(s/2)-1;return n-t>i&&(t=n-i+(o=" ... ").length),r-n>i&&(r=n+i-(a=" ...").length),{str:o+e.slice(t,r).replace(/\t/g,"→")+a,pos:n-t+o.length}}function o(e,t){return n.repeat(" ",t-e.length)+e}e.exports=function(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),"number"!=typeof t.indent&&(t.indent=1),"number"!=typeof t.linesBefore&&(t.linesBefore=3),"number"!=typeof t.linesAfter&&(t.linesAfter=2);for(var r,a=/\r?\n|\r|\0/g,i=[0],c=[],l=-1;r=a.exec(e.buffer);)c.push(r.index),i.push(r.index+r[0].length),e.position<=r.index&&l<0&&(l=i.length-2);l<0&&(l=i.length-1);var u,d,p="",h=Math.min(e.line+t.linesAfter,c.length).toString().length,f=t.maxLength-(t.indent+h+3);for(u=1;u<=t.linesBefore&&!(l-u<0);u++)d=s(e.buffer,i[l-u],c[l-u],e.position-(i[l]-i[l-u]),f),p=n.repeat(" ",t.indent)+o((e.line-u+1).toString(),h)+" | "+d.str+"\n"+p;for(d=s(e.buffer,i[l],c[l],e.position,f),p+=n.repeat(" ",t.indent)+o((e.line+1).toString(),h)+" | "+d.str+"\n",p+=n.repeat("-",t.indent+h+3+d.pos)+"^\n",u=1;u<=t.linesAfter&&!(l+u>=c.length);u++)d=s(e.buffer,i[l+u],c[l+u],e.position-(i[l]-i[l+u]),f),p+=n.repeat(" ",t.indent)+o((e.line+u+1).toString(),h)+" | "+d.str+"\n";return p.replace(/\n$/,"")}},8094:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxProperties"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxProperties"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`Object.keys(${r}).length ${o} ${s}`)}};t.default=s},8142:(e,t,r)=>{e=r.nmd(e);var n="__lodash_hash_undefined__",s=9007199254740991,o="[object Arguments]",a="[object Array]",i="[object Boolean]",c="[object Date]",l="[object Error]",u="[object Function]",d="[object Map]",p="[object Number]",h="[object Object]",f="[object Promise]",m="[object RegExp]",g="[object Set]",y="[object String]",v="[object WeakMap]",w="[object ArrayBuffer]",_="[object DataView]",b=/^\[object .+?Constructor\]$/,$=/^(?:0|[1-9]\d*)$/,E={};E["[object Float32Array]"]=E["[object Float64Array]"]=E["[object Int8Array]"]=E["[object Int16Array]"]=E["[object Int32Array]"]=E["[object Uint8Array]"]=E["[object Uint8ClampedArray]"]=E["[object Uint16Array]"]=E["[object Uint32Array]"]=!0,E[o]=E[a]=E[w]=E[i]=E[_]=E[c]=E[l]=E[u]=E[d]=E[p]=E[h]=E[m]=E[g]=E[y]=E[v]=!1;var S="object"==typeof global&&global&&global.Object===Object&&global,P="object"==typeof self&&self&&self.Object===Object&&self,O=S||P||Function("return this")(),C=t&&!t.nodeType&&t,N=C&&e&&!e.nodeType&&e,I=N&&N.exports===C,T=I&&S.process,A=function(){try{return T&&T.binding&&T.binding("util")}catch(e){}}(),k=A&&A.isTypedArray;function R(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}function x(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}function D(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}var j,M,U,F=Array.prototype,L=Function.prototype,z=Object.prototype,V=O["__core-js_shared__"],q=L.toString,B=z.hasOwnProperty,G=(j=/[^.]+$/.exec(V&&V.keys&&V.keys.IE_PROTO||""))?"Symbol(src)_1."+j:"",H=z.toString,W=RegExp("^"+q.call(B).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),K=I?O.Buffer:void 0,Y=O.Symbol,X=O.Uint8Array,J=z.propertyIsEnumerable,Q=F.splice,Z=Y?Y.toStringTag:void 0,ee=Object.getOwnPropertySymbols,te=K?K.isBuffer:void 0,re=(M=Object.keys,U=Object,function(e){return M(U(e))}),ne=Ne(O,"DataView"),se=Ne(O,"Map"),oe=Ne(O,"Promise"),ae=Ne(O,"Set"),ie=Ne(O,"WeakMap"),ce=Ne(Object,"create"),le=ke(ne),ue=ke(se),de=ke(oe),pe=ke(ae),he=ke(ie),fe=Y?Y.prototype:void 0,me=fe?fe.valueOf:void 0;function ge(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function ye(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function ve(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function we(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new ve;++t<r;)this.add(e[t])}function _e(e){var t=this.__data__=new ye(e);this.size=t.size}function be(e,t){for(var r=e.length;r--;)if(Re(e[r][0],t))return r;return-1}function $e(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Z&&Z in Object(e)?function(e){var t=B.call(e,Z),r=e[Z];try{e[Z]=void 0;var n=!0}catch(e){}var s=H.call(e);return n&&(t?e[Z]=r:delete e[Z]),s}(e):function(e){return H.call(e)}(e)}function Ee(e){return Le(e)&&$e(e)==o}function Se(e,t,r,n,s){return e===t||(null==e||null==t||!Le(e)&&!Le(t)?e!=e&&t!=t:function(e,t,r,n,s,u){var f=De(e),v=De(t),b=f?a:Te(e),$=v?a:Te(t),E=(b=b==o?h:b)==h,S=($=$==o?h:$)==h,P=b==$;if(P&&je(e)){if(!je(t))return!1;f=!0,E=!1}if(P&&!E)return u||(u=new _e),f||ze(e)?Pe(e,t,r,n,s,u):function(e,t,r,n,s,o,a){switch(r){case _:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case w:return!(e.byteLength!=t.byteLength||!o(new X(e),new X(t)));case i:case c:case p:return Re(+e,+t);case l:return e.name==t.name&&e.message==t.message;case m:case y:return e==t+"";case d:var u=x;case g:var h=1&n;if(u||(u=D),e.size!=t.size&&!h)return!1;var f=a.get(e);if(f)return f==t;n|=2,a.set(e,t);var v=Pe(u(e),u(t),n,s,o,a);return a.delete(e),v;case"[object Symbol]":if(me)return me.call(e)==me.call(t)}return!1}(e,t,b,r,n,s,u);if(!(1&r)){var O=E&&B.call(e,"__wrapped__"),C=S&&B.call(t,"__wrapped__");if(O||C){var N=O?e.value():e,I=C?t.value():t;return u||(u=new _e),s(N,I,r,n,u)}}return!!P&&(u||(u=new _e),function(e,t,r,n,s,o){var a=1&r,i=Oe(e),c=i.length;if(c!=Oe(t).length&&!a)return!1;for(var l=c;l--;){var u=i[l];if(!(a?u in t:B.call(t,u)))return!1}var d=o.get(e);if(d&&o.get(t))return d==t;var p=!0;o.set(e,t),o.set(t,e);for(var h=a;++l<c;){var f=e[u=i[l]],m=t[u];if(n)var g=a?n(m,f,u,t,e,o):n(f,m,u,e,t,o);if(!(void 0===g?f===m||s(f,m,r,n,o):g)){p=!1;break}h||(h="constructor"==u)}if(p&&!h){var y=e.constructor,v=t.constructor;y==v||!("constructor"in e)||!("constructor"in t)||"function"==typeof y&&y instanceof y&&"function"==typeof v&&v instanceof v||(p=!1)}return o.delete(e),o.delete(t),p}(e,t,r,n,s,u))}(e,t,r,n,Se,s))}function Pe(e,t,r,n,s,o){var a=1&r,i=e.length,c=t.length;if(i!=c&&!(a&&c>i))return!1;var l=o.get(e);if(l&&o.get(t))return l==t;var u=-1,d=!0,p=2&r?new we:void 0;for(o.set(e,t),o.set(t,e);++u<i;){var h=e[u],f=t[u];if(n)var m=a?n(f,h,u,t,e,o):n(h,f,u,e,t,o);if(void 0!==m){if(m)continue;d=!1;break}if(p){if(!R(t,function(e,t){if(a=t,!p.has(a)&&(h===e||s(h,e,r,n,o)))return p.push(t);var a})){d=!1;break}}else if(h!==f&&!s(h,f,r,n,o)){d=!1;break}}return o.delete(e),o.delete(t),d}function Oe(e){return function(e,t,r){var n=t(e);return De(e)?n:function(e,t){for(var r=-1,n=t.length,s=e.length;++r<n;)e[s+r]=t[r];return e}(n,r(e))}(e,Ve,Ie)}function Ce(e,t){var r,n,s=e.__data__;return("string"==(n=typeof(r=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?s["string"==typeof t?"string":"hash"]:s.map}function Ne(e,t){var r=function(e,t){return null==e?void 0:e[t]}(e,t);return function(e){return!(!Fe(e)||function(e){return!!G&&G in e}(e))&&(Me(e)?W:b).test(ke(e))}(r)?r:void 0}ge.prototype.clear=function(){this.__data__=ce?ce(null):{},this.size=0},ge.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ge.prototype.get=function(e){var t=this.__data__;if(ce){var r=t[e];return r===n?void 0:r}return B.call(t,e)?t[e]:void 0},ge.prototype.has=function(e){var t=this.__data__;return ce?void 0!==t[e]:B.call(t,e)},ge.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=ce&&void 0===t?n:t,this},ye.prototype.clear=function(){this.__data__=[],this.size=0},ye.prototype.delete=function(e){var t=this.__data__,r=be(t,e);return!(r<0||(r==t.length-1?t.pop():Q.call(t,r,1),--this.size,0))},ye.prototype.get=function(e){var t=this.__data__,r=be(t,e);return r<0?void 0:t[r][1]},ye.prototype.has=function(e){return be(this.__data__,e)>-1},ye.prototype.set=function(e,t){var r=this.__data__,n=be(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this},ve.prototype.clear=function(){this.size=0,this.__data__={hash:new ge,map:new(se||ye),string:new ge}},ve.prototype.delete=function(e){var t=Ce(this,e).delete(e);return this.size-=t?1:0,t},ve.prototype.get=function(e){return Ce(this,e).get(e)},ve.prototype.has=function(e){return Ce(this,e).has(e)},ve.prototype.set=function(e,t){var r=Ce(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this},we.prototype.add=we.prototype.push=function(e){return this.__data__.set(e,n),this},we.prototype.has=function(e){return this.__data__.has(e)},_e.prototype.clear=function(){this.__data__=new ye,this.size=0},_e.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},_e.prototype.get=function(e){return this.__data__.get(e)},_e.prototype.has=function(e){return this.__data__.has(e)},_e.prototype.set=function(e,t){var r=this.__data__;if(r instanceof ye){var n=r.__data__;if(!se||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new ve(n)}return r.set(e,t),this.size=r.size,this};var Ie=ee?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var r=-1,n=null==e?0:e.length,s=0,o=[];++r<n;){var a=e[r];t(a)&&(o[s++]=a)}return o}(ee(e),function(t){return J.call(e,t)}))}:function(){return[]},Te=$e;function Ae(e,t){return!!(t=null==t?s:t)&&("number"==typeof e||$.test(e))&&e>-1&&e%1==0&&e<t}function ke(e){if(null!=e){try{return q.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Re(e,t){return e===t||e!=e&&t!=t}(ne&&Te(new ne(new ArrayBuffer(1)))!=_||se&&Te(new se)!=d||oe&&Te(oe.resolve())!=f||ae&&Te(new ae)!=g||ie&&Te(new ie)!=v)&&(Te=function(e){var t=$e(e),r=t==h?e.constructor:void 0,n=r?ke(r):"";if(n)switch(n){case le:return _;case ue:return d;case de:return f;case pe:return g;case he:return v}return t});var xe=Ee(function(){return arguments}())?Ee:function(e){return Le(e)&&B.call(e,"callee")&&!J.call(e,"callee")},De=Array.isArray,je=te||function(){return!1};function Me(e){if(!Fe(e))return!1;var t=$e(e);return t==u||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Ue(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=s}function Fe(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Le(e){return null!=e&&"object"==typeof e}var ze=k?function(e){return function(t){return e(t)}}(k):function(e){return Le(e)&&Ue(e.length)&&!!E[$e(e)]};function Ve(e){return null!=(t=e)&&Ue(t.length)&&!Me(t)?function(e,t){var r=De(e),n=!r&&xe(e),s=!r&&!n&&je(e),o=!r&&!n&&!s&&ze(e),a=r||n||s||o,i=a?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],c=i.length;for(var l in e)!t&&!B.call(e,l)||a&&("length"==l||s&&("offset"==l||"parent"==l)||o&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||Ae(l,c))||i.push(l);return i}(e):function(e){if(r=(t=e)&&t.constructor,t!==("function"==typeof r&&r.prototype||z))return re(e);var t,r,n=[];for(var s in Object(e))B.call(e,s)&&"constructor"!=s&&n.push(s);return n}(e);var t}e.exports=function(e,t){return Se(e,t)}},8182:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(4018),s=r(6461),o=r(3789),a=new o.Name("fullFormats"),i=new o.Name("fastFormats"),c=(e,t={keywords:!0})=>{if(Array.isArray(t))return l(e,t,n.fullFormats,a),e;const[r,o]="fast"===t.mode?[n.fastFormats,i]:[n.fullFormats,a];return l(e,t.formats||n.formatNames,r,o),t.keywords&&s.default(e),e};function l(e,t,r,n){var s,a;null!==(s=(a=e.opts.code).formats)&&void 0!==s||(a.formats=o._`require("ajv-formats/dist/formats").${n}`);for(const n of t)e.addFormat(n,r[n])}c.get=(e,t="full")=>{const r=("fast"===t?n.fastFormats:n.fullFormats)[e];if(!r)throw new Error(`Unknown format "${e}"`);return r},e.exports=t=c,Object.defineProperty(t,"__esModule",{value:!0}),t.default=c},8311:(e,t,r)=>{"use strict";const n=/\s+/g;class s{constructor(e,t){if(t=a(t),e instanceof s)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new s(e.raw,t);if(e instanceof i)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(n," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter(e=>!y(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&v(e[0])){this.set=[e];break}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");const t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&m)|(this.options.loose&&g))+":"+e,r=o.get(t);if(r)return r;const n=this.options.loose,s=n?u[d.HYPHENRANGELOOSE]:u[d.HYPHENRANGE];e=e.replace(s,T(this.options.includePrerelease)),c("hyphen replace",e),e=e.replace(u[d.COMPARATORTRIM],p),c("comparator trim",e),e=e.replace(u[d.TILDETRIM],h),c("tilde trim",e),e=e.replace(u[d.CARETTRIM],f),c("caret trim",e);let a=e.split(" ").map(e=>_(e,this.options)).join(" ").split(/\s+/).map(e=>I(e,this.options));n&&(a=a.filter(e=>(c("loose invalid filter",e,this.options),!!e.match(u[d.COMPARATORLOOSE])))),c("range list",a);const l=new Map,v=a.map(e=>new i(e,this.options));for(const e of v){if(y(e))return[e];l.set(e.value,e)}l.size>1&&l.has("")&&l.delete("");const w=[...l.values()];return o.set(t,w),w}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Range is required");return this.set.some(r=>w(r,t)&&e.set.some(e=>w(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new l(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(A(this.set[t],e,this.options))return!0;return!1}}e.exports=s;const o=new(r(8794)),a=r(8587),i=r(3904),c=r(7272),l=r(3908),{safeRe:u,t:d,comparatorTrimReplace:p,tildeTrimReplace:h,caretTrimReplace:f}=r(9718),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:g}=r(6874),y=e=>"<0.0.0-0"===e.value,v=e=>""===e.value,w=(e,t)=>{let r=!0;const n=e.slice();let s=n.pop();for(;r&&n.length;)r=n.every(e=>s.intersects(e,t)),s=n.pop();return r},_=(e,t)=>(c("comp",e,t),e=S(e,t),c("caret",e),e=$(e,t),c("tildes",e),e=O(e,t),c("xrange",e),e=N(e,t),c("stars",e),e),b=e=>!e||"x"===e.toLowerCase()||"*"===e,$=(e,t)=>e.trim().split(/\s+/).map(e=>E(e,t)).join(" "),E=(e,t)=>{const r=t.loose?u[d.TILDELOOSE]:u[d.TILDE];return e.replace(r,(t,r,n,s,o)=>{let a;return c("tilde",e,t,r,n,s,o),b(r)?a="":b(n)?a=`>=${r}.0.0 <${+r+1}.0.0-0`:b(s)?a=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:o?(c("replaceTilde pr",o),a=`>=${r}.${n}.${s}-${o} <${r}.${+n+1}.0-0`):a=`>=${r}.${n}.${s} <${r}.${+n+1}.0-0`,c("tilde return",a),a})},S=(e,t)=>e.trim().split(/\s+/).map(e=>P(e,t)).join(" "),P=(e,t)=>{c("caret",e,t);const r=t.loose?u[d.CARETLOOSE]:u[d.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,(t,r,s,o,a)=>{let i;return c("caret",e,t,r,s,o,a),b(r)?i="":b(s)?i=`>=${r}.0.0${n} <${+r+1}.0.0-0`:b(o)?i="0"===r?`>=${r}.${s}.0${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${n} <${+r+1}.0.0-0`:a?(c("replaceCaret pr",a),i="0"===r?"0"===s?`>=${r}.${s}.${o}-${a} <${r}.${s}.${+o+1}-0`:`>=${r}.${s}.${o}-${a} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${o}-${a} <${+r+1}.0.0-0`):(c("no pr"),i="0"===r?"0"===s?`>=${r}.${s}.${o}${n} <${r}.${s}.${+o+1}-0`:`>=${r}.${s}.${o}${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${o} <${+r+1}.0.0-0`),c("caret return",i),i})},O=(e,t)=>(c("replaceXRanges",e,t),e.split(/\s+/).map(e=>C(e,t)).join(" ")),C=(e,t)=>{e=e.trim();const r=t.loose?u[d.XRANGELOOSE]:u[d.XRANGE];return e.replace(r,(r,n,s,o,a,i)=>{c("xRange",e,r,n,s,o,a,i);const l=b(s),u=l||b(o),d=u||b(a),p=d;return"="===n&&p&&(n=""),i=t.includePrerelease?"-0":"",l?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&p?(u&&(o=0),a=0,">"===n?(n=">=",u?(s=+s+1,o=0,a=0):(o=+o+1,a=0)):"<="===n&&(n="<",u?s=+s+1:o=+o+1),"<"===n&&(i="-0"),r=`${n+s}.${o}.${a}${i}`):u?r=`>=${s}.0.0${i} <${+s+1}.0.0-0`:d&&(r=`>=${s}.${o}.0${i} <${s}.${+o+1}.0-0`),c("xRange return",r),r})},N=(e,t)=>(c("replaceStars",e,t),e.trim().replace(u[d.STAR],"")),I=(e,t)=>(c("replaceGTE0",e,t),e.trim().replace(u[t.includePrerelease?d.GTE0PRE:d.GTE0],"")),T=e=>(t,r,n,s,o,a,i,c,l,u,d,p)=>`${r=b(n)?"":b(s)?`>=${n}.0.0${e?"-0":""}`:b(o)?`>=${n}.${s}.0${e?"-0":""}`:a?`>=${r}`:`>=${r}${e?"-0":""}`} ${c=b(l)?"":b(u)?`<${+l+1}.0.0-0`:b(d)?`<${l}.${+u+1}.0-0`:p?`<=${l}.${u}.${d}-${p}`:e?`<${l}.${u}.${+d+1}-0`:`<=${c}`}`.trim(),A=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(c(e[r].semver),e[r].semver!==i.ANY&&e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},8330:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(1039),s=r(111),o=r(7193),a=r(5273),i={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>s._`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:i,data:c,errsCount:l,it:u}=e;if(!l)throw new Error("ajv implementation error");const{allErrors:d,opts:p}=u;if(u.props=!0,"all"!==p.removeAdditional&&(0,a.alwaysValidSchema)(u,r))return;const h=(0,n.allSchemaProperties)(i.properties),f=(0,n.allSchemaProperties)(i.patternProperties);function m(e){t.code(s._`delete ${c}[${e}]`)}function g(n){if("all"===p.removeAdditional||p.removeAdditional&&!1===r)m(n);else{if(!1===r)return e.setParams({additionalProperty:n}),e.error(),void(d||t.break());if("object"==typeof r&&!(0,a.alwaysValidSchema)(u,r)){const r=t.name("valid");"failing"===p.removeAdditional?(y(n,r,!1),t.if((0,s.not)(r),()=>{e.reset(),m(n)})):(y(n,r),d||t.if((0,s.not)(r),()=>t.break()))}}}function y(t,r,n){const s={keyword:"additionalProperties",dataProp:t,dataPropType:a.Type.Str};!1===n&&Object.assign(s,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(s,r)}t.forIn("key",c,r=>{h.length||f.length?t.if(function(r){let o;if(h.length>8){const e=(0,a.schemaRefOrVal)(u,i.properties,"properties");o=(0,n.isOwnProperty)(t,e,r)}else o=h.length?(0,s.or)(...h.map(e=>s._`${r} === ${e}`)):s.nil;return f.length&&(o=(0,s.or)(o,...f.map(t=>s._`${(0,n.usePattern)(e,t)}.test(${r})`))),(0,s.not)(o)}(r),()=>g(r)):g(r)}),e.ok(s._`${l} === ${o.default.errors}`)}};t.default=i},8343:(e,t,r)=>{"use strict";const{normalizeIPv6:n,removeDotSegments:s,recomposeAuthority:o,normalizeComponentEncoding:a,isIPv4:i,nonSimpleDomain:c}=r(4834),{SCHEMES:l,getSchemeHandler:u}=r(343);function d(e,t,r,n){const o={};return n||(e=f(p(e,r),r),t=f(p(t,r),r)),!(r=r||{}).tolerant&&t.scheme?(o.scheme=t.scheme,o.userinfo=t.userinfo,o.host=t.host,o.port=t.port,o.path=s(t.path||""),o.query=t.query):(void 0!==t.userinfo||void 0!==t.host||void 0!==t.port?(o.userinfo=t.userinfo,o.host=t.host,o.port=t.port,o.path=s(t.path||""),o.query=t.query):(t.path?("/"===t.path[0]?o.path=s(t.path):(void 0===e.userinfo&&void 0===e.host&&void 0===e.port||e.path?e.path?o.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:o.path=t.path:o.path="/"+t.path,o.path=s(o.path)),o.query=t.query):(o.path=e.path,void 0!==t.query?o.query=t.query:o.query=e.query),o.userinfo=e.userinfo,o.host=e.host,o.port=e.port),o.scheme=e.scheme),o.fragment=t.fragment,o}function p(e,t){const r={host:e.host,scheme:e.scheme,userinfo:e.userinfo,port:e.port,path:e.path,query:e.query,nid:e.nid,nss:e.nss,uuid:e.uuid,fragment:e.fragment,reference:e.reference,resourceName:e.resourceName,secure:e.secure,error:""},n=Object.assign({},t),a=[],i=u(n.scheme||r.scheme);i&&i.serialize&&i.serialize(r,n),void 0!==r.path&&(n.skipEscape?r.path=unescape(r.path):(r.path=escape(r.path),void 0!==r.scheme&&(r.path=r.path.split("%3A").join(":")))),"suffix"!==n.reference&&r.scheme&&a.push(r.scheme,":");const c=o(r);if(void 0!==c&&("suffix"!==n.reference&&a.push("//"),a.push(c),r.path&&"/"!==r.path[0]&&a.push("/")),void 0!==r.path){let e=r.path;n.absolutePath||i&&i.absolutePath||(e=s(e)),void 0===c&&"/"===e[0]&&"/"===e[1]&&(e="/%2F"+e.slice(2)),a.push(e)}return void 0!==r.query&&a.push("?",r.query),void 0!==r.fragment&&a.push("#",r.fragment),a.join("")}const h=/^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;function f(e,t){const r=Object.assign({},t),s={scheme:void 0,userinfo:void 0,host:"",port:void 0,path:"",query:void 0,fragment:void 0};let o=!1;"suffix"===r.reference&&(e=r.scheme?r.scheme+":"+e:"//"+e);const a=e.match(h);if(a){if(s.scheme=a[1],s.userinfo=a[3],s.host=a[4],s.port=parseInt(a[5],10),s.path=a[6]||"",s.query=a[7],s.fragment=a[8],isNaN(s.port)&&(s.port=a[5]),s.host)if(!1===i(s.host)){const e=n(s.host);s.host=e.host.toLowerCase(),o=e.isIPV6}else o=!0;void 0!==s.scheme||void 0!==s.userinfo||void 0!==s.host||void 0!==s.port||void 0!==s.query||s.path?void 0===s.scheme?s.reference="relative":void 0===s.fragment?s.reference="absolute":s.reference="uri":s.reference="same-document",r.reference&&"suffix"!==r.reference&&r.reference!==s.reference&&(s.error=s.error||"URI is not a "+r.reference+" reference.");const t=u(r.scheme||s.scheme);if(!(r.unicodeSupport||t&&t.unicodeSupport)&&s.host&&(r.domainHost||t&&t.domainHost)&&!1===o&&c(s.host))try{s.host=URL.domainToASCII(s.host.toLowerCase())}catch(e){s.error=s.error||"Host's domain name can not be converted to ASCII: "+e}(!t||t&&!t.skipNormalize)&&(-1!==e.indexOf("%")&&(void 0!==s.scheme&&(s.scheme=unescape(s.scheme)),void 0!==s.host&&(s.host=unescape(s.host))),s.path&&(s.path=escape(unescape(s.path))),s.fragment&&(s.fragment=encodeURI(decodeURIComponent(s.fragment)))),t&&t.parse&&t.parse(s,r)}else s.error=s.error||"URI can not be parsed.";return s}const m={SCHEMES:l,normalize:function(e,t){return"string"==typeof e?e=p(f(e,t),t):"object"==typeof e&&(e=f(p(e,t),t)),e},resolve:function(e,t,r){const n=r?Object.assign({scheme:"null"},r):{scheme:"null"},s=d(f(e,n),f(t,n),n,!0);return n.skipEscape=!0,p(s,n)},resolveComponent:d,equal:function(e,t,r){return"string"==typeof e?(e=unescape(e),e=p(a(f(e,r),!0),{...r,skipEscape:!0})):"object"==typeof e&&(e=p(a(e,!0),{...r,skipEscape:!0})),"string"==typeof t?(t=unescape(t),t=p(a(f(t,r),!0),{...r,skipEscape:!0})):"object"==typeof t&&(t=p(a(t,!0),{...r,skipEscape:!0})),e.toLowerCase()===t.toLowerCase()},serialize:p,parse:f};e.exports=m,e.exports.default=m,e.exports.fastUri=m},8380:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateTuple=void 0;const n=r(111),s=r(5273),o=r(1039),a={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return i(e,"additionalItems",t);r.items=!0,(0,s.alwaysValidSchema)(r,t)||e.ok((0,o.validateArray)(e))}};function i(e,t,r=e.schema){const{gen:o,parentSchema:a,data:i,keyword:c,it:l}=e;!function(e){const{opts:n,errSchemaPath:o}=l,a=r.length,i=a===e.minItems&&(a===e.maxItems||!1===e[t]);if(n.strictTuples&&!i){const e=`"${c}" is ${a}-tuple, but minItems or maxItems/${t} are not specified or different at path "${o}"`;(0,s.checkStrictMode)(l,e,n.strictTuples)}}(a),l.opts.unevaluated&&r.length&&!0!==l.items&&(l.items=s.mergeEvaluated.items(o,r.length,l.items));const u=o.name("valid"),d=o.const("len",n._`${i}.length`);r.forEach((t,r)=>{(0,s.alwaysValidSchema)(l,t)||(o.if(n._`${d} > ${r}`,()=>e.subschema({keyword:c,schemaProp:r,dataProp:r},u)),e.ok(u))})}t.validateTuple=i,t.default=a},8433:e=>{"use strict";function t(e){return null==e}e.exports.isNothing=t,e.exports.isObject=function(e){return"object"==typeof e&&null!==e},e.exports.toArray=function(e){return Array.isArray(e)?e:t(e)?[]:[e]},e.exports.repeat=function(e,t){var r,n="";for(r=0;r<t;r+=1)n+=e;return n},e.exports.isNegativeZero=function(e){return 0===e&&Number.NEGATIVE_INFINITY===1/e},e.exports.extend=function(e,t){var r,n,s,o;if(t)for(r=0,n=(o=Object.keys(t)).length;r<n;r+=1)e[s=o[r]]=t[s];return e}},8452:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const n=r(429),s=r(2754),o=r(2861),a=r(2754),i=r(2664),c=r(9087),l=r(3781),u=r(111),d=r(7193),p=r(4629),h=r(5273),f=r(9270);function m({gen:e,validateName:t,schema:r,schemaEnv:n,opts:s},o){s.code.es5?e.func(t,u._`${d.default.data}, ${d.default.valCxt}`,n.$async,()=>{e.code(u._`"use strict"; ${g(r,s)}`),function(e,t){e.if(d.default.valCxt,()=>{e.var(d.default.instancePath,u._`${d.default.valCxt}.${d.default.instancePath}`),e.var(d.default.parentData,u._`${d.default.valCxt}.${d.default.parentData}`),e.var(d.default.parentDataProperty,u._`${d.default.valCxt}.${d.default.parentDataProperty}`),e.var(d.default.rootData,u._`${d.default.valCxt}.${d.default.rootData}`),t.dynamicRef&&e.var(d.default.dynamicAnchors,u._`${d.default.valCxt}.${d.default.dynamicAnchors}`)},()=>{e.var(d.default.instancePath,u._`""`),e.var(d.default.parentData,u._`undefined`),e.var(d.default.parentDataProperty,u._`undefined`),e.var(d.default.rootData,d.default.data),t.dynamicRef&&e.var(d.default.dynamicAnchors,u._`{}`)})}(e,s),e.code(o)}):e.func(t,u._`${d.default.data}, ${function(e){return u._`{${d.default.instancePath}="", ${d.default.parentData}, ${d.default.parentDataProperty}, ${d.default.rootData}=${d.default.data}${e.dynamicRef?u._`, ${d.default.dynamicAnchors}={}`:u.nil}}={}`}(s)}`,n.$async,()=>e.code(g(r,s)).code(o))}function g(e,t){const r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?u._`/*# sourceURL=${r} */`:u.nil}function y({schema:e,self:t}){if("boolean"==typeof e)return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function v(e){return"boolean"!=typeof e.schema}function w(e){(0,h.checkUnknownRules)(e),function(e){const{schema:t,errSchemaPath:r,opts:n,self:s}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(t,s.RULES)&&s.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function _(e,t){if(e.opts.jtd)return $(e,[],!1,t);const r=(0,s.getSchemaTypes)(e.schema);$(e,r,!(0,s.coerceAndCheckDataType)(e,r),t)}function b({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:s}){const o=r.$comment;if(!0===s.$comment)e.code(u._`${d.default.self}.logger.log(${o})`);else if("function"==typeof s.$comment){const r=u.str`${n}/$comment`,s=e.scopeValue("root",{ref:t.root});e.code(u._`${d.default.self}.opts.$comment(${o}, ${r}, ${s}.schema)`)}}function $(e,t,r,n){const{gen:s,schema:i,data:c,allErrors:l,opts:p,self:f}=e,{RULES:m}=f;function g(h){(0,o.shouldUseGroup)(i,h)&&(h.type?(s.if((0,a.checkDataType)(h.type,c,p.strictNumbers)),E(e,h),1===t.length&&t[0]===h.type&&r&&(s.else(),(0,a.reportTypeError)(e)),s.endIf()):E(e,h),l||s.if(u._`${d.default.errors} === ${n||0}`))}!i.$ref||!p.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(i,m)?(p.jtd||function(e,t){!e.schemaEnv.meta&&e.opts.strictTypes&&(function(e,t){t.length&&(e.dataTypes.length?(t.forEach(t=>{S(e.dataTypes,t)||P(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)}),function(e,t){const r=[];for(const n of e.dataTypes)S(t,n)?r.push(n):t.includes("integer")&&"number"===n&&r.push("integer");e.dataTypes=r}(e,t)):e.dataTypes=t)}(e,t),e.opts.allowUnionTypes||function(e,t){t.length>1&&(2!==t.length||!t.includes("null"))&&P(e,"use allowUnionTypes to allow union type keyword")}(e,t),function(e,t){const r=e.self.RULES.all;for(const n in r){const s=r[n];if("object"==typeof s&&(0,o.shouldUseRule)(e.schema,s)){const{type:r}=s.definition;r.length&&!r.some(e=>{return n=e,(r=t).includes(n)||"number"===n&&r.includes("integer");var r,n})&&P(e,`missing type "${r.join(",")}" for keyword "${n}"`)}}}(e,e.dataTypes))}(e,t),s.block(()=>{for(const e of m.rules)g(e);g(m.post)})):s.block(()=>C(e,"$ref",m.all.$ref.definition))}function E(e,t){const{gen:r,schema:n,opts:{useDefaults:s}}=e;s&&(0,i.assignDefaults)(e,t.type),r.block(()=>{for(const r of t.rules)(0,o.shouldUseRule)(n,r)&&C(e,r.keyword,r.definition,t.type)})}function S(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function P(e,t){t+=` at "${e.schemaEnv.baseId+e.errSchemaPath}" (strictTypes)`,(0,h.checkStrictMode)(e,t,e.opts.strictTypes)}t.validateFunctionCode=function(e){v(e)&&(w(e),y(e))?function(e){const{schema:t,opts:r,gen:n}=e;m(e,()=>{r.$comment&&t.$comment&&b(e),function(e){const{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,h.checkStrictMode)(e,"default is ignored in the schema root")}(e),n.let(d.default.vErrors,null),n.let(d.default.errors,0),r.unevaluated&&function(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",u._`${r}.evaluated`),t.if(u._`${e.evaluated}.dynamicProps`,()=>t.assign(u._`${e.evaluated}.props`,u._`undefined`)),t.if(u._`${e.evaluated}.dynamicItems`,()=>t.assign(u._`${e.evaluated}.items`,u._`undefined`))}(e),_(e),function(e){const{gen:t,schemaEnv:r,validateName:n,ValidationError:s,opts:o}=e;r.$async?t.if(u._`${d.default.errors} === 0`,()=>t.return(d.default.data),()=>t.throw(u._`new ${s}(${d.default.vErrors})`)):(t.assign(u._`${n}.errors`,d.default.vErrors),o.unevaluated&&function({gen:e,evaluated:t,props:r,items:n}){r instanceof u.Name&&e.assign(u._`${t}.props`,r),n instanceof u.Name&&e.assign(u._`${t}.items`,n)}(e),t.return(u._`${d.default.errors} === 0`))}(e)})}(e):m(e,()=>(0,n.topBoolOrEmptySchema)(e))};class O{constructor(e,t,r){if((0,c.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,h.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",T(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,c.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",d.default.errors))}result(e,t,r){this.failResult((0,u.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,u.not)(e),void 0,t)}fail(e){if(void 0===e)return this.error(),void(this.allErrors||this.gen.if(!1));this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail(u._`${t} !== undefined && (${(0,u.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t)return this.setParams(t),this._error(e,r),void this.setParams({});this._error(e,r)}_error(e,t){(e?f.reportExtraError:f.reportError)(this,this.def.error,t)}$dataError(){(0,f.reportError)(this,this.def.$dataError||f.keyword$DataError)}reset(){if(void 0===this.errsCount)throw new Error('add "trackErrors" to keyword definition');(0,f.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=u.nil){this.gen.block(()=>{this.check$data(e,r),t()})}check$data(e=u.nil,t=u.nil){if(!this.$data)return;const{gen:r,schemaCode:n,schemaType:s,def:o}=this;r.if((0,u.or)(u._`${n} === undefined`,t)),e!==u.nil&&r.assign(e,!0),(s.length||o.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==u.nil&&r.assign(e,!1)),r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:r,def:n,it:s}=this;return(0,u.or)(function(){if(r.length){if(!(t instanceof u.Name))throw new Error("ajv implementation error");const e=Array.isArray(r)?r:[r];return u._`${(0,a.checkDataTypes)(e,t,s.opts.strictNumbers,a.DataType.Wrong)}`}return u.nil}(),function(){if(n.validateSchema){const r=e.scopeValue("validate$data",{ref:n.validateSchema});return u._`!${r}(${t})`}return u.nil}())}subschema(e,t){const r=(0,l.getSubschema)(this.it,e);(0,l.extendSubschemaData)(r,this.it,e),(0,l.extendSubschemaMode)(r,e);const s={...this.it,...r,items:void 0,props:void 0};return function(e,t){v(e)&&(w(e),y(e))?function(e,t){const{schema:r,gen:n,opts:s}=e;s.$comment&&r.$comment&&b(e),function(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,p.resolveUrl)(e.opts.uriResolver,e.baseId,t))}(e),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}(e);const o=n.const("_errs",d.default.errors);_(e,o),n.var(t,u._`${o} === ${d.default.errors}`)}(e,t):(0,n.boolOrEmptySchema)(e,t)}(s,t),s}mergeEvaluated(e,t){const{it:r,gen:n}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=h.mergeEvaluated.props(n,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=h.mergeEvaluated.items(n,e.items,r.items,t)))}mergeValidEvaluated(e,t){const{it:r,gen:n}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return n.if(t,()=>this.mergeEvaluated(e,u.Name)),!0}}function C(e,t,r,n){const s=new O(e,r,t);"code"in r?r.code(s,n):s.$data&&r.validate?(0,c.funcKeywordCode)(s,r):"macro"in r?(0,c.macroKeywordCode)(s,r):(r.compile||r.validate)&&(0,c.funcKeywordCode)(s,r)}t.KeywordCxt=O;const N=/^\/(?:[^~]|~0|~1)*$/,I=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function T(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let s,o;if(""===e)return d.default.rootData;if("/"===e[0]){if(!N.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);s=e,o=d.default.rootData}else{const a=I.exec(e);if(!a)throw new Error(`Invalid JSON-pointer: ${e}`);const i=+a[1];if(s=a[2],"#"===s){if(i>=t)throw new Error(c("property/index",i));return n[t-i]}if(i>t)throw new Error(c("data",i));if(o=r[t-i],!s)return o}let a=o;const i=s.split("/");for(const e of i)e&&(o=u._`${o}${(0,u.getProperty)((0,h.unescapeJsonPointer)(e))}`,a=u._`${a} && ${o}`);return a;function c(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=T},8465:(e,t,r)=>{"use strict";const n=r(7619),s=e=>{if(!Number.isInteger(e)&&e!==1/0||!(e>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));const t=[];let r=0;const s=()=>{r--,t.length>0&&t.shift()()},o=(e,t,...o)=>{r++;const a=n(e,...o);t(a),a.then(s,s)},a=(n,...s)=>new Promise(a=>((n,s,...a)=>{r<e?o(n,s,...a):t.push(o.bind(null,n,s,...a))})(n,a,...s));return Object.defineProperties(a,{activeCount:{get:()=>r},pendingCount:{get:()=>t.length},clearQueue:{value:()=>{t.length=0}}}),a};e.exports=s,e.exports.default=s},8486:(e,t,r)=>{"use strict";const n=r(8997),s=new Set(["__proto__","prototype","constructor"]);function o(e){const t=e.split("."),r=[];for(let e=0;e<t.length;e++){let n=t[e];for(;"\\"===n[n.length-1]&&void 0!==t[e+1];)n=n.slice(0,-1)+".",n+=t[++e];r.push(n)}return r.some(e=>s.has(e))?[]:r}e.exports={get(e,t,r){if(!n(e)||"string"!=typeof t)return void 0===r?e:r;const s=o(t);if(0!==s.length){for(let t=0;t<s.length;t++)if(null==(e=e[s[t]])){if(t!==s.length-1)return r;break}return void 0===e?r:e}},set(e,t,r){if(!n(e)||"string"!=typeof t)return e;const s=e,a=o(t);for(let t=0;t<a.length;t++){const s=a[t];n(e[s])||(e[s]={}),t===a.length-1&&(e[s]=r),e=e[s]}return s},delete(e,t){if(!n(e)||"string"!=typeof t)return!1;const r=o(t);for(let t=0;t<r.length;t++){const s=r[t];if(t===r.length-1)return delete e[s],!0;if(e=e[s],!n(e))return!1}},has(e,t){if(!n(e)||"string"!=typeof t)return!1;const r=o(t);if(0===r.length)return!1;for(let t=0;t<r.length;t++){if(!n(e))return!1;if(!(r[t]in e))return!1;e=e[r[t]]}return!0}}},8587:e=>{"use strict";const t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},8611:e=>{"use strict";e.exports=require("http")},8626:e=>{"use strict";const t=(e,t)=>{for(const r of Reflect.ownKeys(t))Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r));return e};e.exports=t,e.exports.default=t},8636:(e,t,r)=>{"use strict";var n=r(5388);e.exports=new n("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return null!==e?e:[]}})},8682:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(4936),s=r(973),o=r(4445),a=r(2944),i=r(2040),c=r(7281),l=r(2629),u=r(627),d=r(1969),p=r(5381),h=[n.default,s.default,o.default,a.default,i.default,c.default,l.default,u.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},d.default,p.default];t.default=h},8794:e=>{"use strict";e.exports=class{constructor(){this.max=1e3,this.map=new Map}get(e){const t=this.map.get(e);return void 0===t?void 0:(this.map.delete(e),this.map.set(e,t),t)}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){const e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}},8801:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Lazy=void 0,t.Lazy=class{constructor(e){this._value=null,this.creator=e}get hasValue(){return null==this.creator}get value(){if(null==this.creator)return this._value;const e=this.creator();return this.value=e,e}set value(e){this._value=e,this.creator=null}}},8831:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateAdditionalItems=void 0;const n=r(111),s=r(5273),o={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>n.str`must NOT have more than ${e} items`,params:({params:{len:e}})=>n._`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:n}=t;Array.isArray(n)?a(e,n):(0,s.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas')}};function a(e,t){const{gen:r,schema:o,data:a,keyword:i,it:c}=e;c.items=!0;const l=r.const("len",n._`${a}.length`);if(!1===o)e.setParams({len:t.length}),e.pass(n._`${l} <= ${t.length}`);else if("object"==typeof o&&!(0,s.alwaysValidSchema)(c,o)){const o=r.var("valid",n._`${l} <= ${t.length}`);r.if((0,n.not)(o),()=>function(o){r.forRange("i",t.length,l,t=>{e.subschema({keyword:i,dataProp:t,dataPropType:s.Type.Num},o),c.allErrors||r.if((0,n.not)(o),()=>r.break())})}(o)),e.ok(o)}}t.validateAdditionalItems=a,t.default=o},8840:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;const n=r(7476),s=r(5335),o=r(2796),a=r(3789),i=r(7083);var c;function l(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(n.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}!function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"}(c||(t.DataType=c={})),t.getSchemaTypes=function(e){const t=l(e.type);if(t.includes("null")){if(!1===e.nullable)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&void 0!==e.nullable)throw new Error('"nullable" cannot be used without "type"');!0===e.nullable&&t.push("null")}return t},t.getJSONTypes=l,t.coerceAndCheckDataType=function(e,t){const{gen:r,data:n,opts:o}=e,i=function(e,t){return t?e.filter(e=>u.has(e)||"array"===t&&"array"===e):[]}(t,o.coerceTypes),l=t.length>0&&!(0===i.length&&1===t.length&&(0,s.schemaHasRulesForType)(e,t[0]));if(l){const s=p(t,n,o.strictNumbers,c.Wrong);r.if(s,()=>{i.length?function(e,t,r){const{gen:n,data:s,opts:o}=e,i=n.let("dataType",a._`typeof ${s}`),c=n.let("coerced",a._`undefined`);"array"===o.coerceTypes&&n.if(a._`${i} == 'object' && Array.isArray(${s}) && ${s}.length == 1`,()=>n.assign(s,a._`${s}[0]`).assign(i,a._`typeof ${s}`).if(p(t,s,o.strictNumbers),()=>n.assign(c,s))),n.if(a._`${c} !== undefined`);for(const e of r)(u.has(e)||"array"===e&&"array"===o.coerceTypes)&&l(e);function l(e){switch(e){case"string":return void n.elseIf(a._`${i} == "number" || ${i} == "boolean"`).assign(c,a._`"" + ${s}`).elseIf(a._`${s} === null`).assign(c,a._`""`);case"number":return void n.elseIf(a._`${i} == "boolean" || ${s} === null
              || (${i} == "string" && ${s} && ${s} == +${s})`).assign(c,a._`+${s}`);case"integer":return void n.elseIf(a._`${i} === "boolean" || ${s} === null
              || (${i} === "string" && ${s} && ${s} == +${s} && !(${s} % 1))`).assign(c,a._`+${s}`);case"boolean":return void n.elseIf(a._`${s} === "false" || ${s} === 0 || ${s} === null`).assign(c,!1).elseIf(a._`${s} === "true" || ${s} === 1`).assign(c,!0);case"null":return n.elseIf(a._`${s} === "" || ${s} === 0 || ${s} === false`),void n.assign(c,null);case"array":n.elseIf(a._`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${s} === null`).assign(c,a._`[${s}]`)}}n.else(),f(e),n.endIf(),n.if(a._`${c} !== undefined`,()=>{n.assign(s,c),function({gen:e,parentData:t,parentDataProperty:r},n){e.if(a._`${t} !== undefined`,()=>e.assign(a._`${t}[${r}]`,n))}(e,c)})}(e,t,i):f(e)})}return l};const u=new Set(["string","number","integer","boolean","null"]);function d(e,t,r,n=c.Correct){const s=n===c.Correct?a.operators.EQ:a.operators.NEQ;let o;switch(e){case"null":return a._`${t} ${s} null`;case"array":o=a._`Array.isArray(${t})`;break;case"object":o=a._`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":o=i(a._`!(${t} % 1) && !isNaN(${t})`);break;case"number":o=i();break;default:return a._`typeof ${t} ${s} ${e}`}return n===c.Correct?o:(0,a.not)(o);function i(e=a.nil){return(0,a.and)(a._`typeof ${t} == "number"`,e,r?a._`isFinite(${t})`:a.nil)}}function p(e,t,r,n){if(1===e.length)return d(e[0],t,r,n);let s;const o=(0,i.toHash)(e);if(o.array&&o.object){const e=a._`typeof ${t} != "object"`;s=o.null?e:a._`!${t} || ${e}`,delete o.null,delete o.array,delete o.object}else s=a.nil;o.number&&delete o.integer;for(const e in o)s=(0,a.and)(s,d(e,t,r,n));return s}t.checkDataType=d,t.checkDataTypes=p;const h={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>"string"==typeof e?a._`{type: ${e}}`:a._`{type: ${t}}`};function f(e){const t=function(e){const{gen:t,data:r,schema:n}=e,s=(0,i.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:s,schemaValue:s,parentSchema:n,params:{},it:e}}(e);(0,o.reportError)(t,h)}t.reportTypeError=f},8992:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=t.Ajv=void 0;const n=r(6056),s=r(5694),o=r(4747),a=r(9049),i=["/properties"],c="http://json-schema.org/draft-07/schema";class l extends n.default{_addVocabularies(){super._addVocabularies(),s.default.forEach(e=>this.addVocabulary(e)),this.opts.discriminator&&this.addKeyword(o.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(a,i):a;this.addMetaSchema(e,c,!1),this.refs["http://json-schema.org/schema"]=c}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(c)?c:void 0)}}t.Ajv=l,e.exports=t=l,e.exports.Ajv=l,Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var u=r(8452);Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return u.KeywordCxt}});var d=r(111);Object.defineProperty(t,"_",{enumerable:!0,get:function(){return d._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return d.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return d.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return d.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return d.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return d.CodeGen}});var p=r(2036);Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return p.default}});var h=r(2877);Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return h.default}})},8997:e=>{"use strict";e.exports=e=>{const t=typeof e;return null!==e&&("object"===t||"function"===t)}},8999:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>void 0===t?n.str`must contain at least ${e} valid item(s)`:n.str`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>void 0===t?n._`{minContains: ${e}}`:n._`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:o,data:a,it:i}=e;let c,l;const{minContains:u,maxContains:d}=o;i.opts.next?(c=void 0===u?1:u,l=d):c=1;const p=t.const("len",n._`${a}.length`);if(e.setParams({min:c,max:l}),void 0===l&&0===c)return void(0,s.checkStrictMode)(i,'"minContains" == 0 without "maxContains": "contains" keyword ignored');if(void 0!==l&&c>l)return(0,s.checkStrictMode)(i,'"minContains" > "maxContains" is always invalid'),void e.fail();if((0,s.alwaysValidSchema)(i,r)){let t=n._`${p} >= ${c}`;return void 0!==l&&(t=n._`${t} && ${p} <= ${l}`),void e.pass(t)}i.items=!0;const h=t.name("valid");function f(){const e=t.name("_valid"),r=t.let("count",0);m(e,()=>t.if(e,()=>function(e){t.code(n._`${e}++`),void 0===l?t.if(n._`${e} >= ${c}`,()=>t.assign(h,!0).break()):(t.if(n._`${e} > ${l}`,()=>t.assign(h,!1).break()),1===c?t.assign(h,!0):t.if(n._`${e} >= ${c}`,()=>t.assign(h,!0)))}(r)))}function m(r,n){t.forRange("i",0,p,t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:s.Type.Num,compositeRule:!0},r),n()})}void 0===l&&1===c?m(h,()=>t.if(h,()=>t.break())):0===c?(t.let(h,!0),void 0!==l&&t.if(n._`${a}.length > 0`,f)):(t.let(h,!1),f()),e.result(h,()=>e.reset())}};t.default=o},9023:e=>{"use strict";e.exports=require("util")},9045:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;const n=r(303),s=r(1085),o=r(3789),a=r(6735),i=r(5219),c=r(7083),l={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:s}=e,{baseId:a,schemaEnv:c,validateName:l,opts:p,self:h}=s,{root:f}=c;if(("#"===r||"#/"===r)&&a===f.baseId)return function(){if(c===f)return d(e,l,c,c.$async);const r=t.scopeValue("root",{ref:f});return d(e,o._`${r}.validate`,f,f.$async)}();const m=i.resolveRef.call(h,f,a,r);if(void 0===m)throw new n.default(s.opts.uriResolver,a,r);return m instanceof i.SchemaEnv?function(t){const r=u(e,t);d(e,r,t,t.$async)}(m):function(n){const s=t.scopeValue("schema",!0===p.code.source?{ref:n,code:(0,o.stringify)(n)}:{ref:n}),a=t.name("valid"),i=e.subschema({schema:n,dataTypes:[],schemaPath:o.nil,topSchemaRef:s,errSchemaPath:r},a);e.mergeEvaluated(i),e.ok(a)}(m)}};function u(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):o._`${r.scopeValue("wrapper",{ref:t})}.validate`}function d(e,t,r,n){const{gen:i,it:l}=e,{allErrors:u,schemaEnv:d,opts:p}=l,h=p.passContext?a.default.this:o.nil;function f(e){const t=o._`${e}.errors`;i.assign(a.default.vErrors,o._`${a.default.vErrors} === null ? ${t} : ${a.default.vErrors}.concat(${t})`),i.assign(a.default.errors,o._`${a.default.vErrors}.length`)}function m(e){var t;if(!l.opts.unevaluated)return;const n=null===(t=null==r?void 0:r.validate)||void 0===t?void 0:t.evaluated;if(!0!==l.props)if(n&&!n.dynamicProps)void 0!==n.props&&(l.props=c.mergeEvaluated.props(i,n.props,l.props));else{const t=i.var("props",o._`${e}.evaluated.props`);l.props=c.mergeEvaluated.props(i,t,l.props,o.Name)}if(!0!==l.items)if(n&&!n.dynamicItems)void 0!==n.items&&(l.items=c.mergeEvaluated.items(i,n.items,l.items));else{const t=i.var("items",o._`${e}.evaluated.items`);l.items=c.mergeEvaluated.items(i,t,l.items,o.Name)}}n?function(){if(!d.$async)throw new Error("async schema referenced by sync schema");const r=i.let("valid");i.try(()=>{i.code(o._`await ${(0,s.callValidateCode)(e,t,h)}`),m(t),u||i.assign(r,!0)},e=>{i.if(o._`!(${e} instanceof ${l.ValidationError})`,()=>i.throw(e)),f(e),u||i.assign(r,!1)}),e.ok(r)}():e.result((0,s.callValidateCode)(e,t,h),()=>m(t),()=>f(t))}t.getValidate=u,t.callRef=d,t.default=l},9049:e=>{"use strict";e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')},9067:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressCallbackTransform=void 0;const n=r(2203);class s extends n.Transform{constructor(e,t,r){super(),this.total=e,this.cancellationToken=t,this.onProgress=r,this.start=Date.now(),this.transferred=0,this.delta=0,this.nextUpdate=this.start+1e3}_transform(e,t,r){if(this.cancellationToken.cancelled)return void r(new Error("cancelled"),null);this.transferred+=e.length,this.delta+=e.length;const n=Date.now();n>=this.nextUpdate&&this.transferred!==this.total&&(this.nextUpdate=n+1e3,this.onProgress({total:this.total,delta:this.delta,transferred:this.transferred,percent:this.transferred/this.total*100,bytesPerSecond:Math.round(this.transferred/((n-this.start)/1e3))}),this.delta=0),r(null,e)}_flush(e){this.cancellationToken.cancelled?e(new Error("cancelled")):(this.onProgress({total:this.total,delta:this.delta,transferred:this.total,percent:100,bytesPerSecond:Math.round(this.transferred/((Date.now()-this.start)/1e3))}),this.delta=0,e(null))}}t.ProgressCallbackTransform=s},9087:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;const n=r(111),s=r(7193),o=r(1039),a=r(9270);function i(e){const{gen:t,data:r,it:s}=e;t.if(s.parentData,()=>t.assign(r,n._`${s.parentData}[${s.parentDataProperty}]`))}function c(e,t,r){if(void 0===r)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword","function"==typeof r?{ref:r}:{ref:r,code:(0,n.stringify)(r)})}t.macroKeywordCode=function(e,t){const{gen:r,keyword:s,schema:o,parentSchema:a,it:i}=e,l=t.macro.call(i.self,o,a,i),u=c(r,s,l);!1!==i.opts.validateSchema&&i.self.validateSchema(l,!0);const d=r.name("valid");e.subschema({schema:l,schemaPath:n.nil,errSchemaPath:`${i.errSchemaPath}/${s}`,topSchemaRef:u,compositeRule:!0},d),e.pass(d,()=>e.error(!0))},t.funcKeywordCode=function(e,t){var r;const{gen:l,keyword:u,schema:d,parentSchema:p,$data:h,it:f}=e;!function({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}(f,t);const m=!h&&t.compile?t.compile.call(f.self,d,p,f):t.validate,g=c(l,u,m),y=l.let("valid");function v(r=(t.async?n._`await `:n.nil)){const a=f.opts.passContext?s.default.this:s.default.self,i=!("compile"in t&&!h||!1===t.schema);l.assign(y,n._`${r}${(0,o.callValidateCode)(e,g,a,i)}`,t.modifying)}function w(e){var r;l.if((0,n.not)(null!==(r=t.valid)&&void 0!==r?r:y),e)}e.block$data(y,function(){if(!1===t.errors)v(),t.modifying&&i(e),w(()=>e.error());else{const r=t.async?function(){const e=l.let("ruleErrs",null);return l.try(()=>v(n._`await `),t=>l.assign(y,!1).if(n._`${t} instanceof ${f.ValidationError}`,()=>l.assign(e,n._`${t}.errors`),()=>l.throw(t))),e}():function(){const e=n._`${g}.errors`;return l.assign(e,null),v(n.nil),e}();t.modifying&&i(e),w(()=>function(e,t){const{gen:r}=e;r.if(n._`Array.isArray(${t})`,()=>{r.assign(s.default.vErrors,n._`${s.default.vErrors} === null ? ${t} : ${s.default.vErrors}.concat(${t})`).assign(s.default.errors,n._`${s.default.vErrors}.length`),(0,a.extendErrors)(e)},()=>e.error())}(e,r))}}),e.ok(null!==(r=t.valid)&&void 0!==r?r:y)},t.validSchemaType=function(e,t,r=!1){return!t.length||t.some(t=>"array"===t?Array.isArray(e):"object"===t?e&&"object"==typeof e&&!Array.isArray(e):typeof e==t||r&&void 0===e)},t.validateKeywordUsage=function({schema:e,opts:t,self:r,errSchemaPath:n},s,o){if(Array.isArray(s.keyword)?!s.keyword.includes(o):s.keyword!==o)throw new Error("ajv implementation error");const a=s.dependencies;if(null==a?void 0:a.some(t=>!Object.prototype.hasOwnProperty.call(e,t)))throw new Error(`parent schema must have dependencies of ${o}: ${a.join(",")}`);if(s.validateSchema&&!s.validateSchema(e[o])){const e=`keyword "${o}" value is invalid at path "${n}": `+r.errorsText(s.validateSchema.errors);if("log"!==t.validateSchema)throw new Error(e);r.logger.error(e)}}},9198:(e,t,r)=>{"use strict";var n=r(5388);e.exports=new n("tag:yaml.org,2002:null",{kind:"scalar",resolve:function(e){if(null===e)return!0;var t=e.length;return 1===t&&"~"===e||4===t&&("null"===e||"Null"===e||"NULL"===e)},construct:function(){return null},predicate:function(e){return null===e},represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"})},9243:(e,t,r)=>{"use strict";var n=r(8433),s=r(1231),o=r(8083),a=r(5489),i=Object.prototype.hasOwnProperty,c=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,l=/[\x85\u2028\u2029]/,u=/[,\[\]\{\}]/,d=/^(?:!|!!|![a-z\-]+!)$/i,p=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function h(e){return Object.prototype.toString.call(e)}function f(e){return 10===e||13===e}function m(e){return 9===e||32===e}function g(e){return 9===e||32===e||10===e||13===e}function y(e){return 44===e||91===e||93===e||123===e||125===e}function v(e){var t;return 48<=e&&e<=57?e-48:97<=(t=32|e)&&t<=102?t-97+10:-1}function w(e){return 120===e?2:117===e?4:85===e?8:0}function _(e){return 48<=e&&e<=57?e-48:-1}function b(e){return 48===e?"\0":97===e?"":98===e?"\b":116===e||9===e?"\t":110===e?"\n":118===e?"\v":102===e?"\f":114===e?"\r":101===e?"":32===e?" ":34===e?'"':47===e?"/":92===e?"\\":78===e?"":95===e?" ":76===e?"\u2028":80===e?"\u2029":""}function $(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(55296+(e-65536>>10),56320+(e-65536&1023))}for(var E=new Array(256),S=new Array(256),P=0;P<256;P++)E[P]=b(P)?1:0,S[P]=b(P);function O(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||a,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function C(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=o(r),new s(t,r)}function N(e,t){throw C(e,t)}function I(e,t){e.onWarning&&e.onWarning.call(null,C(e,t))}var T={YAML:function(e,t,r){var n,s,o;null!==e.version&&N(e,"duplication of %YAML directive"),1!==r.length&&N(e,"YAML directive accepts exactly one argument"),null===(n=/^([0-9]+)\.([0-9]+)$/.exec(r[0]))&&N(e,"ill-formed argument of the YAML directive"),s=parseInt(n[1],10),o=parseInt(n[2],10),1!==s&&N(e,"unacceptable YAML version of the document"),e.version=r[0],e.checkLineBreaks=o<2,1!==o&&2!==o&&I(e,"unsupported YAML version of the document")},TAG:function(e,t,r){var n,s;2!==r.length&&N(e,"TAG directive accepts exactly two arguments"),n=r[0],s=r[1],d.test(n)||N(e,"ill-formed tag handle (first argument) of the TAG directive"),i.call(e.tagMap,n)&&N(e,'there is a previously declared suffix for "'+n+'" tag handle'),p.test(s)||N(e,"ill-formed tag prefix (second argument) of the TAG directive");try{s=decodeURIComponent(s)}catch(t){N(e,"tag prefix is malformed: "+s)}e.tagMap[n]=s}};function A(e,t,r,n){var s,o,a,i;if(t<r){if(i=e.input.slice(t,r),n)for(s=0,o=i.length;s<o;s+=1)9===(a=i.charCodeAt(s))||32<=a&&a<=1114111||N(e,"expected valid JSON character");else c.test(i)&&N(e,"the stream contains non-printable characters");e.result+=i}}function k(e,t,r,s){var o,a,c,l;for(n.isObject(r)||N(e,"cannot merge mappings; the provided source object is unacceptable"),c=0,l=(o=Object.keys(r)).length;c<l;c+=1)a=o[c],i.call(t,a)||(t[a]=r[a],s[a]=!0)}function R(e,t,r,n,s,o,a,c,l){var u,d;if(Array.isArray(s))for(u=0,d=(s=Array.prototype.slice.call(s)).length;u<d;u+=1)Array.isArray(s[u])&&N(e,"nested arrays are not supported inside keys"),"object"==typeof s&&"[object Object]"===h(s[u])&&(s[u]="[object Object]");if("object"==typeof s&&"[object Object]"===h(s)&&(s="[object Object]"),s=String(s),null===t&&(t={}),"tag:yaml.org,2002:merge"===n)if(Array.isArray(o))for(u=0,d=o.length;u<d;u+=1)k(e,t,o[u],r);else k(e,t,o,r);else e.json||i.call(r,s)||!i.call(t,s)||(e.line=a||e.line,e.lineStart=c||e.lineStart,e.position=l||e.position,N(e,"duplicated mapping key")),"__proto__"===s?Object.defineProperty(t,s,{configurable:!0,enumerable:!0,writable:!0,value:o}):t[s]=o,delete r[s];return t}function x(e){var t;10===(t=e.input.charCodeAt(e.position))?e.position++:13===t?(e.position++,10===e.input.charCodeAt(e.position)&&e.position++):N(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}function D(e,t,r){for(var n=0,s=e.input.charCodeAt(e.position);0!==s;){for(;m(s);)9===s&&-1===e.firstTabInLine&&(e.firstTabInLine=e.position),s=e.input.charCodeAt(++e.position);if(t&&35===s)do{s=e.input.charCodeAt(++e.position)}while(10!==s&&13!==s&&0!==s);if(!f(s))break;for(x(e),s=e.input.charCodeAt(e.position),n++,e.lineIndent=0;32===s;)e.lineIndent++,s=e.input.charCodeAt(++e.position)}return-1!==r&&0!==n&&e.lineIndent<r&&I(e,"deficient indentation"),n}function j(e){var t,r=e.position;return!(45!==(t=e.input.charCodeAt(r))&&46!==t||t!==e.input.charCodeAt(r+1)||t!==e.input.charCodeAt(r+2)||(r+=3,0!==(t=e.input.charCodeAt(r))&&!g(t)))}function M(e,t){1===t?e.result+=" ":t>1&&(e.result+=n.repeat("\n",t-1))}function U(e,t){var r,n,s=e.tag,o=e.anchor,a=[],i=!1;if(-1!==e.firstTabInLine)return!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=a),n=e.input.charCodeAt(e.position);0!==n&&(-1!==e.firstTabInLine&&(e.position=e.firstTabInLine,N(e,"tab characters must not be used in indentation")),45===n)&&g(e.input.charCodeAt(e.position+1));)if(i=!0,e.position++,D(e,!0,-1)&&e.lineIndent<=t)a.push(null),n=e.input.charCodeAt(e.position);else if(r=e.line,z(e,t,3,!1,!0),a.push(e.result),D(e,!0,-1),n=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&0!==n)N(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break;return!!i&&(e.tag=s,e.anchor=o,e.kind="sequence",e.result=a,!0)}function F(e){var t,r,n,s,o=!1,a=!1;if(33!==(s=e.input.charCodeAt(e.position)))return!1;if(null!==e.tag&&N(e,"duplication of a tag property"),60===(s=e.input.charCodeAt(++e.position))?(o=!0,s=e.input.charCodeAt(++e.position)):33===s?(a=!0,r="!!",s=e.input.charCodeAt(++e.position)):r="!",t=e.position,o){do{s=e.input.charCodeAt(++e.position)}while(0!==s&&62!==s);e.position<e.length?(n=e.input.slice(t,e.position),s=e.input.charCodeAt(++e.position)):N(e,"unexpected end of the stream within a verbatim tag")}else{for(;0!==s&&!g(s);)33===s&&(a?N(e,"tag suffix cannot contain exclamation marks"):(r=e.input.slice(t-1,e.position+1),d.test(r)||N(e,"named tag handle cannot contain such characters"),a=!0,t=e.position+1)),s=e.input.charCodeAt(++e.position);n=e.input.slice(t,e.position),u.test(n)&&N(e,"tag suffix cannot contain flow indicator characters")}n&&!p.test(n)&&N(e,"tag name cannot contain such characters: "+n);try{n=decodeURIComponent(n)}catch(t){N(e,"tag name is malformed: "+n)}return o?e.tag=n:i.call(e.tagMap,r)?e.tag=e.tagMap[r]+n:"!"===r?e.tag="!"+n:"!!"===r?e.tag="tag:yaml.org,2002:"+n:N(e,'undeclared tag handle "'+r+'"'),!0}function L(e){var t,r;if(38!==(r=e.input.charCodeAt(e.position)))return!1;for(null!==e.anchor&&N(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;0!==r&&!g(r)&&!y(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&N(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function z(e,t,r,s,o){var a,c,l,u,d,p,h,b,P,O=1,C=!1,I=!1;if(null!==e.listener&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,a=c=l=4===r||3===r,s&&D(e,!0,-1)&&(C=!0,e.lineIndent>t?O=1:e.lineIndent===t?O=0:e.lineIndent<t&&(O=-1)),1===O)for(;F(e)||L(e);)D(e,!0,-1)?(C=!0,l=a,e.lineIndent>t?O=1:e.lineIndent===t?O=0:e.lineIndent<t&&(O=-1)):l=!1;if(l&&(l=C||o),1!==O&&4!==r||(b=1===r||2===r?t:t+1,P=e.position-e.lineStart,1===O?l&&(U(e,P)||function(e,t,r){var n,s,o,a,i,c,l,u=e.tag,d=e.anchor,p={},h=Object.create(null),f=null,y=null,v=null,w=!1,_=!1;if(-1!==e.firstTabInLine)return!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=p),l=e.input.charCodeAt(e.position);0!==l;){if(w||-1===e.firstTabInLine||(e.position=e.firstTabInLine,N(e,"tab characters must not be used in indentation")),n=e.input.charCodeAt(e.position+1),o=e.line,63!==l&&58!==l||!g(n)){if(a=e.line,i=e.lineStart,c=e.position,!z(e,r,2,!1,!0))break;if(e.line===o){for(l=e.input.charCodeAt(e.position);m(l);)l=e.input.charCodeAt(++e.position);if(58===l)g(l=e.input.charCodeAt(++e.position))||N(e,"a whitespace character is expected after the key-value separator within a block mapping"),w&&(R(e,p,h,f,y,null,a,i,c),f=y=v=null),_=!0,w=!1,s=!1,f=e.tag,y=e.result;else{if(!_)return e.tag=u,e.anchor=d,!0;N(e,"can not read an implicit mapping pair; a colon is missed")}}else{if(!_)return e.tag=u,e.anchor=d,!0;N(e,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===l?(w&&(R(e,p,h,f,y,null,a,i,c),f=y=v=null),_=!0,w=!0,s=!0):w?(w=!1,s=!0):N(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,l=n;if((e.line===o||e.lineIndent>t)&&(w&&(a=e.line,i=e.lineStart,c=e.position),z(e,t,4,!0,s)&&(w?y=e.result:v=e.result),w||(R(e,p,h,f,y,v,a,i,c),f=y=v=null),D(e,!0,-1),l=e.input.charCodeAt(e.position)),(e.line===o||e.lineIndent>t)&&0!==l)N(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return w&&R(e,p,h,f,y,null,a,i,c),_&&(e.tag=u,e.anchor=d,e.kind="mapping",e.result=p),_}(e,P,b))||function(e,t){var r,n,s,o,a,i,c,l,u,d,p,h,f=!0,m=e.tag,y=e.anchor,v=Object.create(null);if(91===(h=e.input.charCodeAt(e.position)))a=93,l=!1,o=[];else{if(123!==h)return!1;a=125,l=!0,o={}}for(null!==e.anchor&&(e.anchorMap[e.anchor]=o),h=e.input.charCodeAt(++e.position);0!==h;){if(D(e,!0,t),(h=e.input.charCodeAt(e.position))===a)return e.position++,e.tag=m,e.anchor=y,e.kind=l?"mapping":"sequence",e.result=o,!0;f?44===h&&N(e,"expected the node content, but found ','"):N(e,"missed comma between flow collection entries"),p=null,i=c=!1,63===h&&g(e.input.charCodeAt(e.position+1))&&(i=c=!0,e.position++,D(e,!0,t)),r=e.line,n=e.lineStart,s=e.position,z(e,t,1,!1,!0),d=e.tag,u=e.result,D(e,!0,t),h=e.input.charCodeAt(e.position),!c&&e.line!==r||58!==h||(i=!0,h=e.input.charCodeAt(++e.position),D(e,!0,t),z(e,t,1,!1,!0),p=e.result),l?R(e,o,v,d,u,p,r,n,s):i?o.push(R(e,null,v,d,u,p,r,n,s)):o.push(u),D(e,!0,t),44===(h=e.input.charCodeAt(e.position))?(f=!0,h=e.input.charCodeAt(++e.position)):f=!1}N(e,"unexpected end of the stream within a flow collection")}(e,b)?I=!0:(c&&function(e,t){var r,s,o,a,i=1,c=!1,l=!1,u=t,d=0,p=!1;if(124===(a=e.input.charCodeAt(e.position)))s=!1;else{if(62!==a)return!1;s=!0}for(e.kind="scalar",e.result="";0!==a;)if(43===(a=e.input.charCodeAt(++e.position))||45===a)1===i?i=43===a?3:2:N(e,"repeat of a chomping mode identifier");else{if(!((o=_(a))>=0))break;0===o?N(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):l?N(e,"repeat of an indentation width identifier"):(u=t+o-1,l=!0)}if(m(a)){do{a=e.input.charCodeAt(++e.position)}while(m(a));if(35===a)do{a=e.input.charCodeAt(++e.position)}while(!f(a)&&0!==a)}for(;0!==a;){for(x(e),e.lineIndent=0,a=e.input.charCodeAt(e.position);(!l||e.lineIndent<u)&&32===a;)e.lineIndent++,a=e.input.charCodeAt(++e.position);if(!l&&e.lineIndent>u&&(u=e.lineIndent),f(a))d++;else{if(e.lineIndent<u){3===i?e.result+=n.repeat("\n",c?1+d:d):1===i&&c&&(e.result+="\n");break}for(s?m(a)?(p=!0,e.result+=n.repeat("\n",c?1+d:d)):p?(p=!1,e.result+=n.repeat("\n",d+1)):0===d?c&&(e.result+=" "):e.result+=n.repeat("\n",d):e.result+=n.repeat("\n",c?1+d:d),c=!0,l=!0,d=0,r=e.position;!f(a)&&0!==a;)a=e.input.charCodeAt(++e.position);A(e,r,e.position,!1)}}return!0}(e,b)||function(e,t){var r,n,s;if(39!==(r=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,n=s=e.position;0!==(r=e.input.charCodeAt(e.position));)if(39===r){if(A(e,n,e.position,!0),39!==(r=e.input.charCodeAt(++e.position)))return!0;n=e.position,e.position++,s=e.position}else f(r)?(A(e,n,s,!0),M(e,D(e,!1,t)),n=s=e.position):e.position===e.lineStart&&j(e)?N(e,"unexpected end of the document within a single quoted scalar"):(e.position++,s=e.position);N(e,"unexpected end of the stream within a single quoted scalar")}(e,b)||function(e,t){var r,n,s,o,a,i;if(34!==(i=e.input.charCodeAt(e.position)))return!1;for(e.kind="scalar",e.result="",e.position++,r=n=e.position;0!==(i=e.input.charCodeAt(e.position));){if(34===i)return A(e,r,e.position,!0),e.position++,!0;if(92===i){if(A(e,r,e.position,!0),f(i=e.input.charCodeAt(++e.position)))D(e,!1,t);else if(i<256&&E[i])e.result+=S[i],e.position++;else if((a=w(i))>0){for(s=a,o=0;s>0;s--)(a=v(i=e.input.charCodeAt(++e.position)))>=0?o=(o<<4)+a:N(e,"expected hexadecimal character");e.result+=$(o),e.position++}else N(e,"unknown escape sequence");r=n=e.position}else f(i)?(A(e,r,n,!0),M(e,D(e,!1,t)),r=n=e.position):e.position===e.lineStart&&j(e)?N(e,"unexpected end of the document within a double quoted scalar"):(e.position++,n=e.position)}N(e,"unexpected end of the stream within a double quoted scalar")}(e,b)?I=!0:function(e){var t,r,n;if(42!==(n=e.input.charCodeAt(e.position)))return!1;for(n=e.input.charCodeAt(++e.position),t=e.position;0!==n&&!g(n)&&!y(n);)n=e.input.charCodeAt(++e.position);return e.position===t&&N(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),i.call(e.anchorMap,r)||N(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],D(e,!0,-1),!0}(e)?(I=!0,null===e.tag&&null===e.anchor||N(e,"alias node should not have any properties")):function(e,t,r){var n,s,o,a,i,c,l,u,d=e.kind,p=e.result;if(g(u=e.input.charCodeAt(e.position))||y(u)||35===u||38===u||42===u||33===u||124===u||62===u||39===u||34===u||37===u||64===u||96===u)return!1;if((63===u||45===u)&&(g(n=e.input.charCodeAt(e.position+1))||r&&y(n)))return!1;for(e.kind="scalar",e.result="",s=o=e.position,a=!1;0!==u;){if(58===u){if(g(n=e.input.charCodeAt(e.position+1))||r&&y(n))break}else if(35===u){if(g(e.input.charCodeAt(e.position-1)))break}else{if(e.position===e.lineStart&&j(e)||r&&y(u))break;if(f(u)){if(i=e.line,c=e.lineStart,l=e.lineIndent,D(e,!1,-1),e.lineIndent>=t){a=!0,u=e.input.charCodeAt(e.position);continue}e.position=o,e.line=i,e.lineStart=c,e.lineIndent=l;break}}a&&(A(e,s,o,!1),M(e,e.line-i),s=o=e.position,a=!1),m(u)||(o=e.position+1),u=e.input.charCodeAt(++e.position)}return A(e,s,o,!1),!!e.result||(e.kind=d,e.result=p,!1)}(e,b,1===r)&&(I=!0,null===e.tag&&(e.tag="?")),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):0===O&&(I=l&&U(e,P))),null===e.tag)null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);else if("?"===e.tag){for(null!==e.result&&"scalar"!==e.kind&&N(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),u=0,d=e.implicitTypes.length;u<d;u+=1)if((h=e.implicitTypes[u]).resolve(e.result)){e.result=h.construct(e.result),e.tag=h.tag,null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);break}}else if("!"!==e.tag){if(i.call(e.typeMap[e.kind||"fallback"],e.tag))h=e.typeMap[e.kind||"fallback"][e.tag];else for(h=null,u=0,d=(p=e.typeMap.multi[e.kind||"fallback"]).length;u<d;u+=1)if(e.tag.slice(0,p[u].tag.length)===p[u].tag){h=p[u];break}h||N(e,"unknown tag !<"+e.tag+">"),null!==e.result&&h.kind!==e.kind&&N(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+h.kind+'", not "'+e.kind+'"'),h.resolve(e.result,e.tag)?(e.result=h.construct(e.result,e.tag),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):N(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return null!==e.listener&&e.listener("close",e),null!==e.tag||null!==e.anchor||I}function V(e){var t,r,n,s,o=e.position,a=!1;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);0!==(s=e.input.charCodeAt(e.position))&&(D(e,!0,-1),s=e.input.charCodeAt(e.position),!(e.lineIndent>0||37!==s));){for(a=!0,s=e.input.charCodeAt(++e.position),t=e.position;0!==s&&!g(s);)s=e.input.charCodeAt(++e.position);for(n=[],(r=e.input.slice(t,e.position)).length<1&&N(e,"directive name must not be less than one character in length");0!==s;){for(;m(s);)s=e.input.charCodeAt(++e.position);if(35===s){do{s=e.input.charCodeAt(++e.position)}while(0!==s&&!f(s));break}if(f(s))break;for(t=e.position;0!==s&&!g(s);)s=e.input.charCodeAt(++e.position);n.push(e.input.slice(t,e.position))}0!==s&&x(e),i.call(T,r)?T[r](e,r,n):I(e,'unknown document directive "'+r+'"')}D(e,!0,-1),0===e.lineIndent&&45===e.input.charCodeAt(e.position)&&45===e.input.charCodeAt(e.position+1)&&45===e.input.charCodeAt(e.position+2)?(e.position+=3,D(e,!0,-1)):a&&N(e,"directives end mark is expected"),z(e,e.lineIndent-1,4,!1,!0),D(e,!0,-1),e.checkLineBreaks&&l.test(e.input.slice(o,e.position))&&I(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&j(e)?46===e.input.charCodeAt(e.position)&&(e.position+=3,D(e,!0,-1)):e.position<e.length-1&&N(e,"end of the stream or a document separator is expected")}function q(e,t){t=t||{},0!==(e=String(e)).length&&(10!==e.charCodeAt(e.length-1)&&13!==e.charCodeAt(e.length-1)&&(e+="\n"),65279===e.charCodeAt(0)&&(e=e.slice(1)));var r=new O(e,t),n=e.indexOf("\0");for(-1!==n&&(r.position=n,N(r,"null byte is not allowed in input")),r.input+="\0";32===r.input.charCodeAt(r.position);)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)V(r);return r.documents}e.exports.loadAll=function(e,t,r){null!==t&&"object"==typeof t&&void 0===r&&(r=t,t=null);var n=q(e,r);if("function"!=typeof t)return n;for(var s=0,o=n.length;s<o;s+=1)t(n[s])},e.exports.load=function(e,t){var r=q(e,t);if(0!==r.length){if(1===r.length)return r[0];throw new s("expected a single document in the stream, but found more")}}},9259:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s=r(7083),o={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>n._`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:o,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(a.opts.discriminator&&o.discriminator)return;const i=r,c=t.let("valid",!1),l=t.let("passing",null),u=t.name("_valid");e.setParams({passing:l}),t.block(function(){i.forEach((r,o)=>{let i;(0,s.alwaysValidSchema)(a,r)?t.var(u,!0):i=e.subschema({keyword:"oneOf",schemaProp:o,compositeRule:!0},u),o>0&&t.if(n._`${u} && ${c}`).assign(c,!1).assign(l,n._`[${l}, ${o}]`).else(),t.if(u,()=>{t.assign(c,!0),t.assign(l,o),i&&e.mergeEvaluated(i,n.Name)})})}),e.result(c,()=>e.reset(),()=>e.error(!0))}};t.default=o},9270:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;const n=r(111),s=r(5273),o=r(7193);function a(e,t){const r=e.const("err",t);e.if(n._`${o.default.vErrors} === null`,()=>e.assign(o.default.vErrors,n._`[${r}]`),n._`${o.default.vErrors}.push(${r})`),e.code(n._`${o.default.errors}++`)}function i(e,t){const{gen:r,validateName:s,schemaEnv:o}=e;o.$async?r.throw(n._`new ${e.ValidationError}(${t})`):(r.assign(n._`${s}.errors`,t),r.return(!1))}t.keywordError={message:({keyword:e})=>n.str`must pass "${e}" keyword validation`},t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?n.str`"${e}" keyword must be ${t} ($data)`:n.str`"${e}" keyword is invalid ($data)`},t.reportError=function(e,r=t.keywordError,s,o){const{it:c}=e,{gen:u,compositeRule:d,allErrors:p}=c,h=l(e,r,s);(null!=o?o:d||p)?a(u,h):i(c,n._`[${h}]`)},t.reportExtraError=function(e,r=t.keywordError,n){const{it:s}=e,{gen:c,compositeRule:u,allErrors:d}=s;a(c,l(e,r,n)),u||d||i(s,o.default.vErrors)},t.resetErrorsCount=function(e,t){e.assign(o.default.errors,t),e.if(n._`${o.default.vErrors} !== null`,()=>e.if(t,()=>e.assign(n._`${o.default.vErrors}.length`,t),()=>e.assign(o.default.vErrors,null)))},t.extendErrors=function({gen:e,keyword:t,schemaValue:r,data:s,errsCount:a,it:i}){if(void 0===a)throw new Error("ajv implementation error");const c=e.name("err");e.forRange("i",a,o.default.errors,a=>{e.const(c,n._`${o.default.vErrors}[${a}]`),e.if(n._`${c}.instancePath === undefined`,()=>e.assign(n._`${c}.instancePath`,(0,n.strConcat)(o.default.instancePath,i.errorPath))),e.assign(n._`${c}.schemaPath`,n.str`${i.errSchemaPath}/${t}`),i.opts.verbose&&(e.assign(n._`${c}.schema`,r),e.assign(n._`${c}.data`,s))})};const c={keyword:new n.Name("keyword"),schemaPath:new n.Name("schemaPath"),params:new n.Name("params"),propertyName:new n.Name("propertyName"),message:new n.Name("message"),schema:new n.Name("schema"),parentSchema:new n.Name("parentSchema")};function l(e,t,r){const{createErrors:s}=e.it;return!1===s?n._`{}`:function(e,t,r={}){const{gen:s,it:a}=e,i=[u(a,r),d(e,r)];return function(e,{params:t,message:r},s){const{keyword:a,data:i,schemaValue:l,it:u}=e,{opts:d,propertyName:p,topSchemaRef:h,schemaPath:f}=u;s.push([c.keyword,a],[c.params,"function"==typeof t?t(e):t||n._`{}`]),d.messages&&s.push([c.message,"function"==typeof r?r(e):r]),d.verbose&&s.push([c.schema,l],[c.parentSchema,n._`${h}${f}`],[o.default.data,i]),p&&s.push([c.propertyName,p])}(e,t,i),s.object(...i)}(e,t,r)}function u({errorPath:e},{instancePath:t}){const r=t?n.str`${e}${(0,s.getErrorPath)(t,s.Type.Str)}`:e;return[o.default.instancePath,(0,n.strConcat)(o.default.instancePath,r)]}function d({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:o}){let a=o?t:n.str`${t}/${e}`;return r&&(a=n.str`${a}${(0,s.getErrorPath)(r,s.Type.Str)}`),[c.schemaPath,a]}},9277:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(5273),s={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:s}=e;if((0,n.alwaysValidSchema)(s,r))return void e.fail();const o=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},o),e.failResult(o,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}};t.default=s},9318:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.XElement=void 0,t.parseXml=function(e){let t=null;const r=n.parser(!0,{}),s=[];return r.onopentag=e=>{const r=new o(e.name);if(r.attributes=e.attributes,null===t)t=r;else{const e=s[s.length-1];null==e.elements&&(e.elements=[]),e.elements.push(r)}s.push(r)},r.onclosetag=()=>{s.pop()},r.ontext=e=>{s.length>0&&(s[s.length-1].value=e)},r.oncdata=e=>{const t=s[s.length-1];t.value=e,t.isCData=!0},r.onerror=e=>{throw e},r.write(e),t};const n=r(4043),s=r(4261);class o{constructor(e){if(this.name=e,this.value="",this.attributes=null,this.isCData=!1,this.elements=null,!e)throw(0,s.newError)("Element name cannot be empty","ERR_XML_ELEMENT_NAME_EMPTY");if(!function(e){return a.test(e)}(e))throw(0,s.newError)(`Invalid element name: ${e}`,"ERR_XML_ELEMENT_INVALID_NAME")}attribute(e){const t=null===this.attributes?null:this.attributes[e];if(null==t)throw(0,s.newError)(`No attribute "${e}"`,"ERR_XML_MISSED_ATTRIBUTE");return t}removeAttribute(e){null!==this.attributes&&delete this.attributes[e]}element(e,t=!1,r=null){const n=this.elementOrNull(e,t);if(null===n)throw(0,s.newError)(r||`No element "${e}"`,"ERR_XML_MISSED_ELEMENT");return n}elementOrNull(e,t=!1){if(null===this.elements)return null;for(const r of this.elements)if(i(r,e,t))return r;return null}getElements(e,t=!1){return null===this.elements?[]:this.elements.filter(r=>i(r,e,t))}elementValueOrEmpty(e,t=!1){const r=this.elementOrNull(e,t);return null===r?"":r.value}}t.XElement=o;const a=new RegExp(/^[A-Za-z_][:A-Za-z0-9_-]*$/i);function i(e,t,r){const n=e.name;return n===t||!0===r&&n.length===t.length&&n.toLowerCase()===t.toLowerCase()}},9322:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BaseUpdater=void 0;const n=r(5317),s=r(4718);class o extends s.AppUpdater{constructor(e,t){super(e,t),this.quitAndInstallCalled=!1,this.quitHandlerAdded=!1}quitAndInstall(e=!1,t=!1){this._logger.info("Install on explicit quitAndInstall"),this.install(e,e?t:this.autoRunAppAfterInstall)?setImmediate(()=>{r(4157).autoUpdater.emit("before-quit-for-update"),this.app.quit()}):this.quitAndInstallCalled=!1}executeDownload(e){return super.executeDownload({...e,done:e=>(this.dispatchUpdateDownloaded(e),this.addQuitHandler(),Promise.resolve())})}get installerPath(){return null==this.downloadedUpdateHelper?null:this.downloadedUpdateHelper.file}install(e=!1,t=!1){if(this.quitAndInstallCalled)return this._logger.warn("install call ignored: quitAndInstallCalled is set to true"),!1;const r=this.downloadedUpdateHelper,n=this.installerPath,s=null==r?null:r.downloadedFileInfo;if(null==n||null==s)return this.dispatchError(new Error("No valid update available, can't quit and install")),!1;this.quitAndInstallCalled=!0;try{return this._logger.info(`Install: isSilent: ${e}, isForceRunAfter: ${t}`),this.doInstall({isSilent:e,isForceRunAfter:t,isAdminRightsRequired:s.isAdminRightsRequired})}catch(e){return this.dispatchError(e),!1}}addQuitHandler(){!this.quitHandlerAdded&&this.autoInstallOnAppQuit&&(this.quitHandlerAdded=!0,this.app.onQuit(e=>{this.quitAndInstallCalled?this._logger.info("Update installer has already been triggered. Quitting application."):this.autoInstallOnAppQuit?0===e?(this._logger.info("Auto install update on quit"),this.install(!0,!1)):this._logger.info(`Update will be not installed on quit because application is quitting with exit code ${e}`):this._logger.info("Update will not be installed on quit because autoInstallOnAppQuit is set to false.")}))}wrapSudo(){const{name:e}=this.app,t=`"${e} would like to update"`,r=this.spawnSyncLog("which gksudo || which kdesudo || which pkexec || which beesu"),n=[r];return/kdesudo/i.test(r)?(n.push("--comment",t),n.push("-c")):/gksudo/i.test(r)?n.push("--message",t):/pkexec/i.test(r)&&n.push("--disable-internal-agent"),n.join(" ")}spawnSyncLog(e,t=[],r={}){this._logger.info(`Executing: ${e} with args: ${t}`);const s=(0,n.spawnSync)(e,t,{env:{...process.env,...r},encoding:"utf-8",shell:!0}),{error:o,status:a,stdout:i,stderr:c}=s;if(null!=o)throw this._logger.error(c),o;if(null!=a&&0!==a)throw this._logger.error(c),new Error(`Command ${e} exited with code ${a}`);return i.trim()}async spawnLog(e,t=[],r=void 0,s="ignore"){return this._logger.info(`Executing: ${e} with args: ${t}`),new Promise((o,a)=>{try{const i={stdio:s,env:r,detached:!0},c=(0,n.spawn)(e,t,i);c.on("error",e=>{a(e)}),c.unref(),void 0!==c.pid&&o(!0)}catch(e){a(e)}})}}t.BaseUpdater=o},9342:(e,t,r)=>{"use strict";var n=r(5388),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\n\r";e.exports=new n("tag:yaml.org,2002:binary",{kind:"scalar",resolve:function(e){if(null===e)return!1;var t,r,n=0,o=e.length,a=s;for(r=0;r<o;r++)if(!((t=a.indexOf(e.charAt(r)))>64)){if(t<0)return!1;n+=6}return n%8==0},construct:function(e){var t,r,n=e.replace(/[\r\n=]/g,""),o=n.length,a=s,i=0,c=[];for(t=0;t<o;t++)t%4==0&&t&&(c.push(i>>16&255),c.push(i>>8&255),c.push(255&i)),i=i<<6|a.indexOf(n.charAt(t));return 0==(r=o%4*6)?(c.push(i>>16&255),c.push(i>>8&255),c.push(255&i)):18===r?(c.push(i>>10&255),c.push(i>>2&255)):12===r&&c.push(i>>4&255),new Uint8Array(c)},predicate:function(e){return"[object Uint8Array]"===Object.prototype.toString.call(e)},represent:function(e){var t,r,n="",o=0,a=e.length,i=s;for(t=0;t<a;t++)t%3==0&&t&&(n+=i[o>>18&63],n+=i[o>>12&63],n+=i[o>>6&63],n+=i[63&o]),o=(o<<8)+e[t];return 0==(r=a%3)?(n+=i[o>>18&63],n+=i[o>>12&63],n+=i[o>>6&63],n+=i[63&o]):2===r?(n+=i[o>>10&63],n+=i[o>>4&63],n+=i[o<<2&63],n+=i[64]):1===r&&(n+=i[o>>2&63],n+=i[o<<4&63],n+=i[64],n+=i[64]),n}})},9345:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseDn=function(e){let t=!1,r=null,n="",s=0;e=e.trim();const o=new Map;for(let a=0;a<=e.length;a++){if(a===e.length){null!==r&&o.set(r,n);break}const i=e[a];if(t){if('"'===i){t=!1;continue}}else{if('"'===i){t=!0;continue}if("\\"===i){a++;const t=parseInt(e.slice(a,a+2),16);Number.isNaN(t)?n+=e[a]:(a++,n+=String.fromCharCode(t));continue}if(null===r&&"="===i){r=n,n="";continue}if(","===i||";"===i||"+"===i){null!==r&&o.set(r,n),r=null,n="";continue}}if(" "===i&&!t){if(0===n.length)continue;if(a>s){let t=a;for(;" "===e[t];)t++;s=t}if(s>=e.length||","===e[s]||";"===e[s]||null===r&&"="===e[s]||null!==r&&"+"===e[s]){a=s-1;continue}}n+=i}return o}},9347:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(3789),s={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r="maxItems"===e?"more":"fewer";return n.str`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>n._`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:s}=e,o="maxItems"===t?n.operators.GT:n.operators.LT;e.fail$data(n._`${r}.length ${o} ${s}`)}};t.default=s},9404:e=>{"use strict";var t=e.exports=function(e,t,n){"function"==typeof t&&(n=t,t={}),r(t,"function"==typeof(n=t.cb||n)?n:n.pre||function(){},n.post||function(){},e,"",e)};function r(e,s,o,a,i,c,l,u,d,p){if(a&&"object"==typeof a&&!Array.isArray(a)){for(var h in s(a,i,c,l,u,d,p),a){var f=a[h];if(Array.isArray(f)){if(h in t.arrayKeywords)for(var m=0;m<f.length;m++)r(e,s,o,f[m],i+"/"+h+"/"+m,c,i,h,a,m)}else if(h in t.propsKeywords){if(f&&"object"==typeof f)for(var g in f)r(e,s,o,f[g],i+"/"+h+"/"+n(g),c,i,h,a,g)}else(h in t.keywords||e.allKeys&&!(h in t.skipKeywords))&&r(e,s,o,f,i+"/"+h,c,i,h,a)}o(a,i,c,l,u,d,p)}}function n(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}t.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},t.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},t.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},t.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},9516:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressDifferentialDownloadCallbackTransform=void 0;const n=r(2203);var s;!function(e){e[e.COPY=0]="COPY",e[e.DOWNLOAD=1]="DOWNLOAD"}(s||(s={}));class o extends n.Transform{constructor(e,t,r){super(),this.progressDifferentialDownloadInfo=e,this.cancellationToken=t,this.onProgress=r,this.start=Date.now(),this.transferred=0,this.delta=0,this.expectedBytes=0,this.index=0,this.operationType=s.COPY,this.nextUpdate=this.start+1e3}_transform(e,t,r){if(this.cancellationToken.cancelled)return void r(new Error("cancelled"),null);if(this.operationType==s.COPY)return void r(null,e);this.transferred+=e.length,this.delta+=e.length;const n=Date.now();n>=this.nextUpdate&&this.transferred!==this.expectedBytes&&this.transferred!==this.progressDifferentialDownloadInfo.grandTotal&&(this.nextUpdate=n+1e3,this.onProgress({total:this.progressDifferentialDownloadInfo.grandTotal,delta:this.delta,transferred:this.transferred,percent:this.transferred/this.progressDifferentialDownloadInfo.grandTotal*100,bytesPerSecond:Math.round(this.transferred/((n-this.start)/1e3))}),this.delta=0),r(null,e)}beginFileCopy(){this.operationType=s.COPY}beginRangeDownload(){this.operationType=s.DOWNLOAD,this.expectedBytes+=this.progressDifferentialDownloadInfo.expectedByteCounts[this.index++]}endRangeDownload(){this.transferred!==this.progressDifferentialDownloadInfo.grandTotal&&this.onProgress({total:this.progressDifferentialDownloadInfo.grandTotal,delta:this.delta,transferred:this.transferred,percent:this.transferred/this.progressDifferentialDownloadInfo.grandTotal*100,bytesPerSecond:Math.round(this.transferred/((Date.now()-this.start)/1e3))})}_flush(e){this.cancellationToken.cancelled?e(new Error("cancelled")):(this.onProgress({total:this.progressDifferentialDownloadInfo.grandTotal,delta:this.delta,transferred:this.transferred,percent:100,bytesPerSecond:Math.round(this.transferred/((Date.now()-this.start)/1e3))}),this.delta=0,this.transferred=0,e(null))}}t.ProgressDifferentialDownloadCallbackTransform=o},9543:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;const n=r(3789),s=r(7083);t.getSubschema=function(e,{keyword:t,schemaProp:r,schema:o,schemaPath:a,errSchemaPath:i,topSchemaRef:c}){if(void 0!==t&&void 0!==o)throw new Error('both "keyword" and "schema" passed, only one allowed');if(void 0!==t){const o=e.schema[t];return void 0===r?{schema:o,schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:o[r],schemaPath:n._`${e.schemaPath}${(0,n.getProperty)(t)}${(0,n.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,s.escapeFragment)(r)}`}}if(void 0!==o){if(void 0===a||void 0===i||void 0===c)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:o,schemaPath:a,topSchemaRef:c,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')},t.extendSubschemaData=function(e,t,{dataProp:r,dataPropType:o,data:a,dataTypes:i,propertyName:c}){if(void 0!==a&&void 0!==r)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:l}=t;if(void 0!==r){const{errorPath:a,dataPathArr:i,opts:c}=t;u(l.let("data",n._`${t.data}${(0,n.getProperty)(r)}`,!0)),e.errorPath=n.str`${a}${(0,s.getErrorPath)(r,o,c.jsPropertySyntax)}`,e.parentDataProperty=n._`${r}`,e.dataPathArr=[...i,e.parentDataProperty]}function u(r){e.data=r,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,r]}void 0!==a&&(u(a instanceof n.Name?a:l.let("data",a,!0)),void 0!==c&&(e.propertyName=c)),i&&(e.dataTypes=i)},t.extendSubschemaMode=function(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:s,allErrors:o}){void 0!==n&&(e.compositeRule=n),void 0!==s&&(e.createErrors=s),void 0!==o&&(e.allErrors=o),e.jtdDiscriminator=t,e.jtdMetadata=r}},9574:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(111),s=r(5273),o={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>n.str`must match "${e.ifClause}" schema`,params:({params:e})=>n._`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:o}=e;void 0===r.then&&void 0===r.else&&(0,s.checkStrictMode)(o,'"if" without "then" and "else" is ignored');const i=a(o,"then"),c=a(o,"else");if(!i&&!c)return;const l=t.let("valid",!0),u=t.name("_valid");if(function(){const t=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},u);e.mergeEvaluated(t)}(),e.reset(),i&&c){const r=t.let("ifClause");e.setParams({ifClause:r}),t.if(u,d("then",r),d("else",r))}else i?t.if(u,d("then")):t.if((0,n.not)(u),d("else"));function d(r,s){return()=>{const o=e.subschema({keyword:r},u);t.assign(l,u),e.mergeValidEvaluated(o,l),s?t.assign(s,n._`${r}`):e.setParams({ifClause:r})}}e.pass(l,()=>e.error(!0))}};function a(e,t){const r=e.schema[t];return void 0!==r&&!(0,s.alwaysValidSchema)(e,r)}t.default=o},9589:(e,t,r)=>{"use strict";const n=r(9718),s=r(6874),o=r(3908),a=r(1123),i=r(144),c=r(6953),l=r(7414),u=r(3007),d=r(1832),p=r(2938),h=r(6254),f=r(4493),m=r(1729),g=r(560),y=r(9970),v=r(1763),w=r(909),_=r(3927),b=r(4277),$=r(5580),E=r(7059),S=r(4641),P=r(3999),O=r(4089),C=r(5200),N=r(2111),I=r(6170),T=r(3904),A=r(8311),k=r(7638),R=r(7631),x=r(9628),D=r(270),j=r(1261),M=r(3874),U=r(7075),F=r(5571),L=r(5342),z=r(6780),V=r(2525),q=r(5032);e.exports={parse:i,valid:c,clean:l,inc:u,diff:d,major:p,minor:h,patch:f,prerelease:m,compare:g,rcompare:y,compareLoose:v,compareBuild:w,sort:_,rsort:b,gt:$,lt:E,eq:S,neq:P,gte:O,lte:C,cmp:N,coerce:I,Comparator:T,Range:A,satisfies:k,toComparators:R,maxSatisfying:x,minSatisfying:D,minVersion:j,validRange:M,outside:U,gtr:F,ltr:L,intersects:z,simplifyRange:V,subset:q,SemVer:o,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:a.compareIdentifiers,rcompareIdentifiers:a.rcompareIdentifiers}},9598:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super("validation failed"),this.errors=e,this.ajv=this.validation=!0}}t.default=r},9623:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.callRef=t.getValidate=void 0;const n=r(2877),s=r(1039),o=r(111),a=r(7193),i=r(5857),c=r(5273),l={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:s}=e,{baseId:a,schemaEnv:c,validateName:l,opts:p,self:h}=s,{root:f}=c;if(("#"===r||"#/"===r)&&a===f.baseId)return function(){if(c===f)return d(e,l,c,c.$async);const r=t.scopeValue("root",{ref:f});return d(e,o._`${r}.validate`,f,f.$async)}();const m=i.resolveRef.call(h,f,a,r);if(void 0===m)throw new n.default(s.opts.uriResolver,a,r);return m instanceof i.SchemaEnv?function(t){const r=u(e,t);d(e,r,t,t.$async)}(m):function(n){const s=t.scopeValue("schema",!0===p.code.source?{ref:n,code:(0,o.stringify)(n)}:{ref:n}),a=t.name("valid"),i=e.subschema({schema:n,dataTypes:[],schemaPath:o.nil,topSchemaRef:s,errSchemaPath:r},a);e.mergeEvaluated(i),e.ok(a)}(m)}};function u(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):o._`${r.scopeValue("wrapper",{ref:t})}.validate`}function d(e,t,r,n){const{gen:i,it:l}=e,{allErrors:u,schemaEnv:d,opts:p}=l,h=p.passContext?a.default.this:o.nil;function f(e){const t=o._`${e}.errors`;i.assign(a.default.vErrors,o._`${a.default.vErrors} === null ? ${t} : ${a.default.vErrors}.concat(${t})`),i.assign(a.default.errors,o._`${a.default.vErrors}.length`)}function m(e){var t;if(!l.opts.unevaluated)return;const n=null===(t=null==r?void 0:r.validate)||void 0===t?void 0:t.evaluated;if(!0!==l.props)if(n&&!n.dynamicProps)void 0!==n.props&&(l.props=c.mergeEvaluated.props(i,n.props,l.props));else{const t=i.var("props",o._`${e}.evaluated.props`);l.props=c.mergeEvaluated.props(i,t,l.props,o.Name)}if(!0!==l.items)if(n&&!n.dynamicItems)void 0!==n.items&&(l.items=c.mergeEvaluated.items(i,n.items,l.items));else{const t=i.var("items",o._`${e}.evaluated.items`);l.items=c.mergeEvaluated.items(i,t,l.items,o.Name)}}n?function(){if(!d.$async)throw new Error("async schema referenced by sync schema");const r=i.let("valid");i.try(()=>{i.code(o._`await ${(0,s.callValidateCode)(e,t,h)}`),m(t),u||i.assign(r,!0)},e=>{i.if(o._`!(${e} instanceof ${l.ValidationError})`,()=>i.throw(e)),f(e),u||i.assign(r,!1)}),e.ok(r)}():e.result((0,s.callValidateCode)(e,t,h),()=>m(t),()=>f(t))}t.getValidate=u,t.callRef=d,t.default=l},9625:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={isFunction:e=>"function"==typeof e,isString:e=>"string"==typeof e,isUndefined:e=>void 0===e}},9628:(e,t,r)=>{"use strict";const n=r(3908),s=r(8311);e.exports=(e,t,r)=>{let o=null,a=null,i=null;try{i=new s(t,r)}catch(e){return null}return e.forEach(e=>{i.test(e)&&(o&&-1!==a.compare(e)||(o=e,a=new n(o,r)))}),o}},9718:(e,t,r)=>{"use strict";const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:o}=r(6874),a=r(7272),i=(t=e.exports={}).re=[],c=t.safeRe=[],l=t.src=[],u=t.safeSrc=[],d=t.t={};let p=0;const h="[a-zA-Z0-9-]",f=[["\\s",1],["\\d",o],[h,s]],m=(e,t,r)=>{const n=(e=>{for(const[t,r]of f)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e})(t),s=p++;a(e,s,t),d[e]=s,l[s]=t,u[s]=n,i[s]=new RegExp(t,r?"g":void 0),c[s]=new RegExp(n,r?"g":void 0)};m("NUMERICIDENTIFIER","0|[1-9]\\d*"),m("NUMERICIDENTIFIERLOOSE","\\d+"),m("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${h}*`),m("MAINVERSION",`(${l[d.NUMERICIDENTIFIER]})\\.(${l[d.NUMERICIDENTIFIER]})\\.(${l[d.NUMERICIDENTIFIER]})`),m("MAINVERSIONLOOSE",`(${l[d.NUMERICIDENTIFIERLOOSE]})\\.(${l[d.NUMERICIDENTIFIERLOOSE]})\\.(${l[d.NUMERICIDENTIFIERLOOSE]})`),m("PRERELEASEIDENTIFIER",`(?:${l[d.NONNUMERICIDENTIFIER]}|${l[d.NUMERICIDENTIFIER]})`),m("PRERELEASEIDENTIFIERLOOSE",`(?:${l[d.NONNUMERICIDENTIFIER]}|${l[d.NUMERICIDENTIFIERLOOSE]})`),m("PRERELEASE",`(?:-(${l[d.PRERELEASEIDENTIFIER]}(?:\\.${l[d.PRERELEASEIDENTIFIER]})*))`),m("PRERELEASELOOSE",`(?:-?(${l[d.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${l[d.PRERELEASEIDENTIFIERLOOSE]})*))`),m("BUILDIDENTIFIER",`${h}+`),m("BUILD",`(?:\\+(${l[d.BUILDIDENTIFIER]}(?:\\.${l[d.BUILDIDENTIFIER]})*))`),m("FULLPLAIN",`v?${l[d.MAINVERSION]}${l[d.PRERELEASE]}?${l[d.BUILD]}?`),m("FULL",`^${l[d.FULLPLAIN]}$`),m("LOOSEPLAIN",`[v=\\s]*${l[d.MAINVERSIONLOOSE]}${l[d.PRERELEASELOOSE]}?${l[d.BUILD]}?`),m("LOOSE",`^${l[d.LOOSEPLAIN]}$`),m("GTLT","((?:<|>)?=?)"),m("XRANGEIDENTIFIERLOOSE",`${l[d.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),m("XRANGEIDENTIFIER",`${l[d.NUMERICIDENTIFIER]}|x|X|\\*`),m("XRANGEPLAIN",`[v=\\s]*(${l[d.XRANGEIDENTIFIER]})(?:\\.(${l[d.XRANGEIDENTIFIER]})(?:\\.(${l[d.XRANGEIDENTIFIER]})(?:${l[d.PRERELEASE]})?${l[d.BUILD]}?)?)?`),m("XRANGEPLAINLOOSE",`[v=\\s]*(${l[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[d.XRANGEIDENTIFIERLOOSE]})(?:${l[d.PRERELEASELOOSE]})?${l[d.BUILD]}?)?)?`),m("XRANGE",`^${l[d.GTLT]}\\s*${l[d.XRANGEPLAIN]}$`),m("XRANGELOOSE",`^${l[d.GTLT]}\\s*${l[d.XRANGEPLAINLOOSE]}$`),m("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),m("COERCE",`${l[d.COERCEPLAIN]}(?:$|[^\\d])`),m("COERCEFULL",l[d.COERCEPLAIN]+`(?:${l[d.PRERELEASE]})?`+`(?:${l[d.BUILD]})?(?:$|[^\\d])`),m("COERCERTL",l[d.COERCE],!0),m("COERCERTLFULL",l[d.COERCEFULL],!0),m("LONETILDE","(?:~>?)"),m("TILDETRIM",`(\\s*)${l[d.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",m("TILDE",`^${l[d.LONETILDE]}${l[d.XRANGEPLAIN]}$`),m("TILDELOOSE",`^${l[d.LONETILDE]}${l[d.XRANGEPLAINLOOSE]}$`),m("LONECARET","(?:\\^)"),m("CARETTRIM",`(\\s*)${l[d.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",m("CARET",`^${l[d.LONECARET]}${l[d.XRANGEPLAIN]}$`),m("CARETLOOSE",`^${l[d.LONECARET]}${l[d.XRANGEPLAINLOOSE]}$`),m("COMPARATORLOOSE",`^${l[d.GTLT]}\\s*(${l[d.LOOSEPLAIN]})$|^$`),m("COMPARATOR",`^${l[d.GTLT]}\\s*(${l[d.FULLPLAIN]})$|^$`),m("COMPARATORTRIM",`(\\s*)${l[d.GTLT]}\\s*(${l[d.LOOSEPLAIN]}|${l[d.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",m("HYPHENRANGE",`^\\s*(${l[d.XRANGEPLAIN]})\\s+-\\s+(${l[d.XRANGEPLAIN]})\\s*$`),m("HYPHENRANGELOOSE",`^\\s*(${l[d.XRANGEPLAINLOOSE]})\\s+-\\s+(${l[d.XRANGEPLAINLOOSE]})\\s*$`),m("STAR","(<|>)?=?\\s*\\*"),m("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),m("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},9741:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};t.default=r},9797:(e,t,r)=>{"use strict";const n=r(4865);e.exports=(e,t={})=>{if("function"!=typeof e)throw new TypeError(`Expected the first argument to be a function, got \`${typeof e}\``);const{wait:r=0,before:s=!1,after:o=!0}=t;if(!s&&!o)throw new Error("Both `before` and `after` are false, function wouldn't be called.");let a,i;const c=function(...t){const n=this,c=s&&!a;return clearTimeout(a),a=setTimeout(()=>{a=void 0,o&&(i=e.apply(n,t))},r),c&&(i=e.apply(n,t)),i};return n(c,e),c.cancel=()=>{a&&(clearTimeout(a),a=void 0)},c}},9810:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const n=r(5583),s=r(8840),o=r(5335),a=r(8840),i=r(5590),c=r(2657),l=r(9543),u=r(3789),d=r(6735),p=r(3043),h=r(7083),f=r(2796);function m({gen:e,validateName:t,schema:r,schemaEnv:n,opts:s},o){s.code.es5?e.func(t,u._`${d.default.data}, ${d.default.valCxt}`,n.$async,()=>{e.code(u._`"use strict"; ${g(r,s)}`),function(e,t){e.if(d.default.valCxt,()=>{e.var(d.default.instancePath,u._`${d.default.valCxt}.${d.default.instancePath}`),e.var(d.default.parentData,u._`${d.default.valCxt}.${d.default.parentData}`),e.var(d.default.parentDataProperty,u._`${d.default.valCxt}.${d.default.parentDataProperty}`),e.var(d.default.rootData,u._`${d.default.valCxt}.${d.default.rootData}`),t.dynamicRef&&e.var(d.default.dynamicAnchors,u._`${d.default.valCxt}.${d.default.dynamicAnchors}`)},()=>{e.var(d.default.instancePath,u._`""`),e.var(d.default.parentData,u._`undefined`),e.var(d.default.parentDataProperty,u._`undefined`),e.var(d.default.rootData,d.default.data),t.dynamicRef&&e.var(d.default.dynamicAnchors,u._`{}`)})}(e,s),e.code(o)}):e.func(t,u._`${d.default.data}, ${function(e){return u._`{${d.default.instancePath}="", ${d.default.parentData}, ${d.default.parentDataProperty}, ${d.default.rootData}=${d.default.data}${e.dynamicRef?u._`, ${d.default.dynamicAnchors}={}`:u.nil}}={}`}(s)}`,n.$async,()=>e.code(g(r,s)).code(o))}function g(e,t){const r="object"==typeof e&&e[t.schemaId];return r&&(t.code.source||t.code.process)?u._`/*# sourceURL=${r} */`:u.nil}function y({schema:e,self:t}){if("boolean"==typeof e)return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function v(e){return"boolean"!=typeof e.schema}function w(e){(0,h.checkUnknownRules)(e),function(e){const{schema:t,errSchemaPath:r,opts:n,self:s}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(t,s.RULES)&&s.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}(e)}function _(e,t){if(e.opts.jtd)return $(e,[],!1,t);const r=(0,s.getSchemaTypes)(e.schema);$(e,r,!(0,s.coerceAndCheckDataType)(e,r),t)}function b({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:s}){const o=r.$comment;if(!0===s.$comment)e.code(u._`${d.default.self}.logger.log(${o})`);else if("function"==typeof s.$comment){const r=u.str`${n}/$comment`,s=e.scopeValue("root",{ref:t.root});e.code(u._`${d.default.self}.opts.$comment(${o}, ${r}, ${s}.schema)`)}}function $(e,t,r,n){const{gen:s,schema:i,data:c,allErrors:l,opts:p,self:f}=e,{RULES:m}=f;function g(h){(0,o.shouldUseGroup)(i,h)&&(h.type?(s.if((0,a.checkDataType)(h.type,c,p.strictNumbers)),E(e,h),1===t.length&&t[0]===h.type&&r&&(s.else(),(0,a.reportTypeError)(e)),s.endIf()):E(e,h),l||s.if(u._`${d.default.errors} === ${n||0}`))}!i.$ref||!p.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(i,m)?(p.jtd||function(e,t){!e.schemaEnv.meta&&e.opts.strictTypes&&(function(e,t){t.length&&(e.dataTypes.length?(t.forEach(t=>{S(e.dataTypes,t)||P(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)}),function(e,t){const r=[];for(const n of e.dataTypes)S(t,n)?r.push(n):t.includes("integer")&&"number"===n&&r.push("integer");e.dataTypes=r}(e,t)):e.dataTypes=t)}(e,t),e.opts.allowUnionTypes||function(e,t){t.length>1&&(2!==t.length||!t.includes("null"))&&P(e,"use allowUnionTypes to allow union type keyword")}(e,t),function(e,t){const r=e.self.RULES.all;for(const n in r){const s=r[n];if("object"==typeof s&&(0,o.shouldUseRule)(e.schema,s)){const{type:r}=s.definition;r.length&&!r.some(e=>{return n=e,(r=t).includes(n)||"number"===n&&r.includes("integer");var r,n})&&P(e,`missing type "${r.join(",")}" for keyword "${n}"`)}}}(e,e.dataTypes))}(e,t),s.block(()=>{for(const e of m.rules)g(e);g(m.post)})):s.block(()=>C(e,"$ref",m.all.$ref.definition))}function E(e,t){const{gen:r,schema:n,opts:{useDefaults:s}}=e;s&&(0,i.assignDefaults)(e,t.type),r.block(()=>{for(const r of t.rules)(0,o.shouldUseRule)(n,r)&&C(e,r.keyword,r.definition,t.type)})}function S(e,t){return e.includes(t)||"integer"===t&&e.includes("number")}function P(e,t){t+=` at "${e.schemaEnv.baseId+e.errSchemaPath}" (strictTypes)`,(0,h.checkStrictMode)(e,t,e.opts.strictTypes)}t.validateFunctionCode=function(e){v(e)&&(w(e),y(e))?function(e){const{schema:t,opts:r,gen:n}=e;m(e,()=>{r.$comment&&t.$comment&&b(e),function(e){const{schema:t,opts:r}=e;void 0!==t.default&&r.useDefaults&&r.strictSchema&&(0,h.checkStrictMode)(e,"default is ignored in the schema root")}(e),n.let(d.default.vErrors,null),n.let(d.default.errors,0),r.unevaluated&&function(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",u._`${r}.evaluated`),t.if(u._`${e.evaluated}.dynamicProps`,()=>t.assign(u._`${e.evaluated}.props`,u._`undefined`)),t.if(u._`${e.evaluated}.dynamicItems`,()=>t.assign(u._`${e.evaluated}.items`,u._`undefined`))}(e),_(e),function(e){const{gen:t,schemaEnv:r,validateName:n,ValidationError:s,opts:o}=e;r.$async?t.if(u._`${d.default.errors} === 0`,()=>t.return(d.default.data),()=>t.throw(u._`new ${s}(${d.default.vErrors})`)):(t.assign(u._`${n}.errors`,d.default.vErrors),o.unevaluated&&function({gen:e,evaluated:t,props:r,items:n}){r instanceof u.Name&&e.assign(u._`${t}.props`,r),n instanceof u.Name&&e.assign(u._`${t}.items`,n)}(e),t.return(u._`${d.default.errors} === 0`))}(e)})}(e):m(e,()=>(0,n.topBoolOrEmptySchema)(e))};class O{constructor(e,t,r){if((0,c.validateKeywordUsage)(e,t,r),this.gen=e.gen,this.allErrors=e.allErrors,this.keyword=r,this.data=e.data,this.schema=e.schema[r],this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,h.schemaRefOrVal)(e,this.schema,r,this.$data),this.schemaType=t.schemaType,this.parentSchema=e.schema,this.params={},this.it=e,this.def=t,this.$data)this.schemaCode=e.gen.const("vSchema",T(this.$data,e));else if(this.schemaCode=this.schemaValue,!(0,c.validSchemaType)(this.schema,t.schemaType,t.allowUndefined))throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`);("code"in t?t.trackErrors:!1!==t.errors)&&(this.errsCount=e.gen.const("_errs",d.default.errors))}result(e,t,r){this.failResult((0,u.not)(e),t,r)}failResult(e,t,r){this.gen.if(e),r?r():this.error(),t?(this.gen.else(),t(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(e,t){this.failResult((0,u.not)(e),void 0,t)}fail(e){if(void 0===e)return this.error(),void(this.allErrors||this.gen.if(!1));this.gen.if(e),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail(u._`${t} !== undefined && (${(0,u.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t)return this.setParams(t),this._error(e,r),void this.setParams({});this._error(e,r)}_error(e,t){(e?f.reportExtraError:f.reportError)(this,this.def.error,t)}$dataError(){(0,f.reportError)(this,this.def.$dataError||f.keyword$DataError)}reset(){if(void 0===this.errsCount)throw new Error('add "trackErrors" to keyword definition');(0,f.resetErrorsCount)(this.gen,this.errsCount)}ok(e){this.allErrors||this.gen.if(e)}setParams(e,t){t?Object.assign(this.params,e):this.params=e}block$data(e,t,r=u.nil){this.gen.block(()=>{this.check$data(e,r),t()})}check$data(e=u.nil,t=u.nil){if(!this.$data)return;const{gen:r,schemaCode:n,schemaType:s,def:o}=this;r.if((0,u.or)(u._`${n} === undefined`,t)),e!==u.nil&&r.assign(e,!0),(s.length||o.validateSchema)&&(r.elseIf(this.invalid$data()),this.$dataError(),e!==u.nil&&r.assign(e,!1)),r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:r,def:n,it:s}=this;return(0,u.or)(function(){if(r.length){if(!(t instanceof u.Name))throw new Error("ajv implementation error");const e=Array.isArray(r)?r:[r];return u._`${(0,a.checkDataTypes)(e,t,s.opts.strictNumbers,a.DataType.Wrong)}`}return u.nil}(),function(){if(n.validateSchema){const r=e.scopeValue("validate$data",{ref:n.validateSchema});return u._`!${r}(${t})`}return u.nil}())}subschema(e,t){const r=(0,l.getSubschema)(this.it,e);(0,l.extendSubschemaData)(r,this.it,e),(0,l.extendSubschemaMode)(r,e);const s={...this.it,...r,items:void 0,props:void 0};return function(e,t){v(e)&&(w(e),y(e))?function(e,t){const{schema:r,gen:n,opts:s}=e;s.$comment&&r.$comment&&b(e),function(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,p.resolveUrl)(e.opts.uriResolver,e.baseId,t))}(e),function(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}(e);const o=n.const("_errs",d.default.errors);_(e,o),n.var(t,u._`${o} === ${d.default.errors}`)}(e,t):(0,n.boolOrEmptySchema)(e,t)}(s,t),s}mergeEvaluated(e,t){const{it:r,gen:n}=this;r.opts.unevaluated&&(!0!==r.props&&void 0!==e.props&&(r.props=h.mergeEvaluated.props(n,e.props,r.props,t)),!0!==r.items&&void 0!==e.items&&(r.items=h.mergeEvaluated.items(n,e.items,r.items,t)))}mergeValidEvaluated(e,t){const{it:r,gen:n}=this;if(r.opts.unevaluated&&(!0!==r.props||!0!==r.items))return n.if(t,()=>this.mergeEvaluated(e,u.Name)),!0}}function C(e,t,r,n){const s=new O(e,r,t);"code"in r?r.code(s,n):s.$data&&r.validate?(0,c.funcKeywordCode)(s,r):"macro"in r?(0,c.macroKeywordCode)(s,r):(r.compile||r.validate)&&(0,c.funcKeywordCode)(s,r)}t.KeywordCxt=O;const N=/^\/(?:[^~]|~0|~1)*$/,I=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function T(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let s,o;if(""===e)return d.default.rootData;if("/"===e[0]){if(!N.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);s=e,o=d.default.rootData}else{const a=I.exec(e);if(!a)throw new Error(`Invalid JSON-pointer: ${e}`);const i=+a[1];if(s=a[2],"#"===s){if(i>=t)throw new Error(c("property/index",i));return n[t-i]}if(i>t)throw new Error(c("data",i));if(o=r[t-i],!s)return o}let a=o;const i=s.split("/");for(const e of i)e&&(o=u._`${o}${(0,u.getProperty)((0,h.unescapeJsonPointer)(e))}`,a=u._`${a} && ${o}`);return a;function c(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=T},9896:e=>{"use strict";e.exports=require("fs")},9970:(e,t,r)=>{"use strict";const n=r(560);e.exports=(e,t,r)=>n(t,e,r)},9999:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.writeFileSync=t.writeFile=t.readFileSync=t.readFile=void 0;const n=r(6928),s=r(4763),o=r(2750),a=r(9625),i=r(2916),c=r(4727);t.readFile=function e(t,r=s.DEFAULT_READ_OPTIONS){var n;if(a.default.isString(r))return e(t,{encoding:r});const i=Date.now()+(null!==(n=r.timeout)&&void 0!==n?n:s.DEFAULT_TIMEOUT_ASYNC);return o.default.readFileRetry(i)(t,r)},t.readFileSync=function e(t,r=s.DEFAULT_READ_OPTIONS){var n;if(a.default.isString(r))return e(t,{encoding:r});const i=Date.now()+(null!==(n=r.timeout)&&void 0!==n?n:s.DEFAULT_TIMEOUT_SYNC);return o.default.readFileSyncRetry(i)(t,r)};const l=(e,t,r,n)=>{if(a.default.isFunction(r))return l(e,t,s.DEFAULT_WRITE_OPTIONS,r);const o=u(e,t,r);return n&&o.then(n,n),o};t.writeFile=l;const u=async(e,t,r=s.DEFAULT_WRITE_OPTIONS)=>{var l;if(a.default.isString(r))return u(e,t,{encoding:r});const d=Date.now()+(null!==(l=r.timeout)&&void 0!==l?l:s.DEFAULT_TIMEOUT_ASYNC);let p=null,h=null,f=null,m=null,g=null;try{r.schedule&&(p=await r.schedule(e)),h=await i.default.schedule(e),e=await o.default.realpathAttempt(e)||e,[m,f]=c.default.get(e,r.tmpCreate||c.default.create,!(!1===r.tmpPurge));const l=s.IS_POSIX&&a.default.isUndefined(r.chown),u=a.default.isUndefined(r.mode);if(l||u){const t=await o.default.statAttempt(e);t&&(r={...r},l&&(r.chown={uid:t.uid,gid:t.gid}),u&&(r.mode=t.mode))}const y=n.dirname(e);await o.default.mkdirAttempt(y,{mode:s.DEFAULT_FOLDER_MODE,recursive:!0}),g=await o.default.openRetry(d)(m,"w",r.mode||s.DEFAULT_FILE_MODE),r.tmpCreated&&r.tmpCreated(m),a.default.isString(t)?await o.default.writeRetry(d)(g,t,0,r.encoding||s.DEFAULT_ENCODING):a.default.isUndefined(t)||await o.default.writeRetry(d)(g,t,0,t.length,0),!1!==r.fsync&&(!1!==r.fsyncWait?await o.default.fsyncRetry(d)(g):o.default.fsyncAttempt(g)),await o.default.closeRetry(d)(g),g=null,r.chown&&await o.default.chownAttempt(m,r.chown.uid,r.chown.gid),r.mode&&await o.default.chmodAttempt(m,r.mode);try{await o.default.renameRetry(d)(m,e)}catch(t){if("ENAMETOOLONG"!==t.code)throw t;await o.default.renameRetry(d)(m,c.default.truncate(e))}f(),m=null}finally{g&&await o.default.closeAttempt(g),m&&c.default.purge(m),p&&p(),h&&h()}},d=(e,t,r=s.DEFAULT_WRITE_OPTIONS)=>{var i;if(a.default.isString(r))return d(e,t,{encoding:r});const l=Date.now()+(null!==(i=r.timeout)&&void 0!==i?i:s.DEFAULT_TIMEOUT_SYNC);let u=null,p=null,h=null;try{e=o.default.realpathSyncAttempt(e)||e,[p,u]=c.default.get(e,r.tmpCreate||c.default.create,!(!1===r.tmpPurge));const i=s.IS_POSIX&&a.default.isUndefined(r.chown),d=a.default.isUndefined(r.mode);if(i||d){const t=o.default.statSyncAttempt(e);t&&(r={...r},i&&(r.chown={uid:t.uid,gid:t.gid}),d&&(r.mode=t.mode))}const f=n.dirname(e);o.default.mkdirSyncAttempt(f,{mode:s.DEFAULT_FOLDER_MODE,recursive:!0}),h=o.default.openSyncRetry(l)(p,"w",r.mode||s.DEFAULT_FILE_MODE),r.tmpCreated&&r.tmpCreated(p),a.default.isString(t)?o.default.writeSyncRetry(l)(h,t,0,r.encoding||s.DEFAULT_ENCODING):a.default.isUndefined(t)||o.default.writeSyncRetry(l)(h,t,0,t.length,0),!1!==r.fsync&&(!1!==r.fsyncWait?o.default.fsyncSyncRetry(l)(h):o.default.fsyncAttempt(h)),o.default.closeSyncRetry(l)(h),h=null,r.chown&&o.default.chownSyncAttempt(p,r.chown.uid,r.chown.gid),r.mode&&o.default.chmodSyncAttempt(p,r.mode);try{o.default.renameSyncRetry(l)(p,e)}catch(t){if("ENAMETOOLONG"!==t.code)throw t;o.default.renameSyncRetry(l)(p,c.default.truncate(e))}u(),p=null}finally{h&&o.default.closeSyncAttempt(h),p&&c.default.purge(p)}};t.writeFileSync=d}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var o=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.c=t,r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r(3281)})();
//# sourceMappingURL=main.js.map