/**
 * Component exports - Simple placeholder components
 */

import React from 'react';

// Simple placeholder components
export const Sidebar: React.FC = () => (
  <div style={{ 
    width: '300px', 
    height: '100%', 
    background: '#2d2d2d', 
    padding: '16px', 
    color: '#ccc',
    borderRight: '1px solid #404040'
  }}>
    <h3>Project Explorer</h3>
    <p>Sidebar content will go here</p>
  </div>
);

export const MainContent: React.FC<{ activeTab?: string }> = ({ activeTab = 'items' }) => (
  <div style={{ 
    flex: 1, 
    height: '100%', 
    background: '#1a1a1a', 
    padding: '16px', 
    color: '#ccc',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column'
  }}>
    <h2>Main Content Area</h2>
    <p>Active tab: {activeTab}</p>
    <p>Main workspace content will be implemented here</p>
  </div>
);

export const StatusBar: React.FC = () => (
  <div style={{ 
    height: '24px', 
    background: '#2a2a2a', 
    padding: '4px 16px', 
    color: '#ccc', 
    fontSize: '12px',
    borderTop: '1px solid #404040',
    display: 'flex',
    alignItems: 'center'
  }}>
    Ready - DripForge Pro v1.0.0
  </div>
);

export const ConsolePanel: React.FC = () => (
  <div style={{ 
    height: '200px', 
    background: '#1e1e1e', 
    padding: '16px', 
    color: '#ccc',
    borderTop: '1px solid #404040',
    fontFamily: 'monospace',
    fontSize: '12px'
  }}>
    <h4>Console</h4>
    <div>Console output will appear here...</div>
  </div>
);

export const MenuHandler: React.FC = () => {
  React.useEffect(() => {
    console.log('MenuHandler mounted - menu events will be handled here');
  }, []);

  return null;
};

export const PreferencesModal: React.FC<{ isOpen: boolean; onClose: () => void }> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;
  
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{ 
        background: '#2d2d2d', 
        padding: '32px', 
        borderRadius: '8px', 
        color: '#fff',
        minWidth: '400px'
      }}>
        <h2>Preferences</h2>
        <p>Settings and configuration options will go here</p>
        <button 
          onClick={onClose} 
          style={{ 
            marginTop: '16px',
            padding: '8px 16px',
            background: '#007acc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export const AboutModal: React.FC<{ isOpen: boolean; onClose: () => void }> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;
  
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{ 
        background: '#2d2d2d', 
        padding: '32px', 
        borderRadius: '8px', 
        color: '#fff',
        textAlign: 'center'
      }}>
        <h2>About DripForge Pro</h2>
        <p>FiveM Clothing Development Studio</p>
        <p>Version 1.0.0</p>
        <button 
          onClick={onClose} 
          style={{ 
            marginTop: '16px',
            padding: '8px 16px',
            background: '#007acc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export const ProgressModal: React.FC = () => (
  <div style={{
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000
  }}>
    <div style={{ 
      background: '#2d2d2d', 
      padding: '32px', 
      borderRadius: '8px', 
      color: '#fff',
      textAlign: 'center'
    }}>
      <h3>Processing...</h3>
      <div style={{
        width: '200px',
        height: '4px',
        background: '#404040',
        borderRadius: '2px',
        margin: '16px 0',
        overflow: 'hidden'
      }}>
        <div style={{
          width: '50%',
          height: '100%',
          background: '#007acc',
          animation: 'progress 2s infinite'
        }} />
      </div>
      <p>Operation in progress...</p>
    </div>
  </div>
);
