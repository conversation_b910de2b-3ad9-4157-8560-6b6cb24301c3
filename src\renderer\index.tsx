/**
 * Renderer process entry point
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import { AssetImport } from './components/AssetImport';
import { ItemManager } from './components/ItemManager';
import { ExportSystem } from './components/ExportSystem';

// Complete DripForge Pro Application
const DripForgeApp: React.FC = () => {
  const [currentView, setCurrentView] = React.useState<'welcome' | 'project'>('welcome');
  const [activeTab, setActiveTab] = React.useState<'import' | 'items' | 'export'>('import');
  const [projectName, setProjectName] = React.useState<string>('');
  const [statusMessage, setStatusMessage] = React.useState<string>('Ready - DripForge Pro v1.0.0');
  const [items, setItems] = React.useState<any[]>([]);

  const handleNewProject = async () => {
    try {
      setStatusMessage('Creating new project...');

      // Check if electronAPI is available
      if (window.electronAPI) {
        const project = await window.electronAPI.project.create({
          name: 'New Project',
          description: 'A new DripForge Pro project'
        });

        setProjectName(project.name);
        setCurrentView('project');
        setStatusMessage(`Project "${project.name}" created successfully`);
      } else {
        // Fallback for development
        setProjectName('New Project (Demo Mode)');
        setCurrentView('project');
        setStatusMessage('Demo project created - Electron API not available');
      }
    } catch (error) {
      console.error('Failed to create project:', error);
      setStatusMessage('Failed to create project');
    }
  };

  const handleOpenProject = async () => {
    try {
      setStatusMessage('Opening project...');

      if (window.electronAPI) {
        const result = await window.electronAPI.project.load();

        if (result) {
          setProjectName(result.project.name);
          setCurrentView('project');
          setStatusMessage(`Project "${result.project.name}" loaded successfully`);
        } else {
          setStatusMessage('No project selected');
        }
      } else {
        // Fallback for development
        setProjectName('Demo Project (File Dialog Not Available)');
        setCurrentView('project');
        setStatusMessage('Demo project loaded - Electron API not available');
      }
    } catch (error) {
      console.error('Failed to open project:', error);
      setStatusMessage('Failed to open project');
    }
  };

  const handleBackToWelcome = () => {
    setCurrentView('welcome');
    setProjectName('');
    setStatusMessage('Ready - DripForge Pro v1.0.0');
  };

  const handleImportAssets = () => {
    setActiveTab('import');
    setStatusMessage('Ready to import assets');
  };

  const handleImportComplete = (importedItems: any[]) => {
    const newItems = importedItems.map((item, index) => ({
      id: `item_${Date.now()}_${index}`,
      name: item.name,
      type: item.name.includes('_m_') ? 'male' : item.name.includes('_f_') ? 'female' : 'prop',
      slot: 'torso', // Would be detected from filename
      drawable: Math.floor(Math.random() * 100),
      texture: 0,
      size: Math.floor(Math.random() * 10000000),
      triangles: Math.floor(Math.random() * 50000),
      quality: Math.random() > 0.7 ? 'good' : Math.random() > 0.4 ? 'warning' : 'error',
      hasConflicts: Math.random() > 0.8,
      imported: new Date()
    }));

    setItems(prev => [...prev, ...newItems]);
    setActiveTab('items');
    setStatusMessage(`Imported ${newItems.length} items successfully`);
  };

  const handleItemDelete = (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
    setStatusMessage('Item deleted');
  };

  const handleItemEdit = (item: any) => {
    setStatusMessage(`Editing ${item.name}`);
  };

  const handleExport = (config: any) => {
    setStatusMessage(`Exported ${config.items.length} items in ${config.formats.length} formats`);
  };

  return (
    <div style={{
      height: '100vh',
      background: '#1a1a1a',
      color: '#ffffff',
      display: 'flex',
      flexDirection: 'column',
      fontFamily: 'system-ui, sans-serif'
    }}>
      {/* Title Bar */}
      <div style={{
        height: '32px',
        background: '#2a2a2a',
        borderBottom: '1px solid #404040',
        display: 'flex',
        alignItems: 'center',
        padding: '0 16px'
      }}>
        🎨 DripForge Pro {projectName && `- ${projectName}`}
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex' }}>
        {/* Sidebar */}
        <div style={{
          width: '300px',
          background: '#2d2d2d',
          borderRight: '1px solid #404040',
          padding: '16px'
        }}>
          <h3>Project Explorer</h3>
          {currentView === 'welcome' ? (
            <p>No project loaded</p>
          ) : (
            <div>
              <p>📁 {projectName}</p>
              <div style={{ marginTop: '16px' }}>
                <button
                  onClick={handleBackToWelcome}
                  style={{
                    padding: '8px 16px',
                    background: '#404040',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px',
                    marginBottom: '16px'
                  }}
                >
                  ← Back to Welcome
                </button>
              </div>

              {/* Navigation Tabs */}
              <div style={{ marginBottom: '16px' }}>
                {['import', 'items', 'export'].map(tab => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab as any)}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '8px 12px',
                      background: activeTab === tab ? '#007acc' : '#404040',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      marginBottom: '4px',
                      textAlign: 'left'
                    }}
                  >
                    {tab === 'import' && '📥'}
                    {tab === 'items' && '📦'}
                    {tab === 'export' && '📤'}
                    {' '}
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </button>
                ))}
              </div>

              <div style={{ marginTop: '16px' }}>
                <h4>Items ({items.length})</h4>
                <p style={{ fontSize: '12px', color: '#888' }}>
                  {items.length === 0 ? 'No items imported yet' : `${items.length} items ready`}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Content Area */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {currentView === 'welcome' ? (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column'
            }}>
              <h1>🎨 Welcome to DripForge Pro</h1>
              <p>FiveM Clothing Development Studio</p>
              <p style={{ marginTop: '32px', color: '#888' }}>
                Import. Fix. Preview. Ship.
              </p>
              <div style={{ marginTop: '32px' }}>
                <button
                  onClick={handleNewProject}
                  style={{
                    padding: '12px 24px',
                    background: '#007acc',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginRight: '16px',
                    fontSize: '14px'
                  }}
                >
                  📁 New Project
                </button>
                <button
                  onClick={handleOpenProject}
                  style={{
                    padding: '12px 24px',
                    background: '#404040',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  📂 Open Project
                </button>
              </div>
            </div>
          ) : (
            <>
              {activeTab === 'import' && (
                <AssetImport onImportComplete={handleImportComplete} />
              )}
              {activeTab === 'items' && (
                <ItemManager
                  items={items}
                  onItemSelect={(item) => setStatusMessage(`Selected ${item.name}`)}
                  onItemDelete={handleItemDelete}
                  onItemEdit={handleItemEdit}
                />
              )}
              {activeTab === 'export' && (
                <ExportSystem
                  items={items}
                  onExport={handleExport}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* Status Bar */}
      <div style={{
        height: '24px',
        background: '#2a2a2a',
        borderTop: '1px solid #404040',
        display: 'flex',
        alignItems: 'center',
        padding: '0 16px',
        fontSize: '12px'
      }}>
        {statusMessage}
      </div>
    </div>
  );
};

// Initialize the React application
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root container not found');
}

const root = createRoot(container);

root.render(
  <React.StrictMode>
    <DripForgeApp />
  </React.StrictMode>
);
