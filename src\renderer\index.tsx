/**
 * <PERSON><PERSON>er process entry point
 */

import React from 'react';
import { createRoot } from 'react-dom/client';

// Simple App component with working buttons
const SimpleApp: React.FC = () => {
  const [currentView, setCurrentView] = React.useState<'welcome' | 'project'>('welcome');
  const [projectName, setProjectName] = React.useState<string>('');
  const [statusMessage, setStatusMessage] = React.useState<string>('Ready - DripForge Pro v1.0.0');

  const handleNewProject = async () => {
    try {
      setStatusMessage('Creating new project...');

      // Check if electronAPI is available
      if (window.electronAPI) {
        const project = await window.electronAPI.project.create({
          name: 'New Project',
          description: 'A new DripForge Pro project'
        });

        setProjectName(project.name);
        setCurrentView('project');
        setStatusMessage(`Project "${project.name}" created successfully`);
      } else {
        // Fallback for development
        setProjectName('New Project (Demo Mode)');
        setCurrentView('project');
        setStatusMessage('Demo project created - Electron API not available');
      }
    } catch (error) {
      console.error('Failed to create project:', error);
      setStatusMessage('Failed to create project');
    }
  };

  const handleOpenProject = async () => {
    try {
      setStatusMessage('Opening project...');

      if (window.electronAPI) {
        const result = await window.electronAPI.project.load();

        if (result) {
          setProjectName(result.project.name);
          setCurrentView('project');
          setStatusMessage(`Project "${result.project.name}" loaded successfully`);
        } else {
          setStatusMessage('No project selected');
        }
      } else {
        // Fallback for development
        setProjectName('Demo Project (File Dialog Not Available)');
        setCurrentView('project');
        setStatusMessage('Demo project loaded - Electron API not available');
      }
    } catch (error) {
      console.error('Failed to open project:', error);
      setStatusMessage('Failed to open project');
    }
  };

  const handleBackToWelcome = () => {
    setCurrentView('welcome');
    setProjectName('');
    setStatusMessage('Ready - DripForge Pro v1.0.0');
  };

  const handleImportAssets = () => {
    setStatusMessage('Import assets feature coming soon...');
  };

  return (
    <div style={{
      height: '100vh',
      background: '#1a1a1a',
      color: '#ffffff',
      display: 'flex',
      flexDirection: 'column',
      fontFamily: 'system-ui, sans-serif'
    }}>
      {/* Title Bar */}
      <div style={{
        height: '32px',
        background: '#2a2a2a',
        borderBottom: '1px solid #404040',
        display: 'flex',
        alignItems: 'center',
        padding: '0 16px'
      }}>
        🎨 DripForge Pro {projectName && `- ${projectName}`}
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex' }}>
        {/* Sidebar */}
        <div style={{
          width: '300px',
          background: '#2d2d2d',
          borderRight: '1px solid #404040',
          padding: '16px'
        }}>
          <h3>Project Explorer</h3>
          {currentView === 'welcome' ? (
            <p>No project loaded</p>
          ) : (
            <div>
              <p>📁 {projectName}</p>
              <div style={{ marginTop: '16px' }}>
                <button
                  onClick={handleBackToWelcome}
                  style={{
                    padding: '8px 16px',
                    background: '#404040',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  ← Back to Welcome
                </button>
              </div>
              <div style={{ marginTop: '16px' }}>
                <h4>Items (0)</h4>
                <p style={{ fontSize: '12px', color: '#888' }}>
                  No items imported yet
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Content Area */}
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column'
        }}>
          {currentView === 'welcome' ? (
            <>
              <h1>🎨 Welcome to DripForge Pro</h1>
              <p>FiveM Clothing Development Studio</p>
              <p style={{ marginTop: '32px', color: '#888' }}>
                Import. Fix. Preview. Ship.
              </p>
              <div style={{ marginTop: '32px' }}>
                <button
                  onClick={handleNewProject}
                  style={{
                    padding: '12px 24px',
                    background: '#007acc',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginRight: '16px',
                    fontSize: '14px'
                  }}
                >
                  📁 New Project
                </button>
                <button
                  onClick={handleOpenProject}
                  style={{
                    padding: '12px 24px',
                    background: '#404040',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  📂 Open Project
                </button>
              </div>
            </>
          ) : (
            <>
              <h1>📁 {projectName}</h1>
              <p>Project workspace is ready</p>
              <div style={{ marginTop: '32px' }}>
                <button
                  onClick={handleImportAssets}
                  style={{
                    padding: '12px 24px',
                    background: '#28a745',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginRight: '16px',
                    fontSize: '14px'
                  }}
                >
                  📥 Import Assets
                </button>
                <button
                  style={{
                    padding: '12px 24px',
                    background: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'not-allowed',
                    fontSize: '14px'
                  }}
                  disabled
                >
                  🎨 3D Preview (Coming Soon)
                </button>
              </div>
              <p style={{ marginTop: '24px', color: '#888', fontSize: '14px' }}>
                Start by importing your .ydd and .ytd clothing files
              </p>
            </>
          )}
        </div>
      </div>

      {/* Status Bar */}
      <div style={{
        height: '24px',
        background: '#2a2a2a',
        borderTop: '1px solid #404040',
        display: 'flex',
        alignItems: 'center',
        padding: '0 16px',
        fontSize: '12px'
      }}>
        {statusMessage}
      </div>
    </div>
  );
};

// Initialize the React application
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root container not found');
}

const root = createRoot(container);

root.render(
  <React.StrictMode>
    <SimpleApp />
  </React.StrictMode>
);
