/**
 * Renderer process entry point
 */

import React from 'react';
import { createRoot } from 'react-dom/client';

// Simple App component for initial testing
const SimpleApp: React.FC = () => {
  return (
    <div style={{
      height: '100vh',
      background: '#1a1a1a',
      color: '#ffffff',
      display: 'flex',
      flexDirection: 'column',
      fontFamily: 'system-ui, sans-serif'
    }}>
      {/* Title Bar */}
      <div style={{
        height: '32px',
        background: '#2a2a2a',
        borderBottom: '1px solid #404040',
        display: 'flex',
        alignItems: 'center',
        padding: '0 16px'
      }}>
        🎨 DripForge Pro
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex' }}>
        {/* Sidebar */}
        <div style={{
          width: '300px',
          background: '#2d2d2d',
          borderRight: '1px solid #404040',
          padding: '16px'
        }}>
          <h3>Project Explorer</h3>
          <p>No project loaded</p>
        </div>

        {/* Content Area */}
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column'
        }}>
          <h1>🎨 Welcome to DripForge Pro</h1>
          <p>FiveM Clothing Development Studio</p>
          <p style={{ marginTop: '32px', color: '#888' }}>
            Application is running successfully!
          </p>
          <div style={{ marginTop: '32px' }}>
            <button style={{
              padding: '12px 24px',
              background: '#007acc',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '16px'
            }}>
              New Project
            </button>
            <button style={{
              padding: '12px 24px',
              background: '#404040',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}>
              Open Project
            </button>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div style={{
        height: '24px',
        background: '#2a2a2a',
        borderTop: '1px solid #404040',
        display: 'flex',
        alignItems: 'center',
        padding: '0 16px',
        fontSize: '12px'
      }}>
        Ready - DripForge Pro v1.0.0
      </div>
    </div>
  );
};

// Initialize the React application
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root container not found');
}

const root = createRoot(container);

root.render(
  <React.StrictMode>
    <SimpleApp />
  </React.StrictMode>
);
