/**
 * Asset Import Component - Drag & Drop Asset Import System
 */

import React, { useState, useCallback, useRef } from 'react';
import styled from 'styled-components';

const ImportContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px;
`;

const DropZone = styled.div<{ isDragOver: boolean; hasFiles: boolean }>`
  border: 2px dashed ${props => props.isDragOver ? '#007acc' : '#404040'};
  border-radius: 8px;
  padding: 48px 24px;
  text-align: center;
  background: ${props => props.isDragOver ? 'rgba(0, 122, 204, 0.1)' : 'rgba(45, 45, 45, 0.5)'};
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 24px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #007acc;
    background: rgba(0, 122, 204, 0.05);
  }
`;

const DropZoneIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
`;

const DropZoneText = styled.div`
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #ffffff;
`;

const DropZoneSubtext = styled.div`
  font-size: 14px;
  color: #888888;
  margin-bottom: 16px;
`;

const BrowseButton = styled.button`
  padding: 12px 24px;
  background: #007acc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;

  &:hover {
    background: #1a8cdd;
  }

  &:active {
    background: #0066aa;
  }
`;

const FileList = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const FileItem = styled.div<{ status: 'pending' | 'processing' | 'success' | 'error' }>`
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #2d2d2d;
  border-radius: 6px;
  border-left: 4px solid ${props => {
    switch (props.status) {
      case 'pending': return '#6c757d';
      case 'processing': return '#ffc107';
      case 'success': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#6c757d';
    }
  }};
`;

const FileIcon = styled.div`
  font-size: 24px;
  margin-right: 12px;
  width: 32px;
  text-align: center;
`;

const FileInfo = styled.div`
  flex: 1;
`;

const FileName = styled.div`
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 4px;
`;

const FileDetails = styled.div`
  font-size: 12px;
  color: #888888;
`;

const FileStatus = styled.div<{ status: string }>`
  font-size: 12px;
  font-weight: 500;
  color: ${props => {
    switch (props.status) {
      case 'pending': return '#6c757d';
      case 'processing': return '#ffc107';
      case 'success': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#6c757d';
    }
  }};
`;

const ProgressBar = styled.div<{ progress: number }>`
  width: 100%;
  height: 4px;
  background: #404040;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 8px;

  &::after {
    content: '';
    display: block;
    width: ${props => props.progress}%;
    height: 100%;
    background: #007acc;
    transition: width 0.3s ease;
  }
`;

const ImportControls = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #404040;
`;

const ControlButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;

  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background: #007acc;
          color: white;
          &:hover { background: #1a8cdd; }
          &:disabled { background: #404040; cursor: not-allowed; }
        `;
      case 'danger':
        return `
          background: #dc3545;
          color: white;
          &:hover { background: #c82333; }
        `;
      default:
        return `
          background: #404040;
          color: white;
          &:hover { background: #505050; }
        `;
    }
  }}
`;

interface FileImportItem {
  id: string;
  file: File;
  status: 'pending' | 'processing' | 'success' | 'error';
  progress: number;
  detectedType?: string;
  error?: string;
}

interface AssetImportProps {
  onImportComplete?: (items: any[]) => void;
}

export const AssetImport: React.FC<AssetImportProps> = ({ onImportComplete }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [files, setFiles] = useState<FileImportItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    addFiles(droppedFiles);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      addFiles(selectedFiles);
    }
  }, []);

  const addFiles = (newFiles: File[]) => {
    const validFiles = newFiles.filter(file => {
      const ext = file.name.toLowerCase();
      return ext.endsWith('.ydd') || ext.endsWith('.ytd') || ext.endsWith('.zip') || ext.endsWith('.rar');
    });

    const fileItems: FileImportItem[] = validFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'pending',
      progress: 0,
      detectedType: detectFileType(file.name)
    }));

    setFiles(prev => [...prev, ...fileItems]);
  };

  const detectFileType = (filename: string): string => {
    const name = filename.toLowerCase();
    
    if (name.includes('_m_') || name.includes('male')) return 'Male Clothing';
    if (name.includes('_f_') || name.includes('female')) return 'Female Clothing';
    if (name.includes('_p_')) return 'Prop';
    if (name.endsWith('.ydd')) return 'Mesh File';
    if (name.endsWith('.ytd')) return 'Texture File';
    if (name.endsWith('.zip') || name.endsWith('.rar')) return 'Archive';
    
    return 'Unknown';
  };

  const processFiles = async () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    
    try {
      for (let i = 0; i < files.length; i++) {
        const fileItem = files[i];
        
        // Update status to processing
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'processing', progress: 0 } : f
        ));

        // Simulate processing with progress
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { ...f, progress } : f
          ));
        }

        // Mark as complete
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'success', progress: 100 } : f
        ));
      }

      // Call completion callback
      if (onImportComplete) {
        onImportComplete(files.map(f => ({ name: f.file.name, type: f.detectedType })));
      }
      
    } catch (error) {
      console.error('Import failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const clearFiles = () => {
    setFiles([]);
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  return (
    <ImportContainer>
      <DropZone
        isDragOver={isDragOver}
        hasFiles={files.length > 0}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <DropZoneIcon>📁</DropZoneIcon>
        <DropZoneText>
          {files.length === 0 ? 'Drop your clothing files here' : `${files.length} files ready to import`}
        </DropZoneText>
        <DropZoneSubtext>
          Supports .ydd, .ytd, .zip, and .rar files
        </DropZoneSubtext>
        <BrowseButton onClick={(e) => { e.stopPropagation(); fileInputRef.current?.click(); }}>
          Browse Files
        </BrowseButton>
      </DropZone>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".ydd,.ytd,.zip,.rar"
        style={{ display: 'none' }}
        onChange={handleFileSelect}
      />

      {files.length > 0 && (
        <>
          <FileList>
            {files.map(fileItem => (
              <FileItem key={fileItem.id} status={fileItem.status}>
                <FileIcon>
                  {fileItem.status === 'success' ? '✅' : 
                   fileItem.status === 'error' ? '❌' : 
                   fileItem.status === 'processing' ? '⏳' : '📄'}
                </FileIcon>
                <FileInfo>
                  <FileName>{fileItem.file.name}</FileName>
                  <FileDetails>
                    {(fileItem.file.size / 1024 / 1024).toFixed(2)} MB • {fileItem.detectedType}
                  </FileDetails>
                  {fileItem.status === 'processing' && (
                    <ProgressBar progress={fileItem.progress} />
                  )}
                </FileInfo>
                <FileStatus status={fileItem.status}>
                  {fileItem.status === 'pending' && 'Ready'}
                  {fileItem.status === 'processing' && `${fileItem.progress}%`}
                  {fileItem.status === 'success' && 'Imported'}
                  {fileItem.status === 'error' && 'Failed'}
                </FileStatus>
              </FileItem>
            ))}
          </FileList>

          <ImportControls>
            <ControlButton 
              variant="primary" 
              onClick={processFiles}
              disabled={isProcessing || files.every(f => f.status === 'success')}
            >
              {isProcessing ? 'Processing...' : 'Import All'}
            </ControlButton>
            <ControlButton onClick={clearFiles} disabled={isProcessing}>
              Clear All
            </ControlButton>
          </ImportControls>
        </>
      )}
    </ImportContainer>
  );
};
