/**
 * Welcome Screen Component - Shown when no project is loaded
 */

import React from 'react';
import styled from 'styled-components';
import { useProjectStore } from '@/stores/projectStore';

const WelcomeContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: ${props => props.theme.spacing.xl};
  background: ${props => props.theme.colors.background};
  text-align: center;
`;

const Logo = styled.div`
  font-size: 48px;
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const Title = styled.h1`
  font-size: ${props => props.theme.typography.fontSize.xxl};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  margin-bottom: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text};
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.typography.fontSize.lg};
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: ${props => props.theme.spacing.xxl};
  max-width: 600px;
  line-height: ${props => props.theme.typography.lineHeight.relaxed};
`;

const ActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  max-width: 800px;
  width: 100%;
  margin-bottom: ${props => props.theme.spacing.xxl};
`;

const ActionCard = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: ${props => props.theme.spacing.xl};
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  cursor: pointer;
  transition: ${props => props.theme.transitions.normal};
  text-align: center;
  
  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
    border-color: ${props => props.theme.colors.borderHover};
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
  
  &:active {
    transform: translateY(0);
  }
`;

const ActionIcon = styled.div`
  font-size: 32px;
  margin-bottom: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.primary};
`;

const ActionTitle = styled.h3`
  font-size: ${props => props.theme.typography.fontSize.lg};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text};
`;

const ActionDescription = styled.p`
  font-size: ${props => props.theme.typography.fontSize.md};
  color: ${props => props.theme.colors.textSecondary};
  line-height: ${props => props.theme.typography.lineHeight.normal};
`;

const RecentProjects = styled.div`
  width: 100%;
  max-width: 600px;
`;

const RecentTitle = styled.h3`
  font-size: ${props => props.theme.typography.fontSize.lg};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  margin-bottom: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text};
`;

const RecentList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const RecentItem = styled.button`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  text-align: left;
  
  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
    border-color: ${props => props.theme.colors.borderHover};
  }
`;

const RecentIcon = styled.div`
  width: 32px;
  height: 32px;
  background: ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.borderRadius.md};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.textInverse};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
`;

const RecentInfo = styled.div`
  flex: 1;
`;

const RecentName = styled.div`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text};
  margin-bottom: 2px;
`;

const RecentPath = styled.div`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.textMuted};
`;

export const WelcomeScreen: React.FC = () => {
  const { createProject, loadProject, recentProjects } = useProjectStore();

  const handleCreateProject = async () => {
    try {
      await createProject('New Project', 'A new DripForge Pro project');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleOpenProject = async () => {
    try {
      await loadProject();
    } catch (error) {
      console.error('Failed to open project:', error);
    }
  };

  const handleOpenRecent = async (projectPath: string) => {
    try {
      await loadProject(projectPath);
    } catch (error) {
      console.error('Failed to open recent project:', error);
    }
  };

  const handleImportAssets = async () => {
    // This would open the import dialog
    console.log('Import assets clicked');
  };

  return (
    <WelcomeContainer>
      <Logo>🎨</Logo>
      <Title>Welcome to DripForge Pro</Title>
      <Subtitle>
        The ultimate FiveM clothing development studio. Import, fix, preview, and ship your clothing packs with ease.
      </Subtitle>

      <ActionsGrid>
        <ActionCard onClick={handleCreateProject}>
          <ActionIcon>📁</ActionIcon>
          <ActionTitle>New Project</ActionTitle>
          <ActionDescription>
            Create a new clothing project and start building your pack
          </ActionDescription>
        </ActionCard>

        <ActionCard onClick={handleOpenProject}>
          <ActionIcon>📂</ActionIcon>
          <ActionTitle>Open Project</ActionTitle>
          <ActionDescription>
            Open an existing DripForge Pro project file
          </ActionDescription>
        </ActionCard>

        <ActionCard onClick={handleImportAssets}>
          <ActionIcon>📥</ActionIcon>
          <ActionTitle>Import Assets</ActionTitle>
          <ActionDescription>
            Import clothing files directly without creating a project
          </ActionDescription>
        </ActionCard>
      </ActionsGrid>

      {recentProjects.length > 0 && (
        <RecentProjects>
          <RecentTitle>Recent Projects</RecentTitle>
          <RecentList>
            {recentProjects.slice(0, 5).map((projectPath, index) => {
              const projectName = projectPath.split(/[/\\]/).pop()?.replace('.dfp', '') || 'Unknown Project';
              
              return (
                <RecentItem
                  key={projectPath}
                  onClick={() => handleOpenRecent(projectPath)}
                >
                  <RecentIcon>
                    {projectName.charAt(0).toUpperCase()}
                  </RecentIcon>
                  <RecentInfo>
                    <RecentName>{projectName}</RecentName>
                    <RecentPath>{projectPath}</RecentPath>
                  </RecentInfo>
                </RecentItem>
              );
            })}
          </RecentList>
        </RecentProjects>
      )}
    </WelcomeContainer>
  );
};
