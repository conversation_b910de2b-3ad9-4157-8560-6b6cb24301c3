/**
 * Project Store - Manages project state and operations
 */

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { Project, Item, SlotMapping, BatchOperation } from '../types/core';
import { ImportResult } from '../types/assets';

interface ProjectState {
  // Current project
  currentProject: Project | null;
  currentProjectPath: string | null;
  hasUnsavedChanges: boolean;

  // Items
  items: Item[];
  selectedItems: string[];
  filteredItems: Item[];

  // Slot mappings
  slotMappings: SlotMapping[];

  // Operations
  activeOperations: BatchOperation[];
  recentProjects: string[];

  // Loading states
  isLoading: boolean;
  loadingMessage: string;
}

interface ProjectActions {
  // Project operations
  createProject: (name: string, description?: string) => Promise<void>;
  loadProject: (filePath?: string) => Promise<void>;
  saveProject: (filePath?: string) => Promise<string | null>;
  closeProject: () => void;
  setUnsavedChanges: (hasChanges: boolean) => void;

  // Item operations
  addItems: (items: Item[]) => void;
  removeItems: (itemIds: string[]) => void;
  updateItem: (itemId: string, updates: Partial<Item>) => void;
  selectItems: (itemIds: string[]) => void;
  clearSelection: () => void;
  filterItems: (filters: any) => void;

  // Asset operations
  importAssets: (paths: string[]) => Promise<ImportResult>;
  routeSlots: () => Promise<void>;
  runQualityAssurance: (itemIds?: string[]) => Promise<void>;
  exportProject: (config: any) => Promise<void>;

  // Batch operations
  startOperation: (operation: BatchOperation) => void;
  updateOperation: (operationId: string, updates: Partial<BatchOperation>) => void;
  completeOperation: (operationId: string) => void;
  cancelOperation: (operationId: string) => void;

  // Utility
  setLoading: (loading: boolean, message?: string) => void;
}

type ProjectStore = ProjectState & ProjectActions;

export const useProjectStore = create<ProjectStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial state
      currentProject: null,
      currentProjectPath: null,
      hasUnsavedChanges: false,
      items: [],
      selectedItems: [],
      filteredItems: [],
      slotMappings: [],
      activeOperations: [],
      recentProjects: [],
      isLoading: false,
      loadingMessage: '',

      // Project operations
      createProject: async (name: string, description?: string) => {
        set((state) => {
          state.isLoading = true;
          state.loadingMessage = 'Creating project...';
        });

        try {
          const projectData = await window.electronAPI.project.create({
            name,
            description
          });

          set((state) => {
            state.currentProject = projectData;
            state.currentProjectPath = null;
            state.hasUnsavedChanges = true;
            state.items = [];
            state.selectedItems = [];
            state.filteredItems = [];
            state.slotMappings = [];
            state.isLoading = false;
            state.loadingMessage = '';
          });
        } catch (error) {
          console.error('Failed to create project:', error);
          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });
          throw error;
        }
      },

      loadProject: async (filePath?: string) => {
        set((state) => {
          state.isLoading = true;
          state.loadingMessage = 'Loading project...';
        });

        try {
          const result = await window.electronAPI.project.load(filePath);
          
          if (result) {
            set((state) => {
              state.currentProject = result.project;
              state.currentProjectPath = result.filePath;
              state.hasUnsavedChanges = false;
              state.items = result.project.items;
              state.selectedItems = [];
              state.filteredItems = result.project.items;
              state.slotMappings = result.project.slotMappings;
            });

            // Add to recent projects
            const recentProjects = await window.electronAPI.settings.get('recentProjects') || [];
            if (!recentProjects.includes(result.filePath)) {
              const updated = [result.filePath, ...recentProjects.slice(0, 9)];
              await window.electronAPI.settings.set('recentProjects', updated);
              
              set((state) => {
                state.recentProjects = updated;
              });
            }
          }

          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });
        } catch (error) {
          console.error('Failed to load project:', error);
          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });
          throw error;
        }
      },

      saveProject: async (filePath?: string) => {
        const { currentProject } = get();
        if (!currentProject) return null;

        set((state) => {
          state.isLoading = true;
          state.loadingMessage = 'Saving project...';
        });

        try {
          const savedPath = await window.electronAPI.project.save(currentProject, filePath);
          
          if (savedPath) {
            set((state) => {
              state.currentProjectPath = savedPath;
              state.hasUnsavedChanges = false;
              state.isLoading = false;
              state.loadingMessage = '';
            });
          }

          return savedPath;
        } catch (error) {
          console.error('Failed to save project:', error);
          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });
          throw error;
        }
      },

      closeProject: () => {
        set((state) => {
          state.currentProject = null;
          state.currentProjectPath = null;
          state.hasUnsavedChanges = false;
          state.items = [];
          state.selectedItems = [];
          state.filteredItems = [];
          state.slotMappings = [];
          state.activeOperations = [];
        });
      },

      setUnsavedChanges: (hasChanges: boolean) => {
        set((state) => {
          state.hasUnsavedChanges = hasChanges;
        });
      },

      // Item operations
      addItems: (items: Item[]) => {
        set((state) => {
          state.items.push(...items);
          state.filteredItems = state.items; // Reset filter
          state.hasUnsavedChanges = true;
          
          if (state.currentProject) {
            state.currentProject.items = state.items;
            state.currentProject.modified = new Date();
          }
        });
      },

      removeItems: (itemIds: string[]) => {
        set((state) => {
          state.items = state.items.filter(item => !itemIds.includes(item.id));
          state.filteredItems = state.items;
          state.selectedItems = state.selectedItems.filter(id => !itemIds.includes(id));
          state.hasUnsavedChanges = true;
          
          if (state.currentProject) {
            state.currentProject.items = state.items;
            state.currentProject.modified = new Date();
          }
        });
      },

      updateItem: (itemId: string, updates: Partial<Item>) => {
        set((state) => {
          const itemIndex = state.items.findIndex(item => item.id === itemId);
          if (itemIndex !== -1) {
            Object.assign(state.items[itemIndex], updates);
            state.items[itemIndex].metadata.modified = new Date();
            state.hasUnsavedChanges = true;
            
            if (state.currentProject) {
              state.currentProject.items = state.items;
              state.currentProject.modified = new Date();
            }
          }
        });
      },

      selectItems: (itemIds: string[]) => {
        set((state) => {
          state.selectedItems = itemIds;
        });
      },

      clearSelection: () => {
        set((state) => {
          state.selectedItems = [];
        });
      },

      filterItems: (filters: any) => {
        set((state) => {
          let filtered = state.items;

          if (filters.gender) {
            filtered = filtered.filter(item => item.gender === filters.gender);
          }

          if (filters.slot) {
            filtered = filtered.filter(item => item.slot === filters.slot);
          }

          if (filters.hasConflicts !== undefined) {
            // This would check for actual conflicts
            filtered = filtered.filter(item => {
              // Mock conflict detection
              return filters.hasConflicts ? Math.random() > 0.8 : Math.random() > 0.2;
            });
          }

          if (filters.qaStatus) {
            filtered = filtered.filter(item => {
              if (!item.qa) return filters.qaStatus === 'pending';
              return item.qa.passed ? 
                filters.qaStatus === 'passed' : 
                filters.qaStatus === 'failed';
            });
          }

          state.filteredItems = filtered;
        });
      },

      // Asset operations
      importAssets: async (paths: string[]) => {
        const { currentProject } = get();
        if (!currentProject) throw new Error('No project loaded');

        const operation: BatchOperation = {
          id: `import_${Date.now()}`,
          type: 'import',
          status: 'running',
          progress: 0,
          total: paths.length,
          startTime: new Date(),
          errors: [],
          warnings: []
        };

        get().startOperation(operation);

        try {
          const result = await window.electronAPI.assets.ingest(paths, currentProject.id);
          
          get().completeOperation(operation.id);
          return result;
        } catch (error) {
          get().updateOperation(operation.id, {
            status: 'failed',
            errors: [error instanceof Error ? error.message : String(error)]
          });
          throw error;
        }
      },

      routeSlots: async () => {
        const { currentProject, items } = get();
        if (!currentProject) throw new Error('No project loaded');

        set((state) => {
          state.isLoading = true;
          state.loadingMessage = 'Routing slots...';
        });

        try {
          const result = await window.electronAPI.slots.route(items, currentProject.settings);
          
          set((state) => {
            state.items = result.resolvedItems;
            state.filteredItems = result.resolvedItems;
            state.slotMappings = result.newMappings;
            state.hasUnsavedChanges = true;
            state.isLoading = false;
            state.loadingMessage = '';
            
            if (state.currentProject) {
              state.currentProject.items = result.resolvedItems;
              state.currentProject.slotMappings = result.newMappings;
              state.currentProject.modified = new Date();
            }
          });
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });
          throw error;
        }
      },

      runQualityAssurance: async (itemIds?: string[]) => {
        const { items, selectedItems } = get();
        const targetItems = itemIds || selectedItems;
        
        if (targetItems.length === 0) {
          throw new Error('No items selected for QA scan');
        }

        set((state) => {
          state.isLoading = true;
          state.loadingMessage = 'Running quality assurance...';
        });

        try {
          const result = await window.electronAPI.assets.validate(targetItems);
          
          // Update items with QA results
          set((state) => {
            for (const [itemId, qaResult] of result.results) {
              const itemIndex = state.items.findIndex(item => item.id === itemId);
              if (itemIndex !== -1) {
                state.items[itemIndex].qa = {
                  clippingScore: 0, // Would be calculated from actual results
                  lods: qaResult.metrics.lodCoverage,
                  tris: qaResult.metrics.triangleCount,
                  notes: qaResult.errors.map((e: any) => e.message),
                  passed: qaResult.isValid,
                  lastScanned: new Date()
                };
              }
            }
            
            state.hasUnsavedChanges = true;
            state.isLoading = false;
            state.loadingMessage = '';
          });
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });
          throw error;
        }
      },

      exportProject: async (config: any) => {
        const { items } = get();
        
        set((state) => {
          state.isLoading = true;
          state.loadingMessage = 'Exporting project...';
        });

        try {
          const result = await window.electronAPI.export.generate(config, items);
          
          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });

          return result;
        } catch (error) {
          set((state) => {
            state.isLoading = false;
            state.loadingMessage = '';
          });
          throw error;
        }
      },

      // Batch operations
      startOperation: (operation: BatchOperation) => {
        set((state) => {
          state.activeOperations.push(operation);
        });
      },

      updateOperation: (operationId: string, updates: Partial<BatchOperation>) => {
        set((state) => {
          const opIndex = state.activeOperations.findIndex(op => op.id === operationId);
          if (opIndex !== -1) {
            Object.assign(state.activeOperations[opIndex], updates);
          }
        });
      },

      completeOperation: (operationId: string) => {
        set((state) => {
          const opIndex = state.activeOperations.findIndex(op => op.id === operationId);
          if (opIndex !== -1) {
            state.activeOperations[opIndex].status = 'completed';
            state.activeOperations[opIndex].endTime = new Date();
          }
        });
      },

      cancelOperation: (operationId: string) => {
        set((state) => {
          const opIndex = state.activeOperations.findIndex(op => op.id === operationId);
          if (opIndex !== -1) {
            state.activeOperations[opIndex].status = 'cancelled';
            state.activeOperations[opIndex].endTime = new Date();
          }
        });
      },

      // Utility
      setLoading: (loading: boolean, message?: string) => {
        set((state) => {
          state.isLoading = loading;
          state.loadingMessage = message || '';
        });
      }
    }))
  )
);

// Subscribe to changes for auto-save
useProjectStore.subscribe(
  (state) => state.hasUnsavedChanges,
  (hasChanges: boolean) => {
    if (hasChanges) {
      // Could implement auto-save logic here
      console.log('Project has unsaved changes');
    }
  }
);
