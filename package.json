{"name": "dripforge-pro", "version": "1.0.0", "description": "FiveM Clothing Development Studio - Import. Fix. Preview. Ship.", "main": "dist/main/main.js", "homepage": "./", "author": "DripForge Pro", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "cross-env NODE_ENV=development webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "cross-env NODE_ENV=development webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "cross-env NODE_ENV=production webpack --config webpack.main.config.js --mode production", "build:renderer": "cross-env NODE_ENV=production webpack --config webpack.renderer.config.js --mode production", "start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:mac": "electron-builder --mac", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "cli": "node dist/cli/index.js"}, "dependencies": {"@types/three": "^0.157.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "commander": "^11.1.0", "electron-reload": "^2.0.0-alpha.1", "electron-store": "^8.1.0", "electron-updater": "^6.1.4", "fs-extra": "^11.1.1", "immer": "^10.0.3", "inquirer": "^8.2.6", "lodash": "^4.17.21", "mime-types": "^2.1.35", "ora": "^5.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "sharp": "^0.32.6", "styled-components": "^6.1.1", "three": "^0.157.0", "uuid": "^9.0.1", "yauzl": "^2.10.0", "yazl": "^2.5.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/mime-types": "^2.1.4", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@types/yauzl": "^2.10.3", "@types/yazl": "^2.4.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "concurrently": "^8.2.2", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "electron": "^27.1.3", "electron-builder": "^24.6.4", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "url-loader": "^4.1.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "build": {"appId": "com.dripforge.pro", "productName": "DripForge Pro", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.developer-tools", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "keywords": ["fivem", "gta5", "clothing", "development", "3d", "modeling", "game-development"]}