/**
 * Slot Router Service - Handles conflict detection and slot remapping
 */

import { Item, SlotMapping, Gender, Component, Prop, ReservedRange } from '@/types/core';
import { ConflictResolution } from '@/types/core';

export class SlotRouterService {
  private slotMappings: Map<string, SlotMapping> = new Map();
  private reservedRanges: ReservedRange[] = [];

  /**
   * Route slots for items, resolving conflicts automatically
   */
  async routeSlots(items: Item[], settings: { reservedRanges: ReservedRange[] }): Promise<{
    resolvedItems: Item[];
    conflicts: ConflictResolution[];
    newMappings: SlotMapping[];
  }> {
    this.reservedRanges = settings.reservedRanges;
    
    const resolvedItems: Item[] = [];
    const conflicts: ConflictResolution[] = [];
    const newMappings: SlotMapping[] = [];

    for (const item of items) {
      const conflict = this.detectConflict(item);
      
      if (conflict) {
        const resolution = await this.resolveConflict(item, conflict);
        conflicts.push(resolution);
        
        // Apply resolution
        const resolvedItem = this.applyResolution(item, resolution);
        resolvedItems.push(resolvedItem);
        
        // Create new mapping
        const mapping = this.createSlotMapping(resolvedItem);
        newMappings.push(mapping);
        this.slotMappings.set(this.getMappingKey(mapping), mapping);
      } else {
        // No conflict, use original item
        resolvedItems.push(item);
        
        const mapping = this.createSlotMapping(item);
        newMappings.push(mapping);
        this.slotMappings.set(this.getMappingKey(mapping), mapping);
      }
    }

    return { resolvedItems, conflicts, newMappings };
  }

  /**
   * Check for conflicts without resolving them
   */
  async checkConflicts(items: Item[]): Promise<ConflictResolution[]> {
    const conflicts: ConflictResolution[] = [];

    for (const item of items) {
      const conflict = this.detectConflict(item);
      if (conflict) {
        const resolution = await this.resolveConflict(item, conflict);
        conflicts.push(resolution);
      }
    }

    return conflicts;
  }

  /**
   * Detect if an item has slot conflicts
   */
  private detectConflict(item: Item): SlotMapping | null {
    const key = this.getItemMappingKey(item);
    const existing = this.slotMappings.get(key);
    
    if (existing && existing.assignedTo !== item.id) {
      return existing;
    }

    // Check if slot is in reserved range
    const isReserved = this.isSlotReserved(item);
    if (isReserved) {
      return {
        gender: item.gender,
        slot: item.slot,
        drawable: item.drawable,
        texture: item.textures[0] || 0,
        reserved: true
      };
    }

    return null;
  }

  /**
   * Resolve a conflict by finding alternative slots
   */
  private async resolveConflict(item: Item, conflict: SlotMapping): Promise<ConflictResolution> {
    const originalSlot = this.createSlotMapping(item);
    
    // Find next available slot
    const newSlot = this.findNextAvailableSlot(item);
    
    const resolution: ConflictResolution = {
      itemId: item.id,
      originalSlot,
      newSlot,
      reason: conflict.reserved ? 'Reserved range conflict' : 'Slot already occupied',
      autoResolved: true
    };

    return resolution;
  }

  /**
   * Find the next available slot for an item
   */
  private findNextAvailableSlot(item: Item): SlotMapping {
    let drawable = item.drawable;
    const maxAttempts = 1000; // Prevent infinite loops
    let attempts = 0;

    while (attempts < maxAttempts) {
      drawable++;
      
      // Skip reserved ranges
      if (this.isSlotInReservedRange(item.gender, item.slot, drawable)) {
        continue;
      }

      // Check if slot is available
      const testKey = `${item.gender}_${item.slot}_${drawable}_${item.textures[0] || 0}`;
      if (!this.slotMappings.has(testKey)) {
        return {
          gender: item.gender,
          slot: item.slot,
          drawable,
          texture: item.textures[0] || 0,
          reserved: false,
          assignedTo: item.id
        };
      }

      attempts++;
    }

    // Fallback: use a high number range
    return {
      gender: item.gender,
      slot: item.slot,
      drawable: 9000 + Math.floor(Math.random() * 1000),
      texture: item.textures[0] || 0,
      reserved: false,
      assignedTo: item.id
    };
  }

  /**
   * Apply conflict resolution to an item
   */
  private applyResolution(item: Item, resolution: ConflictResolution): Item {
    return {
      ...item,
      drawable: resolution.newSlot.drawable,
      textures: [resolution.newSlot.texture]
    };
  }

  /**
   * Check if a slot is reserved
   */
  private isSlotReserved(item: Item): boolean {
    return this.isSlotInReservedRange(item.gender, item.slot, item.drawable);
  }

  /**
   * Check if a specific slot is in a reserved range
   */
  private isSlotInReservedRange(gender: Gender, slot: Component | Prop, drawable: number): boolean {
    return this.reservedRanges.some(range => 
      range.gender === gender &&
      range.slot === slot &&
      drawable >= range.startDrawable &&
      drawable <= range.endDrawable
    );
  }

  /**
   * Create a slot mapping from an item
   */
  private createSlotMapping(item: Item): SlotMapping {
    return {
      gender: item.gender,
      slot: item.slot,
      drawable: item.drawable,
      texture: item.textures[0] || 0,
      reserved: false,
      assignedTo: item.id
    };
  }

  /**
   * Get mapping key for an item
   */
  private getItemMappingKey(item: Item): string {
    return `${item.gender}_${item.slot}_${item.drawable}_${item.textures[0] || 0}`;
  }

  /**
   * Get mapping key for a slot mapping
   */
  private getMappingKey(mapping: SlotMapping): string {
    return `${mapping.gender}_${mapping.slot}_${mapping.drawable}_${mapping.texture}`;
  }

  /**
   * Load existing slot mappings
   */
  loadExistingMappings(mappings: SlotMapping[]): void {
    this.slotMappings.clear();
    
    for (const mapping of mappings) {
      const key = this.getMappingKey(mapping);
      this.slotMappings.set(key, mapping);
    }
  }

  /**
   * Get all current slot mappings
   */
  getAllMappings(): SlotMapping[] {
    return Array.from(this.slotMappings.values());
  }

  /**
   * Get slot usage statistics
   */
  getSlotStatistics(): {
    totalSlots: number;
    usedSlots: number;
    reservedSlots: number;
    availableSlots: number;
    byGender: Record<Gender, {
      total: number;
      used: number;
      reserved: number;
    }>;
    bySlot: Record<string, {
      total: number;
      used: number;
      reserved: number;
    }>;
  } {
    const stats = {
      totalSlots: this.slotMappings.size,
      usedSlots: this.slotMappings.size,
      reservedSlots: 0,
      availableSlots: 0,
      byGender: {
        male: { total: 0, used: 0, reserved: 0 },
        female: { total: 0, used: 0, reserved: 0 }
      } as Record<Gender, { total: number; used: number; reserved: number }>,
      bySlot: {} as Record<string, { total: number; used: number; reserved: number }>
    };

    // Count reserved slots
    for (const range of this.reservedRanges) {
      const count = range.endDrawable - range.startDrawable + 1;
      stats.reservedSlots += count;
      stats.byGender[range.gender].reserved += count;
      
      const slotKey = `${range.gender}_${range.slot}`;
      if (!stats.bySlot[slotKey]) {
        stats.bySlot[slotKey] = { total: 0, used: 0, reserved: 0 };
      }
      stats.bySlot[slotKey].reserved += count;
    }

    // Count used slots by gender and slot
    for (const mapping of this.slotMappings.values()) {
      stats.byGender[mapping.gender].used++;
      
      const slotKey = `${mapping.gender}_${mapping.slot}`;
      if (!stats.bySlot[slotKey]) {
        stats.bySlot[slotKey] = { total: 0, used: 0, reserved: 0 };
      }
      stats.bySlot[slotKey].used++;
    }

    // Calculate totals (simplified - would need actual slot limits)
    const maxSlotsPerGender = 10000; // Approximate GTA V limit
    stats.byGender.male.total = maxSlotsPerGender;
    stats.byGender.female.total = maxSlotsPerGender;
    
    stats.availableSlots = (maxSlotsPerGender * 2) - stats.usedSlots - stats.reservedSlots;

    return stats;
  }

  /**
   * Validate slot mappings for consistency
   */
  validateMappings(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    const seenSlots = new Set<string>();
    const duplicateAssignments = new Map<string, string[]>();

    for (const mapping of this.slotMappings.values()) {
      const slotKey = this.getMappingKey(mapping);
      
      // Check for duplicate slot assignments
      if (seenSlots.has(slotKey)) {
        if (!duplicateAssignments.has(slotKey)) {
          duplicateAssignments.set(slotKey, []);
        }
        duplicateAssignments.get(slotKey)!.push(mapping.assignedTo || 'unknown');
      }
      seenSlots.add(slotKey);

      // Check if slot is in reserved range but not marked as reserved
      if (this.isSlotInReservedRange(mapping.gender, mapping.slot, mapping.drawable) && !mapping.reserved) {
        warnings.push(`Slot ${slotKey} is in reserved range but not marked as reserved`);
      }
    }

    // Report duplicate assignments
    for (const [slotKey, itemIds] of duplicateAssignments) {
      errors.push(`Duplicate slot assignment ${slotKey} for items: ${itemIds.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
