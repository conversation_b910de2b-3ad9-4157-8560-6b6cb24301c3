/**
 * Export System Component - Multi-format export with configuration
 */

import React, { useState } from 'react';
import styled from 'styled-components';

const ExportContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px;
`;

const ExportHeader = styled.div`
  margin-bottom: 24px;
`;

const Title = styled.h2`
  color: #ffffff;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
`;

const Subtitle = styled.p`
  color: #888888;
  font-size: 14px;
`;

const ExportOptions = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
`;

const OptionGroup = styled.div`
  background: #2d2d2d;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #404040;
`;

const OptionTitle = styled.h3`
  color: #ffffff;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
`;

const FormatGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
`;

const FormatCard = styled.div<{ selected: boolean }>`
  padding: 16px;
  background: ${props => props.selected ? '#007acc' : '#1a1a1a'};
  border: 2px solid ${props => props.selected ? '#007acc' : '#404040'};
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;

  &:hover {
    border-color: #007acc;
    background: ${props => props.selected ? '#007acc' : '#353535'};
  }
`;

const FormatIcon = styled.div`
  font-size: 24px;
  margin-bottom: 8px;
`;

const FormatName = styled.div`
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
`;

const FormatDesc = styled.div`
  font-size: 12px;
  color: #888888;
`;

const ConfigSection = styled.div`
  margin-bottom: 16px;
`;

const ConfigLabel = styled.label`
  display: block;
  color: #ffffff;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
`;

const ConfigInput = styled.input`
  width: 100%;
  padding: 8px 12px;
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #007acc;
  }
`;

const ConfigTextarea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;

  &:focus {
    outline: none;
    border-color: #007acc;
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const CheckboxItem = styled.label`
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #ffffff;
  font-size: 14px;

  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #007acc;
  }
`;

const ExportActions = styled.div`
  display: flex;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid #404040;
  margin-top: auto;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 120px;

  ${props => props.variant === 'primary' ? `
    background: #007acc;
    color: white;
    &:hover { background: #1a8cdd; }
    &:disabled { background: #404040; cursor: not-allowed; }
  ` : `
    background: #404040;
    color: white;
    &:hover { background: #505050; }
  `}
`;

const ProgressSection = styled.div<{ visible: boolean }>`
  display: ${props => props.visible ? 'block' : 'none'};
  background: #2d2d2d;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;
  border: 1px solid #404040;
`;

const ProgressBar = styled.div<{ progress: number }>`
  width: 100%;
  height: 8px;
  background: #404040;
  border-radius: 4px;
  overflow: hidden;
  margin: 12px 0;

  &::after {
    content: '';
    display: block;
    width: ${props => props.progress}%;
    height: 100%;
    background: linear-gradient(90deg, #007acc, #1a8cdd);
    transition: width 0.3s ease;
  }
`;

const ProgressText = styled.div`
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
`;

const ProgressDetails = styled.div`
  color: #888888;
  font-size: 12px;
  margin-top: 4px;
`;

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  icon: string;
}

const exportFormats: ExportFormat[] = [
  { id: 'qb-clothing', name: 'QB-Clothing', description: 'QBCore clothing shop', icon: '🛍️' },
  { id: 'esx', name: 'ESX', description: 'ESX clothing system', icon: '👔' },
  { id: 'fivem-resource', name: 'FiveM Resource', description: 'Complete resource pack', icon: '📦' },
  { id: 'icons', name: 'Icons Only', description: 'Thumbnail images', icon: '🖼️' },
  { id: 'marketing', name: 'Marketing Pack', description: 'High-quality renders', icon: '📸' },
  { id: 'escrow', name: 'Escrow Package', description: 'Protected release', icon: '🔒' }
];

interface ExportSystemProps {
  items: any[];
  onExport?: (config: any) => void;
}

export const ExportSystem: React.FC<ExportSystemProps> = ({ items = [], onExport }) => {
  const [selectedFormats, setSelectedFormats] = useState<string[]>(['qb-clothing']);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState('');
  
  const [config, setConfig] = useState({
    resourceName: 'my-clothing-pack',
    author: 'Your Name',
    description: 'Custom clothing pack created with DripForge Pro',
    version: '1.0.0',
    includeQBClothing: true,
    includeESX: false,
    includeIcons: true,
    includeMarketing: false,
    escrowMode: false,
    watermarkImages: false,
    outputPath: ''
  });

  const handleFormatToggle = (formatId: string) => {
    setSelectedFormats(prev => 
      prev.includes(formatId)
        ? prev.filter(id => id !== formatId)
        : [...prev, formatId]
    );
  };

  const handleConfigChange = (key: string, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleExport = async () => {
    if (selectedFormats.length === 0) {
      alert('Please select at least one export format');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);
    setExportStatus('Preparing export...');

    try {
      // Simulate export process
      const steps = [
        'Validating items...',
        'Processing meshes...',
        'Generating textures...',
        'Creating configurations...',
        'Packaging files...',
        'Finalizing export...'
      ];

      for (let i = 0; i < steps.length; i++) {
        setExportStatus(steps[i]);
        setExportProgress((i + 1) / steps.length * 100);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      setExportStatus('Export completed successfully!');
      
      if (onExport) {
        onExport({
          formats: selectedFormats,
          config,
          items
        });
      }

    } catch (error) {
      setExportStatus('Export failed: ' + error);
    } finally {
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(0);
        setExportStatus('');
      }, 2000);
    }
  };

  const selectOutputPath = async () => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.dialog.saveFile({
          title: 'Select Export Directory',
          defaultPath: config.resourceName,
          properties: ['createDirectory']
        });

        if (!result.canceled && result.filePath) {
          handleConfigChange('outputPath', result.filePath);
        }
      }
    } catch (error) {
      console.error('Failed to select output path:', error);
    }
  };

  return (
    <ExportContainer>
      <ExportHeader>
        <Title>Export Project</Title>
        <Subtitle>
          Export your clothing pack in multiple formats for different FiveM frameworks
        </Subtitle>
      </ExportHeader>

      <ExportOptions>
        <OptionGroup>
          <OptionTitle>Export Formats</OptionTitle>
          <FormatGrid>
            {exportFormats.map(format => (
              <FormatCard
                key={format.id}
                selected={selectedFormats.includes(format.id)}
                onClick={() => handleFormatToggle(format.id)}
              >
                <FormatIcon>{format.icon}</FormatIcon>
                <FormatName>{format.name}</FormatName>
                <FormatDesc>{format.description}</FormatDesc>
              </FormatCard>
            ))}
          </FormatGrid>
        </OptionGroup>

        <OptionGroup>
          <OptionTitle>Configuration</OptionTitle>
          
          <ConfigSection>
            <ConfigLabel>Resource Name</ConfigLabel>
            <ConfigInput
              type="text"
              value={config.resourceName}
              onChange={(e) => handleConfigChange('resourceName', e.target.value)}
              placeholder="my-clothing-pack"
            />
          </ConfigSection>

          <ConfigSection>
            <ConfigLabel>Author</ConfigLabel>
            <ConfigInput
              type="text"
              value={config.author}
              onChange={(e) => handleConfigChange('author', e.target.value)}
              placeholder="Your Name"
            />
          </ConfigSection>

          <ConfigSection>
            <ConfigLabel>Description</ConfigLabel>
            <ConfigTextarea
              value={config.description}
              onChange={(e) => handleConfigChange('description', e.target.value)}
              placeholder="Describe your clothing pack..."
            />
          </ConfigSection>

          <ConfigSection>
            <ConfigLabel>Version</ConfigLabel>
            <ConfigInput
              type="text"
              value={config.version}
              onChange={(e) => handleConfigChange('version', e.target.value)}
              placeholder="1.0.0"
            />
          </ConfigSection>
        </OptionGroup>
      </ExportOptions>

      <OptionGroup>
        <OptionTitle>Export Options</OptionTitle>
        <CheckboxGroup>
          <CheckboxItem>
            <input
              type="checkbox"
              checked={config.includeIcons}
              onChange={(e) => handleConfigChange('includeIcons', e.target.checked)}
            />
            Include thumbnail icons
          </CheckboxItem>
          <CheckboxItem>
            <input
              type="checkbox"
              checked={config.includeMarketing}
              onChange={(e) => handleConfigChange('includeMarketing', e.target.checked)}
            />
            Generate marketing images
          </CheckboxItem>
          <CheckboxItem>
            <input
              type="checkbox"
              checked={config.watermarkImages}
              onChange={(e) => handleConfigChange('watermarkImages', e.target.checked)}
            />
            Add watermark to images
          </CheckboxItem>
          <CheckboxItem>
            <input
              type="checkbox"
              checked={config.escrowMode}
              onChange={(e) => handleConfigChange('escrowMode', e.target.checked)}
            />
            Enable escrow protection
          </CheckboxItem>
        </CheckboxGroup>
      </OptionGroup>

      <ProgressSection visible={isExporting}>
        <ProgressText>Exporting...</ProgressText>
        <ProgressBar progress={exportProgress} />
        <ProgressDetails>{exportStatus}</ProgressDetails>
      </ProgressSection>

      <ExportActions>
        <ActionButton onClick={selectOutputPath}>
          📁 Select Output Folder
        </ActionButton>
        <ActionButton 
          variant="primary" 
          onClick={handleExport}
          disabled={isExporting || items.length === 0}
        >
          {isExporting ? 'Exporting...' : `Export ${items.length} Items`}
        </ActionButton>
      </ExportActions>
    </ExportContainer>
  );
};
