/**
 * Preload script - Exposes safe APIs to the renderer process
 */
interface ElectronAPI {
    fs: {
        readFile: (filePath: string) => Promise<string>;
        writeFile: (filePath: string, content: string) => Promise<boolean>;
        exists: (filePath: string) => Promise<boolean>;
        readDir: (dirPath: string) => Promise<Array<{
            name: string;
            isDirectory: boolean;
            isFile: boolean;
            path: string;
        }>>;
    };
    dialog: {
        openFile: (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>;
        saveFile: (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>;
        showMessage: (options: Electron.MessageBoxOptions) => Promise<Electron.MessageBoxReturnValue>;
        showError: (title: string, content: string) => Promise<void>;
    };
    shell: {
        openExternal: (url: string) => Promise<void>;
        showItemInFolder: (fullPath: string) => void;
        openPath: (fullPath: string) => Promise<string>;
    };
    settings: {
        get: (key?: string) => Promise<any>;
        set: (key: string, value: any) => Promise<boolean>;
        reset: () => Promise<boolean>;
    };
    project: {
        create: (projectData: any) => Promise<any>;
        save: (project: any, filePath?: string) => Promise<string | null>;
        load: (filePath?: string) => Promise<{
            project: any;
            filePath: string;
        } | null>;
    };
    assets: {
        ingest: (paths: string[], projectId: string) => Promise<any>;
        validate: (itemIds: string[]) => Promise<any>;
    };
    slots: {
        route: (items: any[], settings: any) => Promise<any>;
        checkConflicts: (items: any[]) => Promise<any>;
    };
    export: {
        generate: (config: any, items: any[]) => Promise<any>;
    };
    window: {
        minimize: () => Promise<void>;
        maximize: () => Promise<void>;
        close: () => Promise<void>;
    };
    app: {
        getVersion: () => Promise<string>;
        getPlatform: () => Promise<string>;
        restart: () => Promise<void>;
    };
    on: (channel: string, callback: (...args: any[]) => void) => void;
    off: (channel: string, callback: (...args: any[]) => void) => void;
    once: (channel: string, callback: (...args: any[]) => void) => void;
}
declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}
export {};
