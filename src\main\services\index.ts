/**
 * Services initialization and management
 */

import Store from 'electron-store';
import * as fs from 'fs-extra';
import * as path from 'path';
import { AppSettings } from '../../types/core';

export async function initializeServices(store: Store<AppSettings>): Promise<void> {
  // Ensure temp directory exists
  const tempDir = store.get('tempDirectory');
  await fs.ensureDir(tempDir);

  // Create subdirectories for different operations
  await fs.ensureDir(path.join(tempDir, 'imports'));
  await fs.ensureDir(path.join(tempDir, 'exports'));
  await fs.ensureDir(path.join(tempDir, 'thumbnails'));
  await fs.ensureDir(path.join(tempDir, 'cache'));
  await fs.ensureDir(path.join(tempDir, 'logs'));

  // Initialize logging
  initializeLogging(store);

  // Clean up old temp files (older than 24 hours)
  await cleanupTempFiles(tempDir);

  console.log('Services initialized successfully');
}

function initializeLogging(store: Store<AppSettings>): void {
  const logLevel = store.get('logLevel', 'info');
  const tempDir = store.get('tempDirectory');
  const logFile = path.join(tempDir, 'logs', `dripforge-${new Date().toISOString().split('T')[0]}.log`);

  // Override console methods to also write to file
  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    debug: console.debug
  };

  const writeToLog = (level: string, ...args: any[]) => {
    const timestamp = new Date().toISOString();
    const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
    const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
    
    fs.appendFile(logFile, logEntry).catch(() => {
      // Silently fail if we can't write to log file
    });
  };

  console.log = (...args: any[]) => {
    originalConsole.log(...args);
    if (['debug', 'info', 'warn', 'error'].includes(logLevel)) {
      writeToLog('info', ...args);
    }
  };

  console.error = (...args: any[]) => {
    originalConsole.error(...args);
    if (['warn', 'error'].includes(logLevel)) {
      writeToLog('error', ...args);
    }
  };

  console.warn = (...args: any[]) => {
    originalConsole.warn(...args);
    if (['warn', 'error'].includes(logLevel)) {
      writeToLog('warn', ...args);
    }
  };

  console.debug = (...args: any[]) => {
    originalConsole.debug(...args);
    if (logLevel === 'debug') {
      writeToLog('debug', ...args);
    }
  };
}

async function cleanupTempFiles(tempDir: string): Promise<void> {
  try {
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    const subdirs = ['imports', 'exports', 'thumbnails', 'cache'];

    for (const subdir of subdirs) {
      const dirPath = path.join(tempDir, subdir);
      if (await fs.pathExists(dirPath)) {
        const files = await fs.readdir(dirPath);
        
        for (const file of files) {
          const filePath = path.join(dirPath, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime.getTime() < oneDayAgo) {
            await fs.remove(filePath);
          }
        }
      }
    }
  } catch (error) {
    console.warn('Failed to cleanup temp files:', error);
  }
}
