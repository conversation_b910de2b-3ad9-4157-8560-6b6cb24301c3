/**
 * Preload script - Exposes safe APIs to the renderer process
 */

import { contextBridge, ipcRenderer } from 'electron';

// Define the API interface
interface ElectronAPI {
  // File system operations
  fs: {
    readFile: (filePath: string) => Promise<string>;
    writeFile: (filePath: string, content: string) => Promise<boolean>;
    exists: (filePath: string) => Promise<boolean>;
    readDir: (dirPath: string) => Promise<Array<{
      name: string;
      isDirectory: boolean;
      isFile: boolean;
      path: string;
    }>>;
  };

  // Dialog operations
  dialog: {
    openFile: (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>;
    saveFile: (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>;
    showMessage: (options: Electron.MessageBoxOptions) => Promise<Electron.MessageBoxReturnValue>;
    showError: (title: string, content: string) => Promise<void>;
  };

  // Shell operations
  shell: {
    openExternal: (url: string) => Promise<void>;
    showItemInFolder: (fullPath: string) => void;
    openPath: (fullPath: string) => Promise<string>;
  };

  // App settings
  settings: {
    get: (key?: string) => Promise<any>;
    set: (key: string, value: any) => Promise<boolean>;
    reset: () => Promise<boolean>;
  };

  // Project operations
  project: {
    create: (projectData: any) => Promise<any>;
    save: (project: any, filePath?: string) => Promise<string | null>;
    load: (filePath?: string) => Promise<{ project: any; filePath: string } | null>;
  };

  // Asset operations
  assets: {
    ingest: (paths: string[], projectId: string) => Promise<any>;
    validate: (itemIds: string[]) => Promise<any>;
  };

  // Slot operations
  slots: {
    route: (items: any[], settings: any) => Promise<any>;
    checkConflicts: (items: any[]) => Promise<any>;
  };

  // Export operations
  export: {
    generate: (config: any, items: any[]) => Promise<any>;
  };

  // Window operations
  window: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
  };

  // App operations
  app: {
    getVersion: () => Promise<string>;
    getPlatform: () => Promise<string>;
    restart: () => Promise<void>;
  };

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  once: (channel: string, callback: (...args: any[]) => void) => void;
}

// Create the API object
const electronAPI: ElectronAPI = {
  // File system operations
  fs: {
    readFile: (filePath: string) => ipcRenderer.invoke('fs:read-file', filePath),
    writeFile: (filePath: string, content: string) => ipcRenderer.invoke('fs:write-file', filePath, content),
    exists: (filePath: string) => ipcRenderer.invoke('fs:exists', filePath),
    readDir: (dirPath: string) => ipcRenderer.invoke('fs:read-dir', dirPath),
  },

  // Dialog operations
  dialog: {
    openFile: (options: Electron.OpenDialogOptions) => ipcRenderer.invoke('dialog:open-file', options),
    saveFile: (options: Electron.SaveDialogOptions) => ipcRenderer.invoke('dialog:save-file', options),
    showMessage: (options: Electron.MessageBoxOptions) => ipcRenderer.invoke('dialog:show-message', options),
    showError: (title: string, content: string) => ipcRenderer.invoke('dialog:show-error', title, content),
  },

  // Shell operations
  shell: {
    openExternal: (url: string) => ipcRenderer.invoke('shell:open-external', url),
    showItemInFolder: (fullPath: string) => ipcRenderer.invoke('shell:show-item-in-folder', fullPath),
    openPath: (fullPath: string) => ipcRenderer.invoke('shell:open-path', fullPath),
  },

  // App settings
  settings: {
    get: (key?: string) => ipcRenderer.invoke('settings:get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('settings:set', key, value),
    reset: () => ipcRenderer.invoke('settings:reset'),
  },

  // Project operations
  project: {
    create: (projectData: any) => ipcRenderer.invoke('project:create', projectData),
    save: (project: any, filePath?: string) => ipcRenderer.invoke('project:save', project, filePath),
    load: (filePath?: string) => ipcRenderer.invoke('project:load', filePath),
  },

  // Asset operations
  assets: {
    ingest: (paths: string[], projectId: string) => ipcRenderer.invoke('assets:ingest', paths, projectId),
    validate: (itemIds: string[]) => ipcRenderer.invoke('assets:validate', itemIds),
  },

  // Slot operations
  slots: {
    route: (items: any[], settings: any) => ipcRenderer.invoke('slots:route', items, settings),
    checkConflicts: (items: any[]) => ipcRenderer.invoke('slots:check-conflicts', items),
  },

  // Export operations
  export: {
    generate: (config: any, items: any[]) => ipcRenderer.invoke('export:generate', config, items),
  },

  // Window operations
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
  },

  // App operations
  app: {
    getVersion: () => ipcRenderer.invoke('app:get-version'),
    getPlatform: () => ipcRenderer.invoke('app:get-platform'),
    restart: () => ipcRenderer.invoke('app:restart'),
  },

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (_, ...args) => callback(...args));
  },

  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },

  once: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.once(channel, (_, ...args) => callback(...args));
  },
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Also expose it as a global type for TypeScript
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
