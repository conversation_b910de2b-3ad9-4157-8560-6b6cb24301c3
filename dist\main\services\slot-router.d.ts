/**
 * Slot Router Service - Handles conflict detection and slot remapping
 */
import { Item, SlotMapping, Gender, ReservedRange, ConflictResolution } from '../../types/core';
export declare class SlotRouterService {
    private slotMappings;
    private reservedRanges;
    /**
     * Route slots for items, resolving conflicts automatically
     */
    routeSlots(items: Item[], settings: {
        reservedRanges: ReservedRange[];
    }): Promise<{
        resolvedItems: Item[];
        conflicts: ConflictResolution[];
        newMappings: SlotMapping[];
    }>;
    /**
     * Check for conflicts without resolving them
     */
    checkConflicts(items: Item[]): Promise<ConflictResolution[]>;
    /**
     * Detect if an item has slot conflicts
     */
    private detectConflict;
    /**
     * Resolve a conflict by finding alternative slots
     */
    private resolveConflict;
    /**
     * Find the next available slot for an item
     */
    private findNextAvailableSlot;
    /**
     * Apply conflict resolution to an item
     */
    private applyResolution;
    /**
     * Check if a slot is reserved
     */
    private isSlotReserved;
    /**
     * Check if a specific slot is in a reserved range
     */
    private isSlotInReservedRange;
    /**
     * Create a slot mapping from an item
     */
    private createSlotMapping;
    /**
     * Get mapping key for an item
     */
    private getItemMappingKey;
    /**
     * Get mapping key for a slot mapping
     */
    private getMappingKey;
    /**
     * Load existing slot mappings
     */
    loadExistingMappings(mappings: SlotMapping[]): void;
    /**
     * Get all current slot mappings
     */
    getAllMappings(): SlotMapping[];
    /**
     * Get slot usage statistics
     */
    getSlotStatistics(): {
        totalSlots: number;
        usedSlots: number;
        reservedSlots: number;
        availableSlots: number;
        byGender: Record<Gender, {
            total: number;
            used: number;
            reserved: number;
        }>;
        bySlot: Record<string, {
            total: number;
            used: number;
            reserved: number;
        }>;
    };
    /**
     * Validate slot mappings for consistency
     */
    validateMappings(): {
        isValid: boolean;
        errors: string[];
        warnings: string[];
    };
}
