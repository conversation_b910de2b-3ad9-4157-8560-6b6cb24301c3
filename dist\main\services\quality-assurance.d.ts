/**
 * Quality Assurance Service - Handles asset validation and quality checks
 */
import { Item } from '../../types/core';
import { AssetValidationResult } from '../../types/assets';
import { ClippingDetectionResult } from '../../types/preview';
export declare class QualityAssuranceService {
    private tempDir;
    constructor(tempDir?: string);
    /**
     * Validate multiple assets
     */
    validateAssets(itemIds: string[]): Promise<{
        results: Map<string, AssetValidationResult>;
        summary: {
            totalItems: number;
            passed: number;
            failed: number;
            warnings: number;
        };
    }>;
    /**
     * Validate a single asset
     */
    private validateSingleAsset;
    /**
     * Perform clipping detection between items
     */
    detectClipping(items: Item[], mode?: 'fast' | 'deep'): Promise<ClippingDetectionResult>;
    /**
     * Check clipping within a group of items
     */
    private checkClippingInGroup;
    /**
     * Check clipping between two specific items
     */
    private checkClippingBetweenItems;
    /**
     * Fast clipping check using bounding boxes
     */
    private fastClippingCheck;
    /**
     * Deep clipping check using mesh intersection
     */
    private deepClippingCheck;
    /**
     * Check if overlap between two slots is expected/acceptable
     */
    private isExpectedOverlap;
    /**
     * Calculate asset metrics
     */
    private calculateAssetMetrics;
    /**
     * Get default metrics for error cases
     */
    private getDefaultMetrics;
    /**
     * Create a mock item for testing
     */
    private createMockItem;
    /**
     * Get mock bounding box for an item
     */
    private getMockBoundingBox;
    /**
     * Calculate intersection volume between two bounding boxes
     */
    private calculateBoundingBoxIntersection;
}
