/**
 * Quality Assurance Service - Handles asset validation and quality checks
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import { Item, QualityAssessment } from '@/types/core';
import { AssetValidationResult, ValidationError, ValidationWarning, AssetMetrics } from '@/types/assets';
import { ClippingDetectionResult, ClippingPair } from '@/types/preview';

export class QualityAssuranceService {
  private tempDir: string;

  constructor(tempDir?: string) {
    this.tempDir = tempDir || path.join(require('os').tmpdir(), 'dripforge-pro', 'qa');
  }

  /**
   * Validate multiple assets
   */
  async validateAssets(itemIds: string[]): Promise<{
    results: Map<string, AssetValidationResult>;
    summary: {
      totalItems: number;
      passed: number;
      failed: number;
      warnings: number;
    };
  }> {
    const results = new Map<string, AssetValidationResult>();
    let passed = 0;
    let failed = 0;
    let totalWarnings = 0;

    for (const itemId of itemIds) {
      try {
        // In a real implementation, we'd load the item from storage
        const mockItem = this.createMockItem(itemId);
        const result = await this.validateSingleAsset(mockItem);
        
        results.set(itemId, result);
        
        if (result.isValid) {
          passed++;
        } else {
          failed++;
        }
        
        totalWarnings += result.warnings.length;
      } catch (error) {
        const errorResult: AssetValidationResult = {
          isValid: false,
          errors: [{
            type: 'missing_file',
            message: `Failed to validate asset: ${error.message}`,
            severity: 'critical'
          }],
          warnings: [],
          metrics: this.getDefaultMetrics()
        };
        
        results.set(itemId, errorResult);
        failed++;
      }
    }

    return {
      results,
      summary: {
        totalItems: itemIds.length,
        passed,
        failed,
        warnings: totalWarnings
      }
    };
  }

  /**
   * Validate a single asset
   */
  private async validateSingleAsset(item: Item): Promise<AssetValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const metrics = await this.calculateAssetMetrics(item);

    // File existence checks
    if (item.files.ydd && !await fs.pathExists(item.files.ydd)) {
      errors.push({
        type: 'missing_file',
        message: 'YDD file not found',
        file: item.files.ydd,
        severity: 'critical'
      });
    }

    for (const ytdPath of item.files.ytds) {
      if (!await fs.pathExists(ytdPath)) {
        errors.push({
          type: 'missing_file',
          message: 'YTD file not found',
          file: ytdPath,
          severity: 'critical'
        });
      }
    }

    // File size checks
    if (item.files.size > 50 * 1024 * 1024) { // 50MB limit
      warnings.push({
        type: 'performance',
        message: 'Asset file size is very large (>50MB)',
        suggestion: 'Consider optimizing textures or reducing mesh complexity'
      });
    }

    // Triangle count check
    if (metrics.triangleCount > 50000) {
      warnings.push({
        type: 'performance',
        message: `High triangle count: ${metrics.triangleCount}`,
        suggestion: 'Consider creating LOD models or reducing mesh complexity'
      });
    }

    // LOD coverage check
    if (metrics.lodCoverage < 0.5) {
      warnings.push({
        type: 'quality',
        message: 'Low LOD coverage detected',
        suggestion: 'Generate additional LOD levels for better performance'
      });
    }

    // Texture memory check
    if (metrics.textureMemory > 100 * 1024 * 1024) { // 100MB
      warnings.push({
        type: 'performance',
        message: `High texture memory usage: ${Math.round(metrics.textureMemory / 1024 / 1024)}MB`,
        suggestion: 'Consider compressing textures or reducing resolution'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      metrics
    };
  }

  /**
   * Perform clipping detection between items
   */
  async detectClipping(items: Item[], mode: 'fast' | 'deep' = 'fast'): Promise<ClippingDetectionResult> {
    const clippingPairs: ClippingPair[] = [];
    let totalIntersectionVolume = 0;

    // Group items by gender for more efficient checking
    const maleItems = items.filter(item => item.gender === 'male');
    const femaleItems = items.filter(item => item.gender === 'female');

    // Check clipping within each gender group
    await this.checkClippingInGroup(maleItems, clippingPairs, mode);
    await this.checkClippingInGroup(femaleItems, clippingPairs, mode);

    // Calculate total intersection volume
    totalIntersectionVolume = clippingPairs.reduce((sum, pair) => sum + pair.intersectionVolume, 0);

    // Determine severity
    let severity: 'none' | 'minor' | 'moderate' | 'severe' = 'none';
    let score = 0;

    if (clippingPairs.length > 0) {
      const avgIntersection = totalIntersectionVolume / clippingPairs.length;
      
      if (avgIntersection < 0.01) {
        severity = 'minor';
        score = 25;
      } else if (avgIntersection < 0.05) {
        severity = 'moderate';
        score = 50;
      } else {
        severity = 'severe';
        score = 75;
      }

      // Increase score based on number of clipping pairs
      score += Math.min(clippingPairs.length * 5, 25);
    }

    return {
      hasClipping: clippingPairs.length > 0,
      clippingPairs,
      severity,
      score: Math.min(score, 100)
    };
  }

  /**
   * Check clipping within a group of items
   */
  private async checkClippingInGroup(items: Item[], clippingPairs: ClippingPair[], mode: 'fast' | 'deep'): Promise<void> {
    for (let i = 0; i < items.length; i++) {
      for (let j = i + 1; j < items.length; j++) {
        const item1 = items[i];
        const item2 = items[j];

        // Skip if items are the same slot (they would naturally overlap)
        if (item1.slot === item2.slot) continue;

        // Skip certain slot combinations that are expected to overlap
        if (this.isExpectedOverlap(item1.slot, item2.slot)) continue;

        const clipping = await this.checkClippingBetweenItems(item1, item2, mode);
        if (clipping) {
          clippingPairs.push(clipping);
        }
      }
    }
  }

  /**
   * Check clipping between two specific items
   */
  private async checkClippingBetweenItems(item1: Item, item2: Item, mode: 'fast' | 'deep'): Promise<ClippingPair | null> {
    if (mode === 'fast') {
      return this.fastClippingCheck(item1, item2);
    } else {
      return this.deepClippingCheck(item1, item2);
    }
  }

  /**
   * Fast clipping check using bounding boxes
   */
  private async fastClippingCheck(item1: Item, item2: Item): Promise<ClippingPair | null> {
    // Simplified bounding box intersection check
    // In a real implementation, this would load and analyze the actual mesh data
    
    const bbox1 = this.getMockBoundingBox(item1);
    const bbox2 = this.getMockBoundingBox(item2);

    const intersection = this.calculateBoundingBoxIntersection(bbox1, bbox2);
    
    if (intersection > 0) {
      return {
        model1: item1.id,
        model2: item2.id,
        intersectionVolume: intersection,
        intersectionPoints: [], // Would be calculated from actual mesh data
        severity: intersection > 0.05 ? 'severe' : intersection > 0.01 ? 'moderate' : 'minor'
      };
    }

    return null;
  }

  /**
   * Deep clipping check using mesh intersection
   */
  private async deepClippingCheck(item1: Item, item2: Item): Promise<ClippingPair | null> {
    // This would use Blender or another 3D library for precise mesh intersection
    // For now, return a mock result
    return this.fastClippingCheck(item1, item2);
  }

  /**
   * Check if overlap between two slots is expected/acceptable
   */
  private isExpectedOverlap(slot1: string, slot2: string): boolean {
    const expectedOverlaps = [
      ['undershirt', 'torso'],
      ['undershirt', 'tops'],
      ['torso', 'tops'],
      ['legs', 'feet'], // Some shoes extend up the leg
    ];

    return expectedOverlaps.some(pair => 
      (pair[0] === slot1 && pair[1] === slot2) ||
      (pair[0] === slot2 && pair[1] === slot1)
    );
  }

  /**
   * Calculate asset metrics
   */
  private async calculateAssetMetrics(item: Item): Promise<AssetMetrics> {
    // In a real implementation, this would analyze the actual YDD/YTD files
    return {
      totalFiles: 1 + item.files.ytds.length,
      totalSize: item.files.size,
      triangleCount: Math.floor(Math.random() * 30000) + 5000, // Mock data
      textureMemory: Math.floor(Math.random() * 50 * 1024 * 1024), // Mock data
      lodCoverage: Math.random(), // Mock data
      averageQuality: 0.8 + Math.random() * 0.2 // Mock data
    };
  }

  /**
   * Get default metrics for error cases
   */
  private getDefaultMetrics(): AssetMetrics {
    return {
      totalFiles: 0,
      totalSize: 0,
      triangleCount: 0,
      textureMemory: 0,
      lodCoverage: 0,
      averageQuality: 0
    };
  }

  /**
   * Create a mock item for testing
   */
  private createMockItem(itemId: string): Item {
    return {
      id: itemId,
      gender: 'male',
      kind: 'component',
      slot: 'torso',
      drawable: 1,
      textures: [0],
      files: {
        ydd: `/mock/path/${itemId}.ydd`,
        ytds: [`/mock/path/${itemId}.ytd`],
        originalPath: `/mock/path/${itemId}`,
        size: Math.floor(Math.random() * 10 * 1024 * 1024),
        checksum: 'mock-checksum'
      },
      metadata: {
        imported: new Date(),
        modified: new Date(),
        source: 'mock',
        version: '1.0.0'
      }
    };
  }

  /**
   * Get mock bounding box for an item
   */
  private getMockBoundingBox(item: Item): { min: [number, number, number]; max: [number, number, number] } {
    // Mock bounding box based on slot type
    const slotBoxes: Record<string, { min: [number, number, number]; max: [number, number, number] }> = {
      torso: { min: [-0.5, 0.5, -0.3], max: [0.5, 1.5, 0.3] },
      legs: { min: [-0.4, -0.5, -0.3], max: [0.4, 0.8, 0.3] },
      feet: { min: [-0.2, -0.8, -0.4], max: [0.2, -0.3, 0.2] },
      // Add more slot-specific bounding boxes
    };

    return slotBoxes[item.slot] || { min: [-0.3, -0.3, -0.3], max: [0.3, 0.3, 0.3] };
  }

  /**
   * Calculate intersection volume between two bounding boxes
   */
  private calculateBoundingBoxIntersection(
    bbox1: { min: [number, number, number]; max: [number, number, number] },
    bbox2: { min: [number, number, number]; max: [number, number, number] }
  ): number {
    const xOverlap = Math.max(0, Math.min(bbox1.max[0], bbox2.max[0]) - Math.max(bbox1.min[0], bbox2.min[0]));
    const yOverlap = Math.max(0, Math.min(bbox1.max[1], bbox2.max[1]) - Math.max(bbox1.min[1], bbox2.min[1]));
    const zOverlap = Math.max(0, Math.min(bbox1.max[2], bbox2.max[2]) - Math.max(bbox1.min[2], bbox2.min[2]));

    return xOverlap * yOverlap * zOverlap;
  }
}
