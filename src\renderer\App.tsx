/**
 * Main React application component
 */

import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Routes, Route } from 'react-router-dom';
import { useProjectStore } from '../stores/projectStore';
import { useUIStore } from '../stores/uiStore';
import { TitleBar } from './components/TitleBar';
import { Sidebar, MainContent, StatusBar, PreferencesModal, AboutModal, ProgressModal, ConsolePanel, MenuHandler } from './components/index';
import { WelcomeScreen } from './components/WelcomeScreen';

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
`;

const MainLayout = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
`;

const WorkspaceArea = styled.div<{ showConsole: boolean }>`
  display: flex;
  flex: 1;
  overflow: hidden;
  ${props => props.showConsole && `
    flex-direction: column;
  `}
`;

const MainWorkspace = styled.div<{ showConsole: boolean }>`
  display: flex;
  flex: 1;
  overflow: hidden;
  ${props => props.showConsole && `
    flex: 0.7;
  `}
`;

export const App: React.FC = () => {
  const { currentProject, loadProject } = useProjectStore();
  const { 
    showPreferences, 
    showAbout, 
    showProgress, 
    showConsole,
    setShowPreferences,
    setShowAbout 
  } = useUIStore();
  
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize the application
    const initialize = async () => {
      try {
        // Load app settings
        const settings = await window.electronAPI.settings.get();
        console.log('App settings loaded:', settings);

        // Check for command line arguments (project file to open)
        // This would be implemented when needed

        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setIsInitialized(true); // Still show the app even if initialization fails
      }
    };

    initialize();
  }, [loadProject]);

  if (!isInitialized) {
    return (
      <AppContainer>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          height: '100vh',
          flexDirection: 'column',
          gap: '20px'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '3px solid #333',
            borderTop: '3px solid #007acc',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <div>Initializing DripForge Pro...</div>
        </div>
      </AppContainer>
    );
  }

  return (
    <AppContainer>
      <MenuHandler />
      
      {process.platform !== 'darwin' && <TitleBar />}
      
      <MainLayout>
        {currentProject ? (
          <>
            <Sidebar />
            <ContentArea>
              <WorkspaceArea showConsole={showConsole}>
                <MainWorkspace showConsole={showConsole}>
                  <Routes>
                    <Route path="/" element={<MainContent />} />
                    <Route path="/preview" element={<MainContent activeTab="preview" />} />
                    <Route path="/qa" element={<MainContent activeTab="qa" />} />
                    <Route path="/export" element={<MainContent activeTab="export" />} />
                  </Routes>
                </MainWorkspace>
                {showConsole && <ConsolePanel />}
              </WorkspaceArea>
              <StatusBar />
            </ContentArea>
          </>
        ) : (
          <WelcomeScreen />
        )}
      </MainLayout>

      {/* Modals */}
      {showPreferences && (
        <PreferencesModal 
          isOpen={showPreferences}
          onClose={() => setShowPreferences(false)}
        />
      )}
      
      {showAbout && (
        <AboutModal 
          isOpen={showAbout}
          onClose={() => setShowAbout(false)}
        />
      )}
      
      {showProgress && <ProgressModal />}
    </AppContainer>
  );
};
